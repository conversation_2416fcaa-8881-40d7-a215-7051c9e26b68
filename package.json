{"name": "view-ui-project-ts", "private": true, "version": "1.0.0", "description": "a template project for vue3, ViewUIPlus, TypeScript and Vite.", "scripts": {"preinstall": "npx only-allow pnpm", "serve": "pnpm dev", "dev": "vite serve", "dev:staging": "vite serve --mode=staging", "dev:prod": "vite serve --mode=production", "build": "vite build && node cos/index.js production", "build-test": "vite build --mode=test && node cos/index.js test", "build:sdk": "pnpm -C packages/core build", "build:staging": "vite build --mode=staging", "preview": "pnpm build && vite preview", "preview:staging": "pnpm build:staging && vite preview --mode=staging", "prepare": "husky install", "test": "pnpm -C packages/core test"}, "dependencies": {"@imengyu/vue3-context-menu": "^1.4.2", "@kuaitu/core": "workspace:^", "@vueuse/core": "^10.11.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.3.4", "clipboard": "^2.0.11", "cos-js-sdk-v5": "^1.8.4", "cos-nodejs-sdk-v5": "^2.14.6", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "events": "^3.3.0", "fabric": "^5.3.0", "fontfaceobserver": "^2.1.0", "glob": "^11.0.0", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "md5": "^2.3.0", "mitt": "^3.0.1", "number-precision": "^1.6.0", "qs": "^6.12.1", "uuid": "^8.3.2", "view-ui-plus": "^1.3.7", "vue": "^3.2.25", "vue-cropper": "^1.1.4", "vue-draggable-plus": "^0.6.0", "vue-i18n": "9.0.0", "vue-masonry": "^0.16.0", "vue-router": "^4.0.16", "vue-select": "4.0.0-beta.6", "vue-virtual-scroller": "2.0.0-beta.8", "vue3-colorpicker": "^2.3.0", "vue3-cropper": "^0.4.0", "vue3-lazyload": "^0.3.6"}, "workspaces": ["packages/*"], "devDependencies": {"@commitlint/cli": "^17.4.2", "@commitlint/config-conventional": "^17.4.2", "@types/events": "^3.0.0", "@types/fabric": "^5.3.0", "@types/fontfaceobserver": "^2.1.3", "@types/lodash-es": "^4.17.7", "@types/node": "^18.15.13", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^5.54.1", "@typescript-eslint/parser": "^5.54.1", "@vitejs/plugin-vue": "^4.1.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "autoprefixer": "^10.4.16", "eslint": "^7.32.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.9.0", "husky": "^8.0.0", "less": "^4.1.3", "less-loader": "^11.1.0", "lint-staged": "^13.1.1", "prettier": "2.8.4", "typescript": "5.1.6", "unplugin-auto-import": "^0.16.0", "vite": "^4.2.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.0", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-tsc": "^0.34.7"}, "lint-staged": {"*.{ts,tsx,js,vue}": ["eslint --fix", "prettier --write"]}}