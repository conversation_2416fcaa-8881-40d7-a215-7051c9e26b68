import Editor from './Editor';
type IEditor = Editor;

class FontPlugin {
  public canvas: fabric.Canvas;
  public editor: IEditor;
  static pluginName = 'FontPlugin';
  static apis = ['downFontByJSON'];
  static events = ['textEvent1', 'textEvent2'];
  public hotkeys: string[] = ['backspace', 'space', 'delete'];
  repoSrc: string;

  constructor(canvas: fabric.Canvas, editor: IEditor, config: { repoSrc: string }) {
    this.canvas = canvas;
    this.editor = editor;
    this.repoSrc = config.repoSrc;
  }

  hookImportBefore(json: string) {
    return this.downFontByJSON(json);
  }

  downFontByJSON() {
  }

  _createFontCSS() {
    const params = [];
    this.editor.emit('textEvent1', params);
  }

  contextMenu() {
    const selectedMode = this.editor.getSelectMode();
    if (selectedMode === SelectMode.ONE) {
      return [
        null, // 分割线
        {
          text: '翻转',
          hotkey: '❯',
          subitems: [
            {
              text: t('flip.x'),
              hotkey: '|',
              onclick: () => this.flip('X'),
            },
            {
              text: t('flip.y'),
              hotkey: '-',
              onclick: () => this.flip('Y'),
            },
          ],
        }
      ];
    }
  }

  hotkeyEvent(eventName: string, { type }: KeyboardEvent) {
    if ((eventName === 'backspace' || eventName === 'delete') && type === 'keydown') {
      this.del();
    }
  }

  destroy() {
    console.log('pluginDestroy');
  }
}

export default FontPlugin;
