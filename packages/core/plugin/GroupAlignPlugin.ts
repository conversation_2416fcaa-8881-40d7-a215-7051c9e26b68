import { fabric } from 'fabric';
import Editor from '../Editor';
type IEditor = Editor;

class GroupAlignPlugin implements IPluginTempl {
  static pluginName = 'GroupAlignPlugin';
  static apis = ['left', 'right', 'xcenter', 'ycenter', 'top', 'bottom', 'xequation', 'yequation'];
  constructor(public canvas: fabric.Canvas, public editor: IEditor) {}

  left() {
    const { canvas } = this;

    const activeObject = canvas.getActiveObject();
    const selectObjects = canvas.getActiveObjects();
    if (activeObject) {
      const { left = 0 } = activeObject;
      canvas.discardActiveObject();
      selectObjects.forEach((item) => {
        const bounding = item.getBoundingRect(true);
        item.set({
          left: left - bounding.left + Number(item.left),
        });
        item.setCoords();
      });
      const activeSelection = new fabric.ActiveSelection(selectObjects, {
        canvas: canvas,
      });
      canvas.setActiveObject(activeSelection);
      canvas.requestRenderAll();
    }
  }

  right() {
    const { canvas } = this;

    const activeObject = canvas.getActiveObject();
    const selectObjects = canvas.getActiveObjects();
    if (activeObject) {
      const { left = 0, width = 0 } = activeObject;
      canvas.discardActiveObject();
      selectObjects.forEach((item) => {
        const bounding = item.getBoundingRect(true);
        item.set({
          left: left + width - (bounding.left + bounding.width) + Number(item.left),
        });
      });
      const activeSelection = new fabric.ActiveSelection(selectObjects, {
        canvas: canvas,
      });
      canvas.setActiveObject(activeSelection);
      canvas.requestRenderAll();
    }
  }

  xcenter() {
    const { canvas } = this;
    const activeObject = canvas.getActiveObject();
    const selectObjects = canvas.getActiveObjects();
    if (activeObject) {
      const { left = 0, width = 0 } = activeObject;
      canvas.discardActiveObject();
      selectObjects.forEach((item) => {
        const bounding = item.getBoundingRect(true);
        item.set({
          left: left + width / 2 - (bounding.left + bounding.width / 2) + Number(item.left),
        });
      });
      const activeSelection = new fabric.ActiveSelection(selectObjects, {
        canvas: canvas,
      });
      canvas.setActiveObject(activeSelection);
      canvas.requestRenderAll();
    }
  }

  ycenter() {
    const { canvas } = this;

    const activeObject = canvas.getActiveObject();
    const selectObjects = canvas.getActiveObjects();
    if (activeObject) {
      const { top = 0, height = 0 } = activeObject;
      canvas.discardActiveObject();
      selectObjects.forEach((item) => {
        const bounding = item.getBoundingRect(true);
        item.set({
          top: top + height / 2 - (bounding.top + bounding.height / 2) + Number(item.top),
        });
      });
      const activeSelection = new fabric.ActiveSelection(selectObjects, {
        canvas: canvas,
      });
      canvas.setActiveObject(activeSelection);
      canvas.requestRenderAll();
    }
  }

  top() {
    const { canvas } = this;

    const activeObject = canvas.getActiveObject();
    const selectObjects = canvas.getActiveObjects();
    if (activeObject) {
      const { top = 0 } = activeObject;
      canvas.discardActiveObject();
      selectObjects.forEach((item) => {
        const bounding = item.getBoundingRect(true);
        item.set({
          top: top - bounding.top + Number(item.top),
        });
      });
      const activeSelection = new fabric.ActiveSelection(selectObjects, {
        canvas: canvas,
      });
      canvas.setActiveObject(activeSelection);
      canvas.requestRenderAll();
    }
  }

  bottom() {
    const { canvas } = this;

    const activeObject = canvas.getActiveObject();
    const selectObjects = canvas.getActiveObjects();
    if (activeObject) {
      const { top = 0, height = 0 } = activeObject;
      canvas.discardActiveObject();
      selectObjects.forEach((item) => {
        const bounding = item.getBoundingRect(true);
        item.set({
          top: top + height - (bounding.top + bounding.height) + Number(item.top),
        });
      });
      const activeSelection = new fabric.ActiveSelection(selectObjects, {
        canvas: canvas,
      });
      canvas.setActiveObject(activeSelection);
      canvas.requestRenderAll();
    }
  }

  xequation() {
    const { canvas } = this;
    const activeObject = canvas.getActiveObject();
    function getItemWidth(item) {
      let x1 = Infinity,
        x2 = -Infinity;
      for (const key in item.aCoords) {
        if (item.aCoords[key].x < x1) {
          x1 = item.aCoords[key].x;
        }
        if (item.aCoords[key].x > x2) {
          x2 = item.aCoords[key].x;
        }
      }
      return x2 - x1;
    }

    function getAllItemHeight() {
      let count = 0;
      if (activeObject) {
        activeObject.forEachObject((item) => {
          count += getItemWidth(item);
        });
      }

      return count;
    }
    function spacWidth() {
      const count = getAllItemHeight();
      if (activeObject) {
        const allSpac = Number(activeObject.width) - count;
        return allSpac / (activeObject._objects.length - 1);
      }
    }

    function getItemLeft(i) {
      if (i === 0) return 0;
      let width = 0;
      if (activeObject) {
        for (let index = 0; index < i; index++) {
          width += getItemWidth(activeObject._objects[index]);
        }
      }

      return width;
    }
    if (activeObject && activeObject.type === 'activeSelection') {
      const activeSelection = activeObject;
      activeSelection._objects.sort((a, b) => a.left - b.left);

      const itemSpac = spacWidth();
      const yHeight = Number(activeObject.width) / 2;

      activeObject.forEachObject((item, i) => {
        const preHeight = getItemLeft(i);
        const top = itemSpac * i + preHeight - yHeight;
        item.set('left', top);
      });
    }

    const objecs = canvas.getActiveObjects();
    canvas.discardActiveObject();
    objecs.forEach((item) => {
      let x = Infinity;
      for (const key in item.aCoords) {
        if (item.aCoords[key].x < x) {
          x = item.aCoords[key].x;
        }
      }
      item.set('left', 2 * item.left - x);
    });

    const sel = new fabric.ActiveSelection(objecs, {
      canvas: canvas,
    });
    canvas.setActiveObject(sel);
    canvas.requestRenderAll();
  }

  yequation() {
    const { canvas } = this;
    const activeObject = canvas.getActiveObject() || { top: 0, height: 0 };
    function getItemHeight(item) {
      let y1 = Infinity,
        y2 = -Infinity;
      for (const key in item.aCoords) {
        if (item.aCoords[key].y < y1) {
          y1 = item.aCoords[key].y;
        }
        if (item.aCoords[key].y > y2) {
          y2 = item.aCoords[key].y;
        }
      }
      return y2 - y1;
    }
    function getAllItemHeight() {
      let count = 0;
      activeObject.forEachObject((item) => {
        count += getItemHeight(item);
      });
      return count;
    }
    function spacHeight() {
      const count = getAllItemHeight();
      const allSpac = activeObject.height - count;
      return allSpac / (activeObject._objects.length - 1);
    }

    function getItemTop(i) {
      if (i === 0) return 0;
      let height = 0;
      for (let index = 0; index < i; index++) {
        height += getItemHeight(activeObject._objects[index]);
      }
      return height;
    }

    if (activeObject && activeObject.type === 'activeSelection') {
      const activeSelection = activeObject;
      activeSelection._objects.sort((a, b) => a.top - b.top);

      const itemSpac = spacHeight();
      const yHeight = Number(activeObject.height) / 2;

      activeObject.forEachObject((item: fabric.Object, i: number) => {
        const preHeight = getItemTop(i);
        const top = itemSpac * i + preHeight - yHeight;
        item.set('top', top);
      });
    }

    const objecs = canvas.getActiveObjects();
    canvas.discardActiveObject();
    objecs.forEach((item) => {
      let y = Infinity;
      for (const key in item.aCoords) {
        if (item.aCoords[key].y < y) {
          y = item.aCoords[key].y;
        }
      }
      item.set('top', 2 * item.top - y);
    });

    const sel = new fabric.ActiveSelection(objecs, {
      canvas: canvas,
    });
    canvas.setActiveObject(sel);
    canvas.requestRenderAll();
  }

  destroy() {
    console.log('pluginDestroy');
  }
}

export default GroupAlignPlugin;
