import { fabric } from 'fabric';
import Editor from '../Editor';
type IEditor = Editor;

class DeleteHotKeyPlugin implements IPluginTempl {
  static pluginName = 'DeleteHotKeyPlugin';
  static apis = ['del'];
  hotkeys: string[] = ['backspace', 'delete'];
  constructor(public canvas: fabric.Canvas, public editor: IEditor) {}

  hotkeyEvent(eventName: string, e: KeyboardEvent) {
    if (e.type === 'keydown' && (eventName === 'backspace' || eventName === 'delete')) {
      this.del();
    }
  }

  del() {
    const { canvas } = this;
    const activeObject = canvas.getActiveObjects();
    if (activeObject) {
      activeObject.map((item) => canvas.remove(item));
      canvas.requestRenderAll();
      canvas.discardActiveObject();
    }
  }

  contextMenu() {
    const activeObject = this.canvas.getActiveObject();
    if (activeObject) {
      return [null, { text: '删除', hotkey: '', disabled: false, onclick: () => this.del() }];
    }
  }

  destroy() {
    console.log('pluginDestroy');
  }
}

export default DeleteHotKeyPlugin;
