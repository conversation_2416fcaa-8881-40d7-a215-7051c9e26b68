import { fabric } from 'fabric';
import Editor from '../Editor';
type IEditor = Editor;

export default class EmitPlugin implements IPluginTempl {
  static pluginName = 'EmitPlugin';
  static events = ['refreshFileList', 'generateEvent', 'refreshFontList'];
  static apis = ['emitRefreshFileList', 'emitGenerate', 'emitRefreshFontList'];
  constructor(public canvas: fabric.Canvas, public editor: IEditor) {}
  emitRefreshFileList() {
    this.editor.emit('refreshFileList');
  }
  emitGenerate(status: boolean) {
    this.editor.emit('generateEvent', status);
  }
  emitRefreshFontList() {
    this.editor.emit('refreshFontList');
  }
}

