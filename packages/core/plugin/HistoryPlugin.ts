import { Canvas } from 'fabric';
import Editor from '../Editor';
type IEditor = Editor;

type HistoryEventCallback = () => void;
class HistoryPlugin {
  private canvas: Canvas;
  private editor: IEditor;
  private historyUndo: string[];
  private historyRedo: string[];
  private extraProps: string[];
  private historyNextState: string;
  private historyProcessing: boolean;

  static pluginName = 'HistoryPlugin';
  static apis = ['undo', 'redo', 'clearHistory'];
  static events = ['historyUpdate'];
  public hotkeys: string[] = ['ctrl+z', 'ctrl+shift+z', '⌘+z', '⌘+shift+z'];

  constructor(canvas: Canvas, editor: IEditor) {
    this.canvas = canvas;
    this.editor = editor;
    this.historyUndo = [];
    this.historyRedo = [];
    this.extraProps = ['selectable', 'hasControls', 'left', 'top', 'id'];
    this.historyNextState = this._historyNext();
    this.historyProcessing = false;

    this._historyInit();
  }

  private _historyNext(): string {
    return JSON.stringify(this.canvas.toDatalessJSON(this.extraProps));
  }

  private _historyEvents() {
    return {
      'object:added': this._historySaveAction.bind(this),
      'object:removed': this._historySaveAction.bind(this),
      'object:modified': this._historySaveAction.bind(this),
      'object:skewing': this._historySaveAction.bind(this),
    };
  }

  private _historyInit() {
    this.canvas.on(this._historyEvents());

    window.addEventListener('beforeunload', (e) => {
      if (this.historyUndo.length > 0 || this.historyRedo.length > 0) {
        (e || window.event).returnValue = '确认离开';
      }
    });
  }

  private _historyDispose() {
    this.canvas.off(this._historyEvents());
  }

  private _historySaveAction() {
    if (this.historyProcessing) return;

    this.canvas.once('after:render', () => {
      const isBatch = document.querySelector('.c-batch-comp')
      if (isBatch) return
      const json = this.historyNextState;
      this.historyUndo.push(json);
      this.historyNextState = this._historyNext();
      this.canvas.fire('history:append', {json: json});
      this.historyUpdate();
    })
  }

  historyUpdate() {
    this.editor.emit('historyUpdate', this.historyUndo.length, this.historyRedo.length);
  }

  undo(callback?: HistoryEventCallback) {
    this.historyProcessing = true;

    const history = this.historyUndo.pop();
    if (history) {
      this.historyRedo.push(this._historyNext());
      this.historyNextState = history;
      this._loadHistory(history, 'history:undo', callback);
    } else {
      this.historyProcessing = false;
    }
    this.historyUpdate();
  }

  redo(callback?: HistoryEventCallback) {
    this.historyProcessing = true;

    const history = this.historyRedo.pop();
    if (history) {
      this.historyUndo.push(this._historyNext());
      this.historyNextState = history;
      this._loadHistory(history, 'history:redo', callback);
    } else {
      this.historyProcessing = false;
    }
    this.historyUpdate();
  }

  private _loadHistory(history: string, event: string, callback?: HistoryEventCallback) {
    const that = this;
    this.canvas.loadFromJSON(history, () => {
      that.canvas.renderAll();
      that.canvas.fire(event);
      that.historyProcessing = false;

      if (callback) callback();
    });
  }

  clearHistory() {
    this.historyUndo = [];
    this.historyRedo = [];
    this.canvas.fire('history:clear');
  }

  onHistory() {
    this.historyProcessing = false;
    this._historySaveAction();
  }

  canUndo(): boolean {
    return this.historyUndo.length > 0;
  }

  canRedo(): boolean {
    return this.historyRedo.length > 0;
  }

  offHistory() {
    this.historyProcessing = true;
  }

  // 快捷键扩展回调
  hotkeyEvent(eventName: string, e: any) {
    if (e.type === 'keydown') {
      switch (eventName) {
        case 'ctrl+z':
        case '⌘+z':
          this.undo();
          break;
        case 'ctrl+shift+z':
        case '⌘+shift+z':
          this.redo();
          break;
      }
    }
  }
}
export default HistoryPlugin;
