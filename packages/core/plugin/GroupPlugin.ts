import { fabric } from 'fabric';
import Editor from '../Editor';
import { isGroup, isActiveSelection } from '../utils/utils';
import { v4 as uuid } from 'uuid';
type IEditor = Editor;

class GroupPlugin implements IPluginTempl {
  static pluginName = 'GroupPlugin';
  static apis = ['unGroup', 'group'];
  constructor(public canvas: fabric.Canvas, public editor: IEditor) {}

  unGroup() {
    const activeObject = this.canvas.getActiveObject() as fabric.Group;
    if (!activeObject) return;
    const activeObjectList = activeObject.getObjects();
    activeObject.toActiveSelection();
    for (const item of activeObjectList) {
      item.set('id', uuid());
    }
    this.canvas.discardActiveObject().renderAll();
  }

  group() {
    const activeObj = this.canvas.getActiveObject() as fabric.ActiveSelection;
    if (!activeObj) return;
    const record: any = {}
    for (const object of this.canvas.getActiveObjects()) {
      const index = this.canvas.getObjects().indexOf(object)
      record[index] = object
    }
    activeObj._objects = Object.keys(record).sort((a: any, b: any) => a - b).map(key => record[key])
    const activegroup = activeObj.toGroup();
    const objectsInGroup = activegroup.getObjects();
    activegroup.clone((newgroup: fabric.Group) => {
      newgroup.set('id', uuid());
      this.canvas.remove(activegroup);
      objectsInGroup.forEach((object) => {
        this.canvas.remove(object);
      });
      this.canvas.add(newgroup);
      this.canvas.setActiveObject(newgroup);
    });
  }

  contextMenu() {
    const activeObject = this.canvas.getActiveObject();

    if (isActiveSelection(activeObject)) {
      return [{ text: '组合', hotkey: '', disabled: false, onclick: () => this.group() }];
    }

    if (isGroup(activeObject)) {
      return [
        { text: '拆分组合', hotkey: '', disabled: false, onclick: () => this.unGroup() },
      ];
    }
  }
  destroy() {
    console.log('pluginDestroy');
  }
}

export default GroupPlugin;
