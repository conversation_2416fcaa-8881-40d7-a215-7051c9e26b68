import { fabric } from 'fabric';
import Editor from '../Editor';
import { selectFiles } from '../utils/utils';
import psdToJson from '../utils/psd';
import Psd from '@webtoon/psd';
type IEditor = Editor;

class PsdPlugin implements IPluginTempl {
  static pluginName = 'PsdPlugin';
  static apis = ['insertPSD', 'insertPSDToCanvas'];
  constructor(public canvas: fabric.Canvas, public editor: IEditor) {}

  insertPSD() {
    return new Promise((resolve, reject) => {
      selectFiles({ accept: '.psd' })
        .then((files) => {
          if (files && files.length > 0) {
            const file = files[0];
            const reader = new FileReader();
            reader.readAsText(file, 'UTF-8');
            reader.onload = async () => {
              const result = await file.arrayBuffer();
              const psdFile = Psd.parse(result as ArrayBuffer);
              const json = await psdToJson(psdFile);
              this.loadJSON(json);
              resolve('');
            };
          }
        })
        .catch(reject);
    });
  }

  insertPSDToCanvas() {
    return new Promise((resolve, reject) => {
      selectFiles({ accept: '.psd' })
        .then((files) => {
          if (files && files.length > 0) {
            const file = files[0];
            const reader = new FileReader();
            reader.readAsText(file, 'UTF-8');
            reader.onload = async () => {
              const result = await file.arrayBuffer();
              const psdFile = Psd.parse(result as ArrayBuffer);
              const json = await psdToJson(psdFile);
              this.loadJSONToCanvas(json);
              resolve('');
            };
          }
        })
        .catch(reject);
    });
  }

  loadJSON(json: string) {
    this.editor.loadJSON(json);
  }

  loadJSONToCanvas(json: string) {
    this.canvas.add(JSON.parse(json))
  }
}

export default PsdPlugin;
