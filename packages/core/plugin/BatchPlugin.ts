import * as XLSX from 'xlsx/xlsx.mjs';

import { cloneDeep } from 'lodash-es';
import { fabric } from 'fabric';
import Editor from '../Editor';

type IEditor = Editor;

class BatchPlugin implements IPluginTempl {
  static pluginName = 'BatchPlugin';
  static apis = ['getExcelData', 'mreageData', 'getColumns'];

  columns: any[];
  constructor(public canvas: fabric.Canvas, public editor: IEditor) {
    this.columns = [];
  }

  getExcelData(file: File | Blob) {
    const that = this;
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = function () {
        const data = reader.result;
        const workbook = XLSX.read(arrayBufferToBinaryString(data), {
          type: 'binary',
        });

        const jsonData = XLSX.utils.sheet_to_json(workbook.Sheets[workbook.SheetNames[0]], {
          defval: 'null', //单元格为空时的默认值
        });

        const [item] = jsonData;
        const columns = Object.keys(item).map((key) => ({
          key: key,
          title: key,
          type: 'text',
          format: '${' + key + '}',
          imgMap: {},
        }));
        that._setColumns(columns);
        resolve({ jsonData, columns });
      };
      reader.readAsArrayBuffer(file);
    });
  }

  getColumns() {
    return this.columns;
  }

  _setColumns(columns: any[]) {
    this.columns = columns;
  }

  mreageData({ item, columnFormats, templ, fileNameFormat = '' }: any) {
    const copyItem = this._formatData(item, columnFormats);
    const copyTempl = cloneDeep(templ);
    const data = this._replaceData(copyTempl, copyItem);
    const fileName = this._replaceKey(fileNameFormat, copyItem) || item['模板名称'];
    return {
      data,
      fileName,
    };
  }

  _replaceData(templData: any, info: any) {
    if (templData.objects) {
      templData.objects = templData.objects.map((item: any) => {
        return this._replaceData(item, info);
      });
    } else {
      if (templData.linkData) {
        const [attr, dataAttr] = templData.linkData;
        if (info[dataAttr]) {
          const str = String(info[dataAttr]);
          templData[attr] = str === 'null' ? '' : str;
        }
      }
    }
    return templData;
  }

  _formatData(item: { [x: string]: string | number }, formats: any[]) {
    const copyItem = cloneDeep(item);
    formats.forEach((formatInfo) => {
      const { type, title: key, format, imgMap } = formatInfo;
      if (type === 'text') {
        copyItem[key] = this._replaceKey(format, copyItem);
      }
      if (type === 'img') {
        copyItem[key] = imgMap[copyItem[key]];
      }
    });

    return copyItem;
  }

  _replaceKey(str: string, item: any) {
    let replaceStr = str;
    Object.keys(item).forEach((key) => {
      replaceStr = replaceStr.replace('${' + key + '}', item[key]);
    });
    return replaceStr;
  }

  destroy() {
    console.log('pluginDestroy');
  }
}

function arrayBufferToBinaryString(data: any) {
  let o = '',
    l = 0;
  const w = 10240;
  for (; l < data.byteLength / w; ++l)
    o += String.fromCharCode.apply(null, new Uint8Array(data.slice(l * w, l * w + w)));
  o += String.fromCharCode.apply(null, new Uint8Array(data.slice(l * w)));
  return o;
}

export default BatchPlugin;
