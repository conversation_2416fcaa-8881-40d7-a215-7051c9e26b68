import { fabric } from 'fabric';

fabric.Canvas.prototype.initialize = (function (originalFn) {
  return function (...args) {
    originalFn.call(this, ...args);
    this._historyInit();
    return this;
  };
})(fabric.Canvas.prototype.initialize);

fabric.Canvas.prototype.dispose = (function (originalFn) {
  return function (...args) {
    originalFn.call(this, ...args);
    this._historyDispose();
    return this;
  };
})(fabric.Canvas.prototype.dispose);

fabric.Canvas.prototype._historyNext = function () {
  return JSON.stringify(this.toDatalessJSON(this.extraProps));
};

fabric.Canvas.prototype._historyEvents = function () {
  return {
    'object:added': (e) => this._historySaveAction(e),
    'object:removed': (e) => this._historySaveAction(e),
    'object:modified': (e) => this._historySaveAction(e),
    'object:skewing': (e) => this._historySaveAction(e),
  };
};

fabric.Canvas.prototype._historyInit = function () {
  this.historyUndo = [];
  this.historyRedo = [];
  this.extraProps = [
    'id',
    'gradientAngle',
    'selectable',
    'hasControls',
    'linkData',
    'editable',
    'extensionType',
    'extension',
  ];
  this.historyNextState = this._historyNext();

  this.on(this._historyEvents());
};

fabric.Canvas.prototype._historyDispose = function () {
  this.off(this._historyEvents());
};

fabric.Canvas.prototype._historySaveAction = function (e) {
  if (this.historyProcessing) return;
  if (!e || (e.target && !e.target.excludeFromExport)) {
    const json = this._historyNext();
    this.historyUndo.push(json);
    this.historyNextState = this._historyNext();
    this.fire('history:append', { json: json });
  }
};

fabric.Canvas.prototype.undo = function (callback) {
  this.historyProcessing = true;

  const history = this.historyUndo.pop();
  if (history) {
    this.historyRedo.push(this._historyNext());
    this.historyNextState = history;
    this._loadHistory(history, 'history:undo', callback);
  } else {
    console.log(1111);
    this.historyProcessing = false;
  }
};

fabric.Canvas.prototype.redo = function (callback) {
  this.historyProcessing = true;
  const history = this.historyRedo.pop();
  if (history) {
    this.historyUndo.push(this._historyNext());
    this.historyNextState = history;
    this._loadHistory(history, 'history:redo', callback);
  } else {
    this.historyProcessing = false;
  }
};

fabric.Canvas.prototype._loadHistory = function (history, event, callback) {
  var that = this;

  this.loadFromJSON(history, function () {
    that.renderAll();
    that.fire(event);
    that.historyProcessing = false;

    if (callback && typeof callback === 'function') callback();
  });
};

fabric.Canvas.prototype.clearHistory = function (type) {
  if (!type) {
    this.historyUndo = [];
    this.historyRedo = [];
    this.fire('history:clear');
  } else {
    const one = this.historyUndo.pop();
    this.historyUndo = [one];
    this.historyRedo = [];
    this.fire('history:clear');
  }
};

fabric.Canvas.prototype.clearUndo = function () {
  this.historyUndo = [];
};

fabric.Canvas.prototype.onHistory = function () {
  this.historyProcessing = false;

  this._historySaveAction();
};

fabric.Canvas.prototype.canUndo = function () {
  return this.historyUndo.length > 0;
};

fabric.Canvas.prototype.canRedo = function () {
  return this.historyRedo.length > 0;
};

fabric.Canvas.prototype.offHistory = function () {
  this.historyProcessing = true;
};