import { defineConfig } from 'vite';
import eslintPlugin from 'vite-plugin-eslint'; //导入包
import { resolve } from 'path';

const config = () => {
  return {
    base: './',
    build: {
      lib: {
        entry: resolve(__dirname, './index.ts'),
        name: '<PERSON>ait<PERSON>',
        fileName: 'index',
      },
      outDir: resolve(__dirname, '../../dist'),
    },
    plugins: [
      eslintPlugin({
        include: ['src/**/*.js', 'src/**/*.vue', 'src/*.js', 'src/*.vue'],
      }),
    ],
  };
};

export default defineConfig(config);
