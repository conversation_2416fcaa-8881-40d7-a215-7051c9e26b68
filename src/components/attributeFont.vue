<template>
  <div
    class="box attr-item-box"
    v-if="mixinState.mSelectMode === 'one' && textType.includes(mixinState.mSelectOneType)"
  >
    <div>
      <div class="flex-view">
        <div class="flex-item">
          <div class="left font-selector" style="position: relative">
            <VirtualFontSelect
              v-if="!isFontLoading"
              v-model="baseAttr.fontFamily"
              :options="safeFontsList"
              placeholder="选择字体"
              @change="changeFontFamily"
              @search="onSearch"
              @open="handleOpen"
              class="font-select"
            />
            <Spin v-else size="small" style="margin-top: 5px" :show="isFontLoading">加载字体中...</Spin>
            <Icon v-if="!isFontLoading" type="md-copy" :data-clipboard-text="copyText" class="copy-text" />
          </div>
          <div class="right">
            <InputNumber
              v-model="baseAttr.fontSize"
              @on-change="(value) => changeCommon('fontSize', value)"
              append="字号"
              :min="1"
            ></InputNumber>
          </div>
        </div>
      </div>

      <div class="flex-view" v-if="noTCurvedText">
        <div class="flex-item">
          <RadioGroup
            class="button-group"
            v-model="baseAttr.textAlign"
            @on-change="(value) => changeCommon('textAlign', value)"
            type="button"
          >
            <Radio v-for="(item, i) in textAlignList" :label="item" :key="item">
              <span v-html="textAlignListSvg[i]"></span>
            </Radio>
          </RadioGroup>
        </div>
      </div>

      <div class="flex-view">
      </div>

      <div class="flex-view">
        <div class="flex-item">
          <ButtonGroup class="button-group">
            <Button @click="handleChangeLetterCase">
              <svg viewBox="0 0 1194 1024" width="14" height="14">
                <path
                  d="M759.443271 0h170.656712v281.580587h-170.656712v-119.465671H546.137316v691.228373h128.007466v170.656711h-418.129849v-162.114916h119.465671V162.114916H170.656711v119.465671H0V0z m341.313423 426.671644H1194.656711V597.328356h-93.840284V529.053725h-119.465672v401.046258h68.274631v93.840284h-238.931342v-93.840284h68.274631V529.053725h-128.067199V597.328356H657.061191V426.671644z"
                  :fill="baseAttr.letterCase ? '#305ef4' : '#666'"
                ></path>
              </svg>
            </Button>
            <Button @click="changeFontStyle('fontStyle', baseAttr.fontStyle)">
              <svg viewBox="0 0 1024 1024" width="14" height="14">
                <path
                  d="M832 96v64a32 32 0 0 1-32 32h-125.52l-160 640H608a32 32 0 0 1 32 32v64a32 32 0 0 1-32 32H224a32 32 0 0 1-32-32v-64a32 32 0 0 1 32-32h125.52l160-640H416a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32h384a32 32 0 0 1 32 32z"
                  :fill="baseAttr.fontStyle === 'italic' ? '#305ef4' : '#666'"
                ></path>
              </svg>
            </Button>
            <Button
              v-if="noTCurvedText"
              @click="changeLineThrough('linethrough', baseAttr.linethrough)"
            >
              <svg viewBox="0 0 1024 1024" width="14" height="14">
                <path
                  d="M893.088 501.792H125.344a32 32 0 0 0 0 64h767.744a32 32 0 0 0 0-64zM448 448h112V208h288V96H160v112h288zM448 640h112v288H448z"
                  :fill="baseAttr.linethrough ? '#305ef4' : '#666'"
                ></path>
              </svg>
            </Button>
            <Button v-if="noTCurvedText" @click="changeUnderline('underline', baseAttr.underline)">
              <svg viewBox="0 0 1024 1024" width="14" height="14">
                <path
                  d="M703.232 67.008h127.488v413.248c0 158.016-142.656 286.016-318.72 286.016-176 0-318.72-128-318.72-286.016V67.008h127.488v413.248c0 39.872 18.176 78.144 51.136 107.776 36.8 32.96 86.528 51.072 140.096 51.072s103.36-18.112 140.032-51.136c33.024-29.632 51.2-67.968 51.2-107.776V67.008zM193.28 871.616h637.44v85.376H193.28v-85.376z"
                  :fill="baseAttr.underline ? '#305ef4' : '#666'"
                ></path>
              </svg>
            </Button>
          </ButtonGroup>
        </div>
      </div>

      <Row :gutter="12" v-if="noTCurvedText">
        <Col flex="1">
          <InputNumber
            v-model="baseAttr.lineHeight"
            @on-change="(value) => changeCommon('lineHeight', value)"
            :step="0.1"
            :append="$t('attributes.line_height')"
          ></InputNumber>
        </Col>
        <Col flex="1">
          <InputNumber
            v-model="baseAttr.charSpacing"
            @on-change="(value) => changeCommon('charSpacing', value)"
            :append="$t('attributes.char_spacing')"
          ></InputNumber>
        </Col>
      </Row>

      <div class="flex-view" v-if="noTCurvedText">
        <div class="flex-item">
          <span class="label">{{ $t('background') }}</span>
          <div class="content">
            <ColorPicker
              v-model="baseAttr.textBackgroundColor"
              @on-change="(value) => changeCommon('textBackgroundColor', value)"
              alpha
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="AttrBute">
import useSelect from '@/hooks/select'
import ClipboardJS from 'clipboard'
import { Message, Spin } from 'view-ui-plus'
import InputNumber from '@/components/inputNumber'
import { getFontFamilyList } from '@/api/v2'
import { COS_URL } from '@/utils/cos'
import VirtualFontSelect from '@/components/VirtualFontSelect.vue'
const APP_APIHOST = import.meta.env.APP_APIHOST
const update = getCurrentInstance()
const { mixinState, canvasEditor } = useSelect()

const textType = ['i-text', 'textbox', 'text', 'curved-text'];

const noTCurvedText = computed(() => mixinState.mSelectOneType !== 'curved-text');

// 确保字体列表始终是有效的数组
const safeFontsList = computed(() => {
  return Array.isArray(myFontsList.value) ? myFontsList.value : [];
});

// 分页相关代码已移除，现在使用虚拟滚动

const copyText = computed(() => typeof baseAttr.fontFamily === 'string' ? baseAttr.fontFamily : baseAttr.fontFamily.name)

const baseAttr = reactive({
  fontSize: 0,
  fontFamily: '',
  lineHeight: 0,
  charSpacing: 0,
  fontWeight: 500,
  textBackgroundColor: '#fff',
  textAlign: '',
  fontStyle: '',
  underline: false,
  linethrough: false,
  overline: false,
});

const allFontsList = ref([])

const isFontLoading = ref(false)
const myFontsList = ref([]);
const pageConfig = ref({
  pageNum: 1,
  pageSize: 30
})
const getFontListHandle = async () => {
  try {
    isFontLoading.value = true
    const result = await getFontFamilyList({
      pageNum: 1,
      pageSize: 100000
    })

    if (!result?.data || !Array.isArray(result.data)) {
      console.warn('Invalid font data received');
      myFontsList.value = [];
      allFontsList.value = [];
      return;
    }

    const list = result.data.map(item => ({
      id: item.id,
      name: item.name || '',
      file: item.fileUrl ? COS_URL + item.fileUrl : '',
      img: item.imgUrl ? APP_APIHOST + item.imgUrl : ''
    })).filter(item => item.name); // 过滤掉没有名称的项

    canvasEditor.createFontCSS(list, 'myFont');
    canvasEditor.pushFontList(list);
    myFontsList.value = list
    allFontsList.value = list
  } catch (error) {
    console.error('Failed to load fonts:', error);
    myFontsList.value = [];
    allFontsList.value = [];
  } finally {
    isFontLoading.value = false
  }
}
getFontListHandle()

canvasEditor.on('refreshFontList', getFontListHandle);

const textAlignList = ['left', 'center', 'right'];
const textAlignListSvg = [
  '<svg t="1650441458823" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3554" width="18" height="18"><path d="M198.4 198.4h341.333333c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533334 19.2v57.6c0 8.533333-2.133333 14.933333-8.533334 19.2-6.4 6.4-12.8 8.533333-19.2 8.533334h-341.333333c-8.533333 0-14.933333-2.133333-19.2-8.533334-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 12.8-8.533333 19.2-8.533333z m0 170.666667h569.6c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533333h-569.6c-8.533333 0-14.933333-2.133333-19.2-8.533333-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 12.8-8.533333 19.2-8.533333z m0 170.666666h454.4c8.533333 0 14.933333 2.133333 19.2 8.533334 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533333h-454.4c-8.533333 0-14.933333-2.133333-19.2-8.533333-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 12.8-8.533333 19.2-8.533334z m0 170.666667h625.066667c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533334h-625.066667c-8.533333 0-14.933333-2.133333-19.2-8.533334-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 12.8-8.533333 19.2-8.533333z" p-id="3555"></path></svg>',
  '<svg t="1650441512015" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3704" width="18" height="18"><path d="M313.6 198.4h398.933333c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533334 19.2v57.6c0 8.533333-2.133333 14.933333-8.533334 19.2-6.4 6.4-12.8 8.533333-19.2 8.533334h-398.933333c-8.533333 0-14.933333-2.133333-19.2-8.533334-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 10.666667-8.533333 19.2-8.533333z m-115.2 170.666667h625.066667c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533333h-625.066667c-8.533333 0-14.933333-2.133333-19.2-8.533333-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 12.8-8.533333 19.2-8.533333z m115.2 170.666666h398.933333c8.533333 0 14.933333 2.133333 19.2 8.533334 6.4 6.4 8.533333 12.8 8.533334 19.2v57.6c0 8.533333-2.133333 14.933333-8.533334 19.2-6.4 6.4-12.8 8.533333-19.2 8.533333h-398.933333c-8.533333 0-14.933333-2.133333-19.2-8.533333-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 10.666667-8.533333 19.2-8.533334z m-115.2 170.666667h625.066667c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533334h-625.066667c-8.533333 0-14.933333-2.133333-19.2-8.533334-6.4-6.4-8.533333-12.8-8.533333-19.2v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 4.266667-4.266667 12.8-8.533333 19.2-8.533333z" p-id="3705"></path></svg>',
  '<svg t="1650441519862" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3854" width="18" height="18"><path d="M454.4 283.733333v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 6.4-6.4 12.8-8.533333 19.2-8.533333h341.333334c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533334h-341.333334c-8.533333 0-14.933333-2.133333-19.2-8.533334-4.266667-4.266667-8.533333-10.666667-8.533333-19.2z m-226.133333 170.666667v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 6.4-6.4 12.8-8.533333 19.2-8.533333h569.6c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533333H256c-8.533333 0-14.933333-2.133333-19.2-8.533333-6.4-4.266667-8.533333-10.666667-8.533333-19.2z m113.066666 170.666667v-57.6c0-8.533333 2.133333-14.933333 8.533334-19.2 6.4-6.4 12.8-8.533333 19.2-8.533334h454.4c8.533333 0 14.933333 2.133333 19.2 8.533334 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533333h-454.4c-8.533333 0-14.933333-2.133333-19.2-8.533333-6.4-4.266667-8.533333-10.666667-8.533334-19.2z m-170.666666 170.666666v-57.6c0-8.533333 2.133333-14.933333 8.533333-19.2 6.4-6.4 12.8-8.533333 19.2-8.533333h625.066667c8.533333 0 14.933333 2.133333 19.2 8.533333 6.4 6.4 8.533333 12.8 8.533333 19.2v57.6c0 8.533333-2.133333 14.933333-8.533333 19.2-6.4 6.4-12.8 8.533333-19.2 8.533334h-625.066667c-8.533333 0-14.933333-2.133333-19.2-8.533334-6.4-4.266667-8.533333-10.666667-8.533333-19.2z" p-id="3855"></path></svg>',
];

const getObjectAttr = (e) => {
  const activeObject = canvasEditor.canvas.getActiveObject();
  // activeObject.isClamp = true
  // if (activeObject && activeObject.linkData) {
  //   activeObject.text = activeObject.text.toLocaleLowerCase()
  //   activeObject.linkData = ['text', activeObject.text.slice(1, -1)]
  // }
  // if (activeObject) {
  //   activeObject.isClamp = true
  // }
  isFontLoading.value = true
  if (e && e.target && e.target !== activeObject) return;
  if (activeObject && textType.includes(activeObject.type)) {
    baseAttr.fontSize = activeObject.get('fontSize');
    baseAttr.fontFamily = activeObject.get('fontFamily');
    baseAttr.lineHeight = activeObject.get('lineHeight');
    baseAttr.textAlign = activeObject.get('textAlign');
    baseAttr.underline = activeObject.get('underline');
    baseAttr.linethrough = activeObject.get('linethrough');
    baseAttr.charSpacing = activeObject.get('charSpacing');
    baseAttr.overline = activeObject.get('overline');
    baseAttr.fontStyle = activeObject.get('fontStyle');
    baseAttr.textBackgroundColor = activeObject.get('textBackgroundColor');
    baseAttr.fontWeight = activeObject.get('fontWeight') === 'normal' ? 500 : activeObject.get('fontWeight');
  }
  setTimeout(() => {
    isFontLoading.value = !allFontsList.value.length
  }, 1000)
};

const handleOpen = () => {
  // VirtualFontSelect 组件会自动滚动到当前选中的字体位置
}

const onSearch = str => {
  // 搜索逻辑现在由 VirtualFontSelect 组件内部处理
}

const changeCommon = (key, value) => {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  if (activeObject) {
    activeObject && activeObject.set(key, value);
    canvasEditor.canvas.renderAll();
  }
};

const selectCancel = () => {
  update?.proxy?.$forceUpdate();
};

const changeFontFamily = async (fontName) => {
  // VirtualFontSelect 传递的是选中的字体对象
  const actualFontName = typeof fontName === 'object' && fontName ? fontName.name : fontName;

  if (!actualFontName) {
    changeCommon('fontFamily', '')
    baseAttr.fontFamily = ''
    return
  }

  Spin.show()
  canvasEditor.loadFont(actualFontName).finally(() => Spin.hide())
};

const changeFontStyle = (key, value) => {
  const nValue = value === 'normal' ? 'italic' : 'normal';
  baseAttr.fontStyle = nValue;
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  activeObject && activeObject.set(key, nValue);
  canvasEditor.canvas.renderAll();
};

// 字体大小写
function isUpperCase(str) {
  return str === str.toUpperCase()
}
const handleChangeLetterCase = () => {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];

  if (activeObject.type === 'curved-text') {
    const text = activeObject.text
    activeObject.set('text', isUpperCase(text) ? text.toLowerCase() : text.toUpperCase())
    canvasEditor.canvas.setActiveObject(canvasEditor.canvas.getObjects()[0])
    nextTick(() => {
      canvasEditor.canvas.setActiveObject(activeObject)
      canvasEditor.canvas.renderAll()
    })
    return
  }

  if (!activeObject) { return; }
  const text = activeObject.text.slice(activeObject.selectionStart, activeObject.selectionEnd);
  let letterText = ''
  if (isUpperCase(text)) {
    letterText = activeObject.text.slice(0, activeObject.selectionStart) + text.toLowerCase() + activeObject.text.slice(activeObject.selectionEnd)
  } else {
    letterText = activeObject.text.slice(0, activeObject.selectionStart) + text.toUpperCase() + activeObject.text.slice(activeObject.selectionEnd)
  }
  activeObject.set('text', letterText);
  canvasEditor.canvas.renderAll();
};

const changeLineThrough = (key, value) => {
  const nValue = value === false;
  baseAttr.linethrough = nValue;
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  activeObject && activeObject.set(key, nValue);
  canvasEditor.canvas.renderAll();
};

const changeUnderline = (key, value) => {
  const nValue = value === false;
  baseAttr.underline = nValue;
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  activeObject && activeObject.set(key, nValue);
  canvasEditor.canvas.renderAll();
};

onMounted(() => {
  getObjectAttr();
  // $bus.on(EVENT_NAME.JSON_LOADED, handleFamilyLoad)
  canvasEditor.on('selectCancel', selectCancel);
  canvasEditor.on('selectOne', getObjectAttr);
  canvasEditor.canvas.on('object:modified', getObjectAttr);
  const clipboard = new ClipboardJS('.copy-text')
  clipboard.on('success', () => {
    Message.success(`复制 ${copyText.value} 成功`)
  })
});

onBeforeUnmount(() => {
  // $bus.off(EVENT_NAME.JSON_LOADED, handleFamilyLoad)
  canvasEditor.off('selectCancel', selectCancel);
  canvasEditor.off('selectOne', getObjectAttr);
});
</script>

<style scoped lang="less">
:deep(.ivu-input-number) {
  display: block;
  width: 100%;
}

:deep(.ivu-color-picker) {
  display: block;
}
.ivu-row {
  margin-bottom: 8px;
  .ivu-col {
    position: inherit;
    &__box {
      display: flex;
      align-items: center;
      background: #f8f8f8;
      border-radius: 4px;
      gap: 8px;
    }
  }

  .label {
    padding-left: 8px;
  }
  .content {
    flex: 1;
    :deep(.--input),
    :deep(.ivu-select-selection) {
      background-color: transparent;
      border: none !important;
      box-shadow: none !important;
    }
  }
}
.font-selector {
  :deep(.v-select) {
    width: 100%;
  }

  :deep(.vs__dropdown-menu) {
    max-height: 320px;
    width: 240px;
    padding: 0 5px;
    box-sizing: border-box;
    overflow-x: hidden;
  }

  :deep(.vs__dropdown-option) {
    padding: 1px 4px;
  }

  :deep(.vs__selected-options) {
    padding: 0;
  }

  :deep(.vs__actions) {
    padding: 0;
    margin-top: 5px;
    margin-right: 5px;
  }

  .font-item {
    height: 40px;
    width: 330px;
    background-size: auto 40px;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    padding: 0 8px;
    cursor: pointer;
  }

  .font-item__history:hover {
    background-color: #f6f8fa;
  }
}

.flex-view {
  width: 100%;
  margin-bottom: 5px;
  padding: 5px;
  display: inline-flex;
  justify-content: space-between;
  border-radius: 5px;
  background: #f6f7f9;
}
.flex-item {
  display: inline-flex;
  flex: 1;
  .label {
    width: 32px;
    height: 32px;
    line-height: 32px;
    display: inline-block;
    font-size: 14px;
  }
  .content {
    flex: 1;
  }
  .slider-box {
    width: calc(100% - 50px);
    margin-left: 10px;
  }
  .left {
    flex: 1;
    width: calc(50% - 5px);
  }
  .right {
    flex: 1;
    width: calc(50% - 5px);
    margin-left: 10px;
    :deep(.ivu-input-number) {
      display: block;
      width: 100%;
    }
  }
  :deep(.ivu-slider-wrap) {
    margin: 13px 0;
  }
  :deep(.ivu-radio-group-button) {
    display: flex;
    flex: 1;
    width: 100%;
    & .ivu-radio-wrapper {
      flex: 1;
      line-height: 40px;
      text-align: center;
      svg {
        vertical-align: baseline;
      }
    }
  }

  :deep(.ivu-btn-group) {
    display: flex;
    flex: 1;
    .ivu-btn {
      flex: 1;
    }
  }

  :deep(.ivu-btn-group-large) {
    & > .ivu-btn {
      font-size: 24px;
      flex: 1;
    }
  }

  :deep(.ivu-radio-group-button) {
    &.ivu-radio-group-large .ivu-radio-wrapper {
      font-size: 24px;
    }
  }
}
</style>

<style>
.vs--searchable .vs__dropdown-toggle {
  height: 33px;
}
.vs__dropdown-option:has(.font-item__active) {
  background-color: #6194f4;
}
.copy-text {
  font-size: 17px;
  position: absolute;
  right: 7px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  z-index: 500;
}
.vs__actions {
  display: none;
}
.vs__selected {
  overflow: hidden;
  width: 90px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
