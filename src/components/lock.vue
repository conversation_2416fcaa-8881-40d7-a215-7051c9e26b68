<template>
  <Tooltip :content="$t('quick.lock')" v-if="mixinState.mSelectMode === 'one'">
    <Button long v-if="isLock" @click="doLock(false)" icon="md-lock" type="text"></Button>
    <Button long v-else @click="doLock(true)" icon="md-unlock" type="text"></Button>
  </Tooltip>
</template>

<script setup name="Lock">
import useSelect from '@/hooks/select';
import { onBeforeUnmount, onMounted, onBeforeMount } from 'vue';
import {$bus, EVENT_NAME} from "@/utils/mitt";

const { mixinState, canvasEditor } = useSelect();
const lockAttrs = [
  'lockMovementX',
  'lockMovementY',
  'lockRotation',
  'lockScalingX',
  'lockScalingY',
];
const isLock = ref(false);
const lock = () => {
  mixinState.mSelectActive.hasControls = false;
  lockAttrs.forEach((key) => {
    mixinState.mSelectActive[key] = true;
  });

  mixinState.mSelectActive.selectable = false;

  isLock.value = true;
  canvasEditor.canvas.renderAll();
};
const unLock = () => {
  mixinState.mSelectActive.hasControls = true;
  lockAttrs.forEach((key) => {
    mixinState.mSelectActive[key] = false;
  });
  mixinState.mSelectActive.selectable = true;

  isLock.value = false;
  canvasEditor.canvas.renderAll();
};

const doLock = (isLock) => {
  isLock ? lock() : unLock();
};


const recordId = ref({})
const handleSelected = (items) => {
  console.log(items)
  const id = items[0].id
  if (recordId.value[id]) {
    isLock.value = !items[0].hasControls
  } else {
    const hasControls = json.value.objects?.find(item => item.id === id)?.hasControls
    isLock.value = typeof hasControls === 'boolean' ? !hasControls : false
    recordId.value[id] = true
  }
  if (isLock.value) {
    nextTick(() => {
      lock()
    })
  }
  mixinState.mSelectActive = items[0];
};

const json = ref({})
onMounted(() => {
  canvasEditor.on('selectOne', handleSelected);
  $bus.on(EVENT_NAME.CURRENT_JSON, (record) => {
    json.value = record.json
    recordId.value = {}
  });
});

onBeforeUnmount(() => {
  canvasEditor.off('selectOne', handleSelected);
});
</script>

<style scoped lang="less">
h3 {
  margin: 40px 0 0;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
</style>
