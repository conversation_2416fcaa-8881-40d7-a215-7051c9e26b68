<template>
  <div class="my-material">
    <myTempl></myTempl>
  </div>
</template>

<script setup name="ImportTmpl">
import { getFileList } from '@/api/user';
import myTempl from './myTempl';

const isLogin = ref(false);
const getFileListHandle = () => {
  getFileList()
    .then(() => {
      isLogin.value = true;
    })
    .catch(() => {
      isLogin.value = false;
    });
};

// getFileListHandle()
</script>

<style scoped lang="less">
.tip {
  padding: 20px;
  text-align: center;
}
.my-material {
  padding-top: 10px;
}
</style>
