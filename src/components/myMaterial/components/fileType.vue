<template>
  <div class="file-type-box">
    <div v-if="preview" class="c-file-preview" :style="{ backgroundImage: `url(${toWebp(preview)})` }" @click.stop="handleClick"></div>
    <img :src="fileTypeIcon" />
    <span>{{ props.name }}</span>
    <div class="click-bg" @click.stop="handleClick"></div>
  </div>
</template>

<script setup name="ImportTmpl">
import { toWebp } from '@/utils/cos'
import fileTypeIcon from '@/assets/icon/fileType.png'

const props = defineProps({
  name: {
    type: String,
    default: '',
  },
  itemId: {
    type: [Number, String],
    default: '',
  },
  parentId: {
    type: [Number, String],
    default: ''
  },
  type: {
    type: String,
    default: ''
  },
  record: {
    type: Object,
    default: () => {
      return {}
    }
  },
  preview: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['select', 'ok']);

const handleClick = () => {
  const record = {
    id: props.itemId,
    name: props.name,
    type: props.type,
    parentId: props.parentId,
  }
  emit('ok', record)
}
</script>

<style scoped lang="less">
.file-type-box {
  width: 134px;
  height: 116px;
  cursor: pointer;
  background: var(--bg-color);
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--border-color);
  border-radius: 10px;
  img {
    width: 60px;
    margin-top: 12px;
    z-index: 10;
  }
  span {
    display: block;
    padding-top: 10px;
    text-align: center;
    width: 90%;
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; //溢出不换行
  }
  div.more {
    position: absolute;
    display: none;
  }

  .click-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 11;
  }

  &:hover {
    background: var(--file-type-bg);
    border: 1px solid rgb(225, 230, 239);
    border-radius: 8px;
    div.more {
      display: block;
      top: 5px;
      right: 5px;
    }
    .c-file-preview {
      width: 85px;
      height: 85px;
      top: 0;
      left: 50%;
      z-index: 100;
      transform: rotate(0) translateX(-50%);
    }
  }

  .c-file-preview {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 4px;
    top: 10%;
    left: 45%;
    transform: rotate(30deg);
    background-size: contain;
    background-repeat: no-repeat;
    transition: all ease-in-out 0.2s;
  }
}
</style>
