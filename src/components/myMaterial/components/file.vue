<template>
  <div class="file-type-box" ref="fileRef">
    <Image
      lazy
      @click.stop="beforeClearTip"
      @dblclick.stop="beforeClearTip"
      :src="props.src && toWebp(props.src, 132, 94)"
      fit="contain"
      :height="imageHeight"
      width="100%"
      :alt="props.name"
    />
    <p class="name-wrap">{{ props.name }}</p>
  </div>
</template>

<script setup name="ImportTmpl">
import useTemplateList from '@/components/myMaterial/hooks/useTemplateList'
import useSelect from '@/hooks/select'
import Loading from '@/components/load/index.js'
import { $bus, EVENT_NAME } from '@/utils/mitt'
import { useI18n } from 'vue-i18n'
import { getUserFileTypeTree } from '@/api/user'
import { Modal } from 'view-ui-plus'
import { toWebp } from '@/utils/cos'

const { t } = useI18n()
const { canvasEditor } = useSelect()
const { getTemplateInfo, routerToId } = useTemplateList()

const props = defineProps({
  name: {
    type: String,
    default: '',
  },
  src: {
    type: String,
    default: '',
  },
  itemId: {
    type: [Number, String],
    default: '',
  },
  parentId: {
    type: [Number, String],
    default: '',
  }
});

const emit = defineEmits(['ok']);

const imageHeight = ref(0)
const fileRef = ref(null)

const beforeClearTip = () => {
  Modal.confirm({
    title: t('tip'),
    content: `<p>${t('replaceTip')}</p>`,
    okText: t('ok'),
    cancelText: t('cancel'),
    onOk: () => getTempData(),
  });
};

const getTempData = async () => {
  Loading.show()
  const data = await getTemplateInfo(props.itemId)
  props.itemId && $bus.emit(EVENT_NAME.CURRENT_ID, { id: props.itemId, name: data.name, type: data.type })
  routerToId(props.itemId)
  const json = data.json
  $bus.emit(EVENT_NAME.CURRENT_JSON, { json })
  canvasEditor.clearHistory()
  canvasEditor.loadJSON(JSON.stringify(json), () => {
    emit('ok', props.parentId)
    $bus.emit(EVENT_NAME.JSON_LOADED)
    Loading.hide()
  })
}

const modalVisable = ref(false);
const fileTypeId = ref('');
const treeData = ref([]);

const transfer = async () => {
  treeData.value = [];
  fileTypeId.value = '';
  const res = await getUserFileTypeTree();
  treeData.value = [res.data.data];
  modalVisable.value = true;
}

const init = () => {
  imageHeight.value = fileRef.value.clientHeight - 20
}

onMounted(() => {
  init()
})
</script>

<style scoped lang="less">
// 文件夹
.file-type-box {
  width: 134px;
  height: 116px;
  cursor: pointer;
  background: var(--bg-color);
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--border-color);
  border-radius: 10px;
  .ivu-image {
    height: 94px !important;
  }
  img {
    width: 60px;
    margin-top: 12px;
  }
  div.more {
    position: absolute;
    display: none;
  }
  &:hover {
    background: var(--file-type-bg);
    border: 1px solid rgb(225, 230, 239);
    border-radius: 8px;
    img {
      opacity: 0.8;
    }
    div.more {
      display: block;
      top: 5px;
      right: 5px;
    }
  }
  .name-wrap {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
  }
}
</style>
