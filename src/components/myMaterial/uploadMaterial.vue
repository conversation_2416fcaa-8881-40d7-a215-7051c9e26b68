<template>
  <div ref="pictureContainerRef" class="my-material">
    <Input search placeholder="搜索" v-model="searchData" @on-change="search" />
    <Button icon="md-cloud-upload" @click="uploadImgHandule" long type="primary">
      {{ $t('myMaterial.uploadBtn') }}
    </Button>
    <Breadcrumb style="margin: 5px 0; cursor: pointer">
      <BreadcrumbItem v-for="item in filePath" :key="item.value" @click="handleFilePath(item.value)">{{ item.label }}</BreadcrumbItem>
    </Breadcrumb>

    <Scroll v-if="fileList.length" class="my-picture-scroll" :on-reach-bottom="handleSearchMore" :height="scrollHeight" @contextmenu.stop="onContextMainMenu">
      <div v-for="(info, i) in fileList" :key="`${i}-bai1-button`" class="c-picture-item__position">
        <div class="c-picture-item" @contextmenu.stop="onContextMenu($event, info)">
          <template v-if="info.isFile">
            <div @click.stop="handleFileOpen(info.id, info.name)">
              <img class="c-my-picture-scroll__img" :src="fileTypeIcon" alt="" />
              <p class="name-wrap">{{ info.name }}</p>
            </div>
          </template>
          <template v-else>
            <Image lazy :src="info.imgUrl" :alt="info.name" fit="contain" width="100%" height="calc(100% - 21px)" @click="addImgByElement" />
            <p class="name-wrap">{{ info.name }}</p>
          </template>
        </div>
      </div>
    </Scroll>
    <div class="tip" v-else>
      <template v-if="isLoading">
        <div class="first-loading-wrp">
          <div class="loading-wrp">
            <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="c-md-cloud__no" @contextmenu.stop="onContextMainMenu">暂无素材</div>
      </template>
    </div>
  </div>
</template>

<script setup name="ImportTmpl">
import { Input, Message, Modal } from 'view-ui-plus'
import { getMaterialList, setMaterial, addMaterialFolder } from '@/api/v2'
import ContextMenu from '@imengyu/vue3-context-menu'
import fileTypeIcon from '@/assets/icon/fileType.png'

const APP_APIHOST = import.meta.env.APP_APIHOST;
import { removeMaterial } from '@/api/v2'
import { Utils } from '@kuaitu/core'

const { selectFiles } = Utils
const canvasEditor = inject('canvasEditor');

const isLoading = ref(false)
const searchData = ref('')
const filePath = ref([
  { label: '全部', value: 0 }
])

const handleFilePath = (value) => {
  const index = filePath.value.findIndex(item => item.value === value)
  filePath.value = filePath.value.slice(0, index + 1)
  handleClearSearch(value)
}

const uploadImgHandule = async () => {
  const result = await selectFiles({
    accept: 'image/*',
  })
  const params = {
    file: result[0],
    parentId: pageConfig.parentId
  }
  try {
    const { data } = await setMaterial(params)
    Message.success(data)
  } catch (e) {
    Message.error('上传素材失败')
  } finally {
    handleClearSearch(params.parentId)
  }
}
const addImgByElement = (e) => {
  canvasEditor.addImgByElement(e.target)
  // canvasEditor.addImgByElement({ src: 'https://test-env-1317082621.cos.ap-hongkong.myqcloud.com/folder/fc.jpg' } )
};

const search = () => {
  if (searchData.value !== "") {
    fileList.value = fileList.value.filter(item => item.name.search(searchData.value) != -1)
  }
}

const onContextMainMenu = (e) => {
  e.preventDefault()
  const items = [
    {
      label: '新建文件夹',
      onClick: () => {
        const fileName = ref('')
        Modal.confirm({
          title: '请输入文件夹名',
          render: (h) => {
            return h(Input, {
              size: 'large',
              modelValue: fileName,
              autofocus: true,
              placeholder: '请输入文件名',
            });
          },
          onOk: async () => {
            if (!fileName.value.trim()) return Message.error('请输入文件名')
            try {
              await addMaterialFolder({
                parentId: pageConfig.parentId,
                name: fileName.value
              })
              Message.success('创建成功')
              handleClearSearch(pageConfig.parentId)
            } catch (e) {
              Message.error('创建失败')
            }
          }
        })
      }
    }
  ]
  ContextMenu.showContextMenu({
    customClass: 'c-main-menu',
    x: e.x,
    y: e.y,
    items
  })
}

const onContextMenu = (e, currentRecord) => {
  e.preventDefault()
  const items = [
    {
      label: '下载',
      onClick: () => {
        const a = document.createElement('a')
        a.href = window.location.origin + currentRecord.imgUrl
        a.download = currentRecord.name
        a.click()
      }
    },
    {
      label: '删除',
      onClick: () => {
        Modal.confirm({
          title: '提示',
          content: `是否删除 <span class="c-remove-name">${currentRecord.name}</span> ？`,
          onOk: async () => {
            try {
              await removeMaterial({ ids: [currentRecord.id] })
              Message.success('删除成功')
              handleClearSearch(pageConfig.parentId)
            } catch (e) {
              Message.error('删除失败')
            }
          }
        })
      }
    }
  ]

  if (currentRecord.isFile) {
    items.splice(0, 1)
  }

  ContextMenu.showContextMenu({
    x: e.x,
    y: e.y,
    items
  })
}

const fileList = ref([])
const pageConfig = reactive({
  pageNum: 0,
  pageSize: 30,
  parentId: 0
})
const handleSearchMore = async () => {
  pageConfig.pageNum++
  isLoading.value = true
  const result = await getMaterialList({ ...pageConfig })
  fileList.value = [
    ...fileList.value,
    ...result.data.map((item) => {
      return {
        id: item.id,
        name: item.attributes.name,
        imgUrl: APP_APIHOST + item.attributes.img.data.attributes.url,
        isFile: item.attributes.type === '1'
        // imgUrl: 'https://test-env-1317082621.cos.ap-hongkong.myqcloud.com/folder/fc.jpg?imageMogr2/format/webp/thumbnail/124x103'
      }
    })
  ].sort(item => item.isFile ? -1 : 1)
  isLoading.value = false
}

const handleClearSearch = (id = 0) => {
  pageConfig.pageNum = 0
  pageConfig.parentId = id
  fileList.value = []
  handleSearchMore()
}

const handleFileOpen = (id, label) => {
  handleClearSearch(id)
  filePath.value.push({
    value: id,
    label
  })
}

const scrollHeight = ref(0)
const pictureContainerRef = ref(null)
onMounted(() => {
  handleSearchMore()
  scrollHeight.value = pictureContainerRef.value.clientHeight ? pictureContainerRef.value.clientHeight - 140 : 600
})
</script>

<style scoped lang="less">
.img-group {
  padding: 10px 0;
  margin-top: 10px;
  display: flex;
  flex-flow: row wrap;
}
.c-picture-item {
  width: 134px;
  height: 134px;
  padding: 5px;
  margin: 5px;
  cursor: pointer;
  border-radius: 10px;
  text-align: center;

  background: #eeeeeea1;

  &:hover {
    background: #e3e3e3;
    .del-btn {
      right: 5px;
    }
  }
}

.del-btn {
  z-index: 1;
  position: absolute;
  top: 5px;
  right: 1000000px;
}

.tip {
  text-align: center;
  padding: 10px;
}
.ivu-input-wrapper {
  margin: 10px 0;
}
.name-wrap {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}
.my-material {
  height: calc(100vh - 45px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}
</style>
<style lang="less">
.my-picture-scroll {
  .c-picture-item__position {
    width: 50%;
  }
  .ivu-scroll-content {
    display: flex;
    flex-wrap: wrap;
  }
}
.my-material {
  .ivu-scroll-wrapper {
    margin: 0 !important;
  }
}
.c-main-menu .mx-context-menu-item .label {
  overflow: auto;
  text-overflow: initial;
  width: 110px;
}
.c-my-picture-scroll__img {
  width: 60px;
  height: 55px;
  margin: 25px 20px 20px;
}
.c-md-cloud__no {
  height: 730px;
}
</style>
