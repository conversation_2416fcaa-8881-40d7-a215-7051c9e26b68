<template>
  <div>
    <div class="search-box" v-if="!isOuter">
      <Dropdown @on-click="createType" placement="bottom-start" style="margin-right: 10px" transfer>
        <Button type="primary" icon="md-add"></Button>
        <template #list>
          <DropdownMenu>
            <DropdownItem name="file">新建设计</DropdownItem>
            <DropdownItem name="fileType">新建文件夹</DropdownItem>
          </DropdownMenu>
        </template>
      </Dropdown>

      <Input
        class="input"
        placeholder="请输入关键词"
        v-model="pageConfig.keyword"
        search
        :disabled="isLoading"
        @on-search="() => handleResetSearch('')"
      >
      </Input>
    </div>

    <div class="c-my-template__top">
      <Breadcrumb>
        <BreadcrumbItem
            @click="toFile(item.parentId, i, item.name)"
            :key="item.id"
            v-for="(item, i) in filePath"
        >
          {{ item.name }}
        </BreadcrumbItem>
      </Breadcrumb>
      <div class="c-my-template__top__select">
        <Dropdown style="margin-left: 20px">
          <a href="javascript:void(0)">
            排序<Icon type="ios-arrow-down"></Icon>
          </a>
          <template #list>
            <DropdownMenu>
              <DropdownItem :class="{ 'is-active': sortKey === '' }" @click="handleSort('')">默认</DropdownItem>
              <DropdownItem :class="{ 'is-active': sortKey === 'name' }" @click="handleSort('name')">文件名</DropdownItem>
              <DropdownItem :class="{ 'is-active': sortKey === 'createdAt' }" @click="handleSort('createdAt')">创建时间</DropdownItem>
              <DropdownItem :class="{ 'is-active': sortKey === 'updatedAt' }" @click="handleSort('updatedAt')">更新时间</DropdownItem>
            </DropdownMenu>
          </template>
        </Dropdown>
      </div>
    </div>

    <div style="height: calc(100vh - 160px)" id="myFileTemplBox" @contextmenu.stop="onContextMenu" @mousedown.stop="handleMouseDown">
      <div class="mask" v-show="positionList.is_show_mask" :style="'width:' + mask_width + 'left:' + mask_left + 'height:' + mask_height + 'top:' + mask_top"></div>
      <Scroll
        key="myFileTemplBox"
        style="height: 100%"
        v-if="!!templateList.length"
        :on-reach-bottom="getTemplateListData"
        :on-reach-edg="handleResetSearch"
      >
        <div v-for="info in templateList" :key="info.name" class="item file-item" :class="{ 'is-selected': addList.includes(String(info.id)) }" :data-id="info.id" @contextmenu.stop="onContextMenu2($event, info)">
          <fileType
            v-if="info.type === 'fileType'"
            :itemId="info.id"
            :name="info.name"
            :parentId="info.parentId"
            :type="info.type"
            :preview="info.fileUrl"
            @ok="handleFileOk"
          ></fileType>

          <file
            v-else
            :src="info.url"
            :itemId="info.id"
            :parentId="info.parentId"
            :name="info.name"
            @ok="handleOk"
          ></file>
        </div>
        <Spin size="large" fix :show="isLoading"></Spin>
      </Scroll>
      <div style="text-align: center; margin-top: 10px" v-else>--暂无数据--</div>
    </div>

    <modalSzie
      :title="$t('importFiles.createDesign.title')"
      ref="modalSizeRef"
      @set="customSizeCreate"
    ></modalSzie>
  </div>
</template>

<script setup name="ImportTmpl">
import useSystemPermission from '@/hooks/permission'
import { Input } from 'view-ui-plus'
import { Spin, Modal, Message } from 'view-ui-plus'
import { $bus, EVENT_NAME } from '@/utils/mitt'
import { updateFileName, removeFile, pasteFile, getAllParentFolder, getPermissions } from '@/api/v2'
import Loading from '@/components/load/index.js'
import ContextMenu from '@imengyu/vue3-context-menu'

import fileType from './components/fileType.vue';
import file from './components/file.vue';
import modalSzie from '@/components/common/modalSzie';

import { useRoute } from 'vue-router'
import { useFrameSelect } from './hooks/useFrameSelect'
import useMaterial from '@/hooks/useMaterial'
import useTemplateList from '@/components/myMaterial/hooks/useTemplateList'
const route = useRoute()

const { isOuter } = useSystemPermission()

import useSelect from '@/hooks/select'
const { canvasEditor } = useSelect()
const { createdFileTypeV2, createTemplateV2 } = useMaterial()
const { positionList, mask_width, mask_height, mask_top, mask_left, addList, handleMouseDown } = useFrameSelect()

const {
  templateList,
  sortKey,
  isLoading,
  pageConfig,
  handleSort,
  getTemplateListData,
  handleResetSearch
} = useTemplateList()

// const filePermissions = ref([])
onMounted(async () => {
  await getFileTypeTreeData()
  await getTemplateListData()
  // const result = await getPermissions()
  // filePermissions.value = result.data.map(item => item.id)
})

canvasEditor.on('refreshFileList', handleResetSearch)

const fileTypeName = ref('');
const modalSizeRef = ref(null);
const createType = (type) => {
  if (type === 'fileType') {
    fileTypeName.value = ''
    Modal.confirm({
      title: '新建文件夹',
      render: (h) => {
        return h(Input, {
          size: 'large',
          modelValue: fileTypeName,
          autofocus: true,
          placeholder: '请输入文件夹名称',
        })
      },
      onOk: async () => {
        if (fileTypeName.value === '') {
          Message.warning('文件夹名称不能为空');
          return;
        }
        await createdFileTypeV2(fileTypeName.value, pageConfig.parentId)
        $bus.emit(EVENT_NAME.CLEAR_DOCUMENT)
        handleResetSearch()
      },
    })
  } else {
    modalSizeRef.value.showSetSize()
  }
}
const customSizeCreate = async (w, h) => {
  const name = await createTemplateV2(w, h, pageConfig.parentId);
  $bus.emit(EVENT_NAME.CLEAR_DOCUMENT)
  $bus.emit(EVENT_NAME.CURRENT_ID, { type: 'file', name })
  $bus.emit(EVENT_NAME.IS_HIDE_SHOW_BUTTON, true)
  await handleResetSearch()
}

const filePath = ref([
  {
    name: '全部',
    parentId: '',
  },
]);

const handleOk = (id, parentId, name, type) => {
  if (pageConfig.keyword) {
    pageConfig.keyword = ''
    pageConfig.parentId = id
    getFileTypeTreeData(id, name, parentId, type)
    handleResetSearch()
  }
}

const handleFileOk = record => {
  if (pageConfig.keyword) {
    handleOk(record.id, record.parentId, record.name, record.type)
  } else {
    joinFileTyper(record.id, record.name)
  }
}

const getFileTypeTreeData = async (id, name, parentId, type) => {
  if (route?.query?.id || name) {
    const result = await getAllParentFolder({
      id: id || route.query.id,
      type: 'USER_TEMPLS'
    })
    filePath.value = result.data.map(item => ({ parentId: item.id || '', name: item.name }))
    if (type === 'fileType') {
      filePath.value.push({
        parentId: id,
        name
      })
    }
    pageConfig.parentId = filePath.value.at(-1).parentId
  }
};
const joinFileTyper = (id, name) => {
  pageConfig.parentId = id
  id && $bus.emit(EVENT_NAME.CURRENT_ID, { id, name })
  filePath.value.push({
    name: name,
    parentId: id,
  })
  handleResetSearch()
}

const toFile = (id, i, name) => {
  pageConfig.keyword = ''
  $bus.emit(EVENT_NAME.CURRENT_ID, { id: id, name })
  pageConfig.parentId = id
  filePath.value = filePath.value.slice(0, i + 1)
  handleResetSearch()
}


// 右键菜单
const copyRecords = ref([]);
const onContextMenu = (e) => {
  e.preventDefault()
  ContextMenu.showContextMenu({
    x: e.x,
    y: e.y,
    items: [
      {
        label: '粘贴',
        onClick: () => {
          if (!copyRecords.value.length) return
          const currentRecord = filePath.value[filePath.value.length - 1]
          handlePaste({ ...currentRecord, id: currentRecord.parentId })
        }
      }
    ]
  })
}
const onContextMenu2 = (e, currentRecord) => {
  e.preventDefault();
  const items = [
    {
      label: '复制',
      onClick: () => {
        if (addList.value.length && addList.value.length > 1) {
          copyRecords.value = getSelections()
        } else {
          copyRecords.value = [currentRecord]
        }
        addList.value = []
      }
    },
    {
      label: '修改名称',
      onClick: () => {
        handleRename(currentRecord)
      }
    },
    {
      label: '删除',
      onClick: () => {
        if (addList.value.length > 1 && addList.value.includes(String(currentRecord.id))) {
          handleRemove(getSelections())
        } else {
          handleRemove([currentRecord])
        }
      }
    }
  ]
  // 只有文件夹才具有粘贴功能
  currentRecord.type === 'fileType' && copyRecords.value.length && items.splice(1, 0, {
    label: '粘贴',
    onClick: () => {
      handlePaste(currentRecord)
    }
  })
  ContextMenu.showContextMenu({
    x: e.x,
    y: e.y,
    items
  });
}

const getSelections = () => {
  const selections = []
  templateList.value.forEach(item => {
    if (addList.value.includes(String(item.id))) {
      selections.push(item)
    }
  })
  return selections
}

// 删除文件
const handleRemove = (selections) => {
  const name = selections.map(item => item.name).join('、')
  Modal.confirm({
    title: '提示',
    content: `是否删除 <span class="c-remove-name">${name}</span> ？`,
    onOk: async () => {
      await removeFile({
        ids: selections.map(item => item.id)
      })
      Message.success('删除成功')
      handleResetSearch()
    }
  })
}

// 粘贴文件
 const handlePaste = ({ id, name }) => {
   Modal.confirm({
     title: '提示',
     content: `是否粘贴 <span class="c-paste-name">${copyRecords.value.map(item => item.name).join('、')}</span> 到 <span class="c-remove-name">${name}</span> ？`,
     onOk: async () => {
       Loading.show('正在粘贴')
       try {
         const result = await pasteFile({
           sourceIds: copyRecords.value.map(item => item.id),
           targetId: id
         })
         if (result.code === 50001) throw new Error(result.message)
         if (result.code === 50000) throw new Error(result.message)
         Message.success('粘贴成功')
       } catch (e) {
         Message.error(e.message)
       } finally {
         Loading.hide()
         handleResetSearch()
       }
     }
   })
 }

 // 修改名称
const fileName = ref('')
const handleRename = (record) => {
  fileName.value = record.name
  Modal.confirm({
    title: '修改名称',
    render: (h) => {
      return h(Input, {
        size: 'default',
        modelValue: fileName,
        autofocus: true,
        placeholder: '请输入文件名称',
      });
    },
    onOk: async () => {
      Loading.show('正在修改')
      try {
        const result = await updateFileName({
          fileId: record.id,
          newName: fileName.value
        })
        if (result.code === 50001 || result.code === 50000) throw new Error(result.message)
        record.name = fileName.value
        Message.success('修改成功')
        if (record.id === Number(route.query.id || 0)) {
          $bus.emit(EVENT_NAME.FILE_NAME, fileName.value)
        }
      } catch (e) {
        Message.error(e.message)
      } finally {
        Loading.hide()
      }
    }
  })
}
</script>

<style scoped lang="less">
.search-box {
  padding-bottom: 10px;
  display: flex;
}

:deep(.ivu-scroll-content) {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-right: 10px;
}
.item {
  margin-bottom: 10px;
}
:deep(.ivu-breadcrumb-item-link) {
  cursor: pointer;
  &:hover {
    color: #57a3f3;
  }
}
</style>
<style lang="less">
// 右键菜单
.mx-context-menu {
  padding: 8px 0;
  border-radius: 5px;
}
.mx-context-menu-item {
  padding: 6px 15px;
  .label {
    width: 80px;
    letter-spacing: 2px;
    text-align: center;
  }
}
.c-remove-name {
  color: #ff4d4f;
}
.c-paste-name {
  color: #0068ff;
}
#myFileTemplBox {
  position: relative;
  user-select: none;
  .mask {
    position: absolute;
    background: #409eff;
    opacity: 0.4;
    z-index: 100;
  }
  .is-selected {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    .file-type-box {
      background-color: rgba(#409eff, .3);
    }
  }
  .ivu-scroll-container {
    height: 100% !important;
  }
}
.c-my-template__top {
  display: flex;
  justify-content: space-between;
  .ivu-breadcrumb > span {
    display: inline-block;
  }
}
.c-my-template__top__select {
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.is-active {
  color: #63a0ed;
}
</style>
