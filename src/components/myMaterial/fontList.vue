<template>
  <div ref="materialRef" class="my-material">
    <Button icon="md-cloud-upload" @click="shwoModal" long type="primary">上传字体</Button>

    <div class="c-my-material__top__select">
      <Dropdown style="margin-left: 20px">
        <a href="javascript:void(0)">
          排序<Icon type="ios-arrow-down"></Icon>
        </a>
        <template #list>
          <DropdownMenu>
            <DropdownItem :class="{ 'is-active': sortKey === '' }" @click="handleInitSearch('')">默认</DropdownItem>
            <DropdownItem :class="{ 'is-active': sortKey === 'name' }" @click="handleInitSearch('name')">字体名</DropdownItem>
            <DropdownItem :class="{ 'is-active': sortKey === 'createdAt' }" @click="handleInitSearch('createdAt')">创建时间</DropdownItem>
            <DropdownItem :class="{ 'is-active': sortKey === 'updatedAt' }" @click="handleInitSearch('updatedAt')">更新时间</DropdownItem>
          </DropdownMenu>
        </template>
      </Dropdown>
    </div>

    <div v-if="fontList.length === 0" class="tip">暂无字体</div>
    <Scroll v-else :on-reach-bottom="handleSearchMore" class="my-material-scroll" :height="scrollHeight">
      <div class="item" v-for="item in fontList" :key="item.id" @contextmenu.stop="handleMouseDown($event, item)">
        <div @click.stop="addTextBy(item.name)">
          <img :src="item.img" alt="" />
        </div>
      </div>
    </Scroll>

    <Modal v-model="modal" title="请选择字体上传">
      <Form :label-width="80">
        <FormItem label="字体文件">
          <Upload action="/" :before-upload="selectFile" accept=".ttf,.woff,.woff2">
            <Button size="small" icon="ios-cloud-upload-outline">选择文件</Button>
          </Upload>
        </FormItem>
        <FormItem label="字体名称">
          <Input type="text" v-model="fontName" placeholder="请输入字体名称"></Input>
        </FormItem>
        <FormItem label="字体类型">
          <Select v-model="fontType" placeholder="请选择字体类型">
            <Option value="cn">中文</Option>
            <Option value="en">英文</Option>
          </Select>
        </FormItem>
        <FormItem label="字体预览">
          <span :style="`font-size: 32px; color: #000; font-family: ${fontNameId || '-'} ;`">
            {{ fontName }}
          </span>
        </FormItem>
      </Form>
      <template #footer>
        <Button type="text" @click="modal = false">取消</Button>
        <Button type="primary" :v-loading="loading" @click="asyncOK">确定</Button>
      </template>
    </Modal>
  </div>
</template>

<script setup name="myFontList">
const APP_APIHOST = import.meta.env.APP_APIHOST;
import { v4 as uuid } from 'uuid';
import { getFontFamilyList, removeFontFamily, uploadText } from '@/api/v2'
import { Message, Modal, Spin } from 'view-ui-plus';
import ContextMenu from '@imengyu/vue3-context-menu'
import useClamp from '@/hooks/useClamp'
import { cos, COS_URL } from '@/utils/cos'

import FontFaceObserver from 'fontfaceobserver';
const canvasEditor = inject('canvasEditor');
const fabric = inject('fabric');

const fontList = ref([]);
const sortKey = ref('');

const { dataURLtoFile } = useClamp()

const pageConfig = ref({
  pageNum: 0,
  pageSize: 30
})
const getFontListHandle = async () => {
  const params = { ...pageConfig.value }
  if (sortKey.value) {
    params[sortKey.value] = sortKey.value.includes('At') ? 'desc' : 'asc'
  }
  const result = await getFontFamilyList(params)
  return result.data.map((item) => {
    return {
      id: item.id,
      name: item.name,
      file: COS_URL + item.fileUrl,
      img: APP_APIHOST + item.imgUrl,
    };
  })
};

const modal = ref(false);
const fontName = ref('');
const fontType = ref('en');
const fontNameId = ref('');
const materialRef = ref(null)

const loading = ref(false);
const shwoModal = () => {
  fontName.value = '';
  fontNameId.value = '';
  modal.value = true;
  loading.value = false;
};
const asyncOK = async () => {
  if (!fontName.value || !fontType.value || !fontNameId.value) {
    Message.error('请填写完整信息');
    return;
  }

  if (!/^[A-Za-z]+$/.test(fontName.value)) {
    return Message.error('名称只允许字母大小写信息')
  }

  try {
    loading.value = true;
    const file = await getFontPreview();
    const { Location } = await cos.uploadFileToOther(file, file.name)
    const result = await uploadText({
      imgUrl: '//' + Location,
      name: fontName.value,
      file: fileCache
    })
    if (result.code === 50001) {
      throw Error(result.message)
    }
    Message.success('上传成功');
    handleInitSearch(sortKey.value)
    canvasEditor.emitRefreshFontList()
    modal.value = false;
    loading.value = false;
  } catch (error) {
    console.log(error)
    Message.error(error.message)
  }
};

let fileCache = null;
const selectFile = async (file) => {
  try {
    fileCache = file;
    fontName.value = file.name.split('.')[0];
    fontType.value = 'en';
    const id = 'font' + String(uuid().split('-')[0]);
    const base64Code = await canvasEditor.Utils.blobToBase64(file);
    canvasEditor.createFontCSS([
      {
        name: id,
        file: base64Code,
      },
    ]);
    fontNameId.value = id;
  } catch (error) {
    console.log(error);
  }

  return false;
};

const getFontPreview = async () => {
  const fontLoad = new FontFaceObserver(fontNameId.value);
  await fontLoad.load(null, 150000);

  const width = 330;
  const height = 40;
  const canvas = new fabric.Canvas();
  canvas.setWidth(width);
  canvas.setHeight(height);

  const text = new fabric.Text(fontName.value, {
    fontSize: 40,
    fill: '#000000',
    fontFamily: fontNameId.value,
  });
  canvas.add(text);

  const scale = fabric.util.findScaleToFit(text, { width, height });
  text.set({
    scaleX: scale,
    scaleY: scale,
    left: 0,
  });
  text.centerV();
  canvas.renderAll();

  const base64 = canvas.toDataURL({
    multiplier: 3,
  })

  return dataURLtoFile(base64, fontName.value + '.png')
};

const addTextBy = async (name) => {
  try {
    Spin.show();
    const fontLoad = new FontFaceObserver(name);
    await fontLoad.load(null, 150000);
    const text = new fabric.Textbox('万事大吉', {
      fontSize: 80,
      fontFamily: name,
      fill: '#000000FF',
      id: uuid(),
    });
    canvasEditor.canvas.add(text);
    canvasEditor.canvas.setActiveObject(text);
    canvasEditor.position('center');
  } catch (error) {
    console.log(error);
    Message.warning('请重试');
  }
  Spin.hide();
};

const removeFontHandle = (id) => {
  Modal.confirm({
    title: '确定删除字体文件吗？',
    onOk: async () => {
      await removeFontFamily({ ids: [id] })
      Message.success('删除成功')
      const index = fontList.value.findIndex(item => item.id === id)
      fontList.value.splice(index, 1)
    },
  });
};

const handleMouseDown = (e, record) => {
  e.preventDefault()
  const items = [
    {
      label: '删除',
      onClick: () => {
        removeFontHandle(record.id)
      }
    }
  ]
  ContextMenu.showContextMenu({
    x: e.x,
    y: e.y,
    items
  })
}

const handleInitSearch = (key = '') => {
  pageConfig.value.pageNum = 0
  fontList.value = []
  sortKey.value = key
  handleSearchMore()
}

const handleSearchMore = async () => {
  pageConfig.value.pageNum += 1
  const list = await getFontListHandle()
  fontList.value = [...fontList.value, ...list]
  canvasEditor.createFontCSS(list, 'myFont')
}

const scrollHeight = ref(0)
onMounted(() => {
  handleSearchMore()
  scrollHeight.value = materialRef.value.clientHeight ? materialRef.value.clientHeight - 52 : 600
})
</script>

<style scoped lang="less">
div.more {
  position: absolute;
  display: none;
}

.item {
  margin-top: 10px;
  padding: 5px;
  background: var(--img-font-bg);
  position: relative;
  border-radius: 8px;
  img {
    height: 30px;
    display: block;
  }
  &:hover {
    cursor: pointer;
    background: #e9e9e9;
    div.more {
      display: block;
      top: 8px;
      right: 5px;
    }
  }
}

.tip {
  text-align: center;
  padding: 10px;
}
.ivu-btn {
  margin-top: 10px;
}
.my-material {
  height: calc(100vh - 45px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}
.my-material-scroll {
  flex: 1;
  margin: 0 0 10px;
}
.c-my-material__top__select {
  position: absolute;
  right: 10px;
  top: 41px;
  z-index: 1000;
  .is-active {
    color: #63a0ed;
  }
}
</style>
