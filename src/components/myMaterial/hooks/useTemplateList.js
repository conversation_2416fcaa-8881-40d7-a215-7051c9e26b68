import { ref, reactive } from 'vue'
import { getTemplateList, getTemplateDetail } from '@/api/v2'
import { useRouter } from 'vue-router'

function useTemplateList() {
  const router = useRouter()
  const pageConfig = reactive({
    pageNum: 0,
    pageSize: 100,
    parentId: '',
    keyword: ''
  })
  const isLoading = ref(false)
  const templateList = ref([])

  const getTemplateListData = async () => {
    isLoading.value = true
    pageConfig.pageNum++
    const params = {
      ...pageConfig
    }
    if (sortKey.value) {
      params[sortKey.value] = sortKey.value.includes('At') ? 'desc' : 'asc'
    }
    const result = await getTemplateList(params)
    templateList.value = [...templateList.value, ...result.data].sort((a, b) => b.type.length - a.type.length)
    isLoading.value = false
  }

  const handleResetSearch = async (parentId = undefined) => {
    pageConfig.pageNum = 0
    pageConfig.parentId = parentId ?? pageConfig.parentId
    templateList.value = []
    await getTemplateListData()
  }

  const sortKey = ref('')
  const handleSort = async (key) => {
    sortKey.value = key
    await handleResetSearch()
  }

  const getTemplateInfo = async (id) => {
    const result = await getTemplateDetail(id)
    const json = result.data.json && JSON.parse(result.data.json)
    json.objects.forEach(item => {
      if (['i-text', 'textbox', 'text'].includes(item.type) && item.fontFamily === '') {
        delete item.fontFamily
      }
    })
    result.data.json = json
    return result.data
  }

  const routerToId = (id) => {
    router.replace('/?id=' + id)
  }

  return {
    templateList,
    sortKey,
    isLoading,
    pageConfig,
    routerToId,
    getTemplateListData,
    handleResetSearch,
    handleSort,
    getTemplateInfo
  }
}

export default useTemplateList
