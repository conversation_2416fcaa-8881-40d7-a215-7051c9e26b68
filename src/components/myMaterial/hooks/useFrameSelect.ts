import { ref, reactive, computed } from 'vue'

function useFrameSelect() {
  const selectedState = ref([])
  const positionList = reactive({
    is_show_mask: false,
    box_screen_left: 65,
    box_screen_top: 128,
    start_x: 0,
    start_y: 0,
    end_x: 0,
    end_y: 0
  })
  const addList = ref<any>([])

  const  mask_width = computed(()=> {
    return `${Math.abs(positionList.end_x - positionList.start_x)}px;`;
  })
  const  mask_height = computed(()=> {
    return `${Math.abs(positionList.end_y - positionList.start_y)}px;`;
  })
  const mask_left = computed(() =>{
    return `${Math.min(positionList.start_x, positionList.end_x) - positionList.box_screen_left}px;`;
  })
  const mask_top = computed(()=> {
    return `${Math.min(positionList.start_y, positionList.end_y) - positionList.box_screen_top}px;`;
  })

  const  handleMouseDown = (event: any)=> {
    if (event.button === 2) return
    positionList.is_show_mask = true
    positionList.start_x = event.clientX
    positionList.start_y = event.clientY
    positionList.end_x = event.clientX
    positionList.end_y = event.clientY
    document.body.addEventListener('mousemove', handleMouseMove) //监听鼠标移动事件
    document.body.addEventListener('mouseup', handleMouseUp) //监听鼠标抬起事件
  }

  function  handleMouseMove(event: any) {
    positionList.end_x = event.clientX;
    positionList.end_y = event.clientY;
  }

  function handleMouseUp() {
    document.body.removeEventListener('mousemove', handleMouseMove)
    document.body.removeEventListener('mouseup', handleMouseUp)
    positionList.is_show_mask = false
    handleDomSelect()
    resSetXY()
  }

  function handleDomSelect() {
    const dom_mask = window.document.querySelector('.mask')
    const rect_select = dom_mask?.getClientRects()[0]
    const add_list: any[] = []
    document.querySelectorAll('.file-item').forEach((node, index) => {
      const rects = node.getClientRects()[0]
      if (collide(rects, rect_select)) {
        add_list.push(node.getAttribute('data-id'))
      }
    })
    addList.value = add_list
  }

  function  collide(rect1: any, rect2: any): boolean {
    const maxX = Math.max(rect1.x + rect1.width, rect2.x + rect2.width);
    const maxY = Math.max(rect1.y + rect1.height, rect2.y + rect2.height);
    const minX = Math.min(rect1.x, rect2.x);
    const minY = Math.min(rect1.y, rect2.y);
    return maxX - minX <= rect1.width + rect2.width && maxY - minY <= rect1.height + rect2.height;
  }

  function  resSetXY() {
    positionList.start_x = 0;
    positionList.start_y = 0;
    positionList.end_x = 0;
    positionList.end_y = 0;
  }

  return {
    selectedState,
    positionList,
    mask_width,
    mask_height,
    mask_left,
    mask_top,
    addList,
    handleMouseDown
  }
}

export {
  useFrameSelect,
}
