<!-- file: 文字底片 -->
<template>
  <Tooltip content="文字底片" v-if="mixinState.mSelectMode === 'one' && textType.includes(mixinState.mSelectOneType)">
    <Button long @click="handlePicture" icon="logo-angular" type="text"></Button>
  </Tooltip>
  <Tooltip content="图片底片" v-if="mixinState.mSelectMode === 'one' && imgType.includes(mixinState.mSelectOneType)">
    <Button long @click="handlePicture" icon="md-photos" type="text"></Button>
  </Tooltip>
  <Modal
    v-model="cropperVisible"
    title="图片裁剪"
    width="800px"
    @on-ok="handleOk"
    @on-cancel="handleCancel">
      <div class="c-vue-cropper">
        <vue-cropper
          v-if="cropperVisible"
          ref="cropperRef"
          :img="options.img"
          :info="true"
          :autoCrop="options.autoCrop"
          :autoCropWidth="options.autoCropWidth"
          :autoCropHeight="options.autoCropHeight"
          :outputType="options.outputType"
          :fixedBox="true"
          :full="true"
          :maxImgSize="5000"
          :infoTrue="true"
          @realTime="handleImgLoad"
        />

      </div>
      <div>
        <div class="c-cropper-input__text">裁剪框大小调整</div>
        <div class="c-cropper-input">
          <div class="c-cropper-input__num">倍数：<Slider style="width: 200px" v-model="copperInfo.sliderValue" :step="0.01" :min="0" :max="4" @on-input="handleChange"></Slider></div>
          <!--
          <div class="c-cropper-input">
            <div class="c-cropper-input" style="margin: 0 20px">
              <span>长：</span>
              <span><Input v-model="copperInfo.width" type="number" /></span>
            </div>
            <div class="c-cropper-input">
              <span>宽：</span>
              <span><Input v-model="copperInfo.height" type="number" /></span>
            </div>
          </div>
          -->
        </div>
      </div>
  </Modal>
</template>

<script name="PictureText" setup>
import useSelect from '@/hooks/select'
import 'vue-cropper/dist/index.css'
import { VueCropper } from 'vue-cropper'
import { cos } from '@/utils/cos'
import useClamp from '@/hooks/useClamp'
import dayjs from 'dayjs'
import Loading from '@/components/load'

const { mixinState, canvasEditor, fabric } = useSelect()
const { dataURLtoFile } = useClamp()

const copperInfo = reactive({
  sliderValue: 1,
  width: 0,
  height: 0
})
const handleChange = (num) => {
  options.autoCropWidth = num * oldCrop.autoCropWidth
  options.autoCropHeight = num * oldCrop.autoCropHeight
}

const textType = ['i-text', 'textbox', 'text']
const imgType= ['image']
const imagePath = ref('')
const handlePicture = () => {
  const { scaleX, scaleY, width, height} = canvasEditor.canvas.getActiveObject()
  const input = document.createElement('input')
  input.type = 'file'
  input.onchange = (e) => {
    const file = e.target.files[0]
    options.img = URL.createObjectURL(file)
    options.autoCropWidth = width * scaleX
    options.autoCropHeight = height * scaleY
    cropperVisible.value = true
    options.fixedBox = imgType.includes(mixinState.mSelectOneType)
  }
  input.click()
}

const cropperVisible = ref(false)

const replaceImg = (img) => {
  fabric.Image.fromURL(img.src, (image) => {
    const filter = new fabric.Image.filters.BlendImage({
      image,
      mode: 'multiply',
      alpha: 1
    })
    const activeObject = canvasEditor.canvas.getActiveObject()
    activeObject.filters = activeObject.filters.filter(item => !(item instanceof fabric.Image.filters.BlendImage))
    activeObject.filters.push(filter)
    activeObject.applyFilters()
    canvasEditor.canvas.renderAll()
    Loading.hide()
    handleCancel()
  }, { crossOrigin: 'anonymous' })
}

const replaceText = (img) => {
  const pattern = new fabric.Pattern({
    source: img,
    repeat: 'repeat',
    offsetX: img.width
  })
  const activeObject = canvasEditor.canvas.getActiveObject()
  activeObject.set('fill', pattern)
  canvasEditor.canvas.renderAll()
  Loading.hide()
  handleCancel()
}

const handleCancel = () => {
  cropperVisible.value = false
  copperImg.width = 0
  copperImg.height = 0
  imagePath.value = ''
}

const options = reactive({
  img: null, // 裁剪图片的地址
  autoCropWidth: 200, // 默认生成截图框宽度 默认容器的 80%
  autoCropHeight: 200, // 默认生成截图框高度 默认容器的 80%
  outputType: 'png', // 裁剪生成图片的格式 jpeg, png, webp
  autoCrop: true, // 是否默认生成截图框
  fixedBox: true // 固定截图框大小
})
const oldCrop = reactive({
  autoCropWidth: 0,
  autoCropHeight: 200
})

const cropperRef = ref(null)
const handleOk = () => {
  cropperRef.value.getCropData(async data => {
    Loading.show('正在上传图片')
    const file = dataURLtoFile(data, `${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.png`)
    const { Location } = await cos.uploadFileToPoker(file, file.name)
    const img = new Image()
    img.src = imgType.includes(mixinState.mSelectOneType) ? `//${Location}` : data
    img.crossOrigin = 'anonymous'
    img.onload = () => {
      if (imgType.includes(mixinState.mSelectOneType)) {
        replaceImg(img)
      } else {
        replaceText(img)
      }
    }
  })
}

const copperImg = reactive({
  width: 0,
  height: 0
})
const handleImgLoad = data => {
  if (!!data.img && !copperImg.width && data.img.width !== '0px' && data.w) {
    const [, scale] = data.img.transform.match(/scale\(([\d.]+)\)/)
    if (Number(scale) === 1) {
      const { w, h } = data
      const num = w > h ? (h / options.autoCropHeight) : (w / options.autoCropWidth)
      options.autoCropWidth = w > h ? options.autoCropWidth * num : w
      options.autoCropHeight = h > w ? options.autoCropHeight * num : h
    } else {
      const { w, h } = data
      options.autoCropWidth = options.autoCropWidth * scale
      options.autoCropHeight = options.autoCropHeight * scale
      oldCrop.autoCropHeight = options.autoCropHeight
      oldCrop.autoCropWidth = options.autoCropWidth
      if (w < options.autoCropWidth) {
        options.autoCropHeight = (w / options.autoCropWidth) * options.autoCropHeight
        options.autoCropWidth = w
      }

      if (h < options.autoCropHeight) {
        options.autoCropWidth = (h / options.autoCropHeight) * options.autoCropWidth
        options.autoCropHeight = h
      }
    }
    copperImg.width = 1
    copperInfo.width = options.autoCropWidth
    copperInfo.height = options.autoCropHeight
  }
}
</script>

<style>
.cropper {
  z-index: 1000!important;
}
.c-vue-cropper {
  height: 500px;
}
.c-cropper-input {
  display: flex;
  align-items: center;
}
.c-cropper-input__num {
  display: flex;
  align-items: center;
}
.c-cropper-input__text {
  font-size: 16px;
  font-weight: bold;
}

</style>
