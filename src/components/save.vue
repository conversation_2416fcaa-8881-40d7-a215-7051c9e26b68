<template>
  <div class="save-box">
    <Button style="margin-left: 10px" type="text" @click="beforeClear">
      {{ $t('save.empty') }}
    </Button>
    <Dropdown style="margin-left: 10px" @on-click="saveWith">
      <Button type="primary">
        更多
        <Icon type="ios-arrow-down"></Icon>
      </Button>
      <template #list>
        <DropdownMenu>
          <DropdownItem name="saveImg">{{ $t('save.save_as_picture') }}</DropdownItem>
          <DropdownItem name="saveJpg">JPG图片</DropdownItem>
          <DropdownItem name="saveSvg">{{ $t('save.save_as_svg') }}</DropdownItem>
          <DropdownItem name="saveTif">TIF图片</DropdownItem>
          <DropdownItem name="saveImg300">DPI300图片</DropdownItem>
          <DropdownItem name="exportJson" divided>{{ $t('save.export_as_json') }}</DropdownItem>
          <DropdownItem name="importJson">{{ $t('save.import_as_json') }}</DropdownItem>
          <DropdownItem name="password" divided>更改密码</DropdownItem>
          <DropdownItem name="loginOut" divided>退出登录</DropdownItem>
        </DropdownMenu>
      </template>
    </Dropdown>

    <PasswordModal v-model="passwordVisible" />
  </div>
</template>

<script setup name="save-bar">
import {Message, Modal} from 'view-ui-plus'
import { exportFile } from '@/api/v2'
import useSelect from '@/hooks/select'
import { debounce } from 'lodash-es'
import { useI18n } from 'vue-i18n'
import { Spin } from 'view-ui-plus'
import { useRoute, useRouter } from 'vue-router'
import PasswordModal from '@/components/system/PasswordModal.vue'
import dayjs from 'dayjs'
const route = useRoute()
const router = useRouter()

const { t } = useI18n();

const passwordVisible = ref(false)

const { canvasEditor } = useSelect();
const cbMap = {
  saveSvg() {
    canvasEditor.saveSvg();
  },
  saveImg() {
    canvasEditor.saveImg();
  },
  saveJpg() {
    canvasEditor.saveJpg();
  },
  saveTif() {
    canvasEditor.saveTif();
  },
  saveImg300() {
    canvasEditor.saveImg300();
  },
  async exportJson() {
    const { data, message } = await exportFile({
      json: JSON.stringify(canvasEditor.getJson(), null, 4)
    })
    if (!data) return Message.error(message || '导出失败')
    let fileName = dayjs().format('YYYY-MM-DD HH:mm:ss');
    if (route.query?.id) {
      const input = document.querySelector('.c-my-template input');
      fileName = input?.value || fileName;
    }
    const link = document.createElement('a');
    link.download = `${fileName}.json`;
    const blob = new Blob([data], { type: 'text/json' });
    link.href = window.URL.createObjectURL(blob);
    link.click();
  },
  importJson() {
    let input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = (event) => {
      let files = event.target.files;
      if (!files || !files.length) {
        input = null;
        throw new Error('No files');
      }

      // 当选择文件后，使用FileReader API读取文件，返回数据
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          Spin.show({
            render: (h) => h('div', t('alert.loading_data')),
          });
          const config = event.target.result;
          canvasEditor.loadJSON(config, Spin.hide);
        } catch (e) {
          Spin.hide();
          throw new Error(e);
        }
        input = null;
      }
      reader.readAsText(files[0]);
    }
    input.click();
  },
  password() {
    passwordVisible.value = true
  },
  loginOut() {
    Modal.confirm({
      title: '提示',
      content: `是否退出登录`,
      onOk: async () => {
        localStorage.clear()
        Message.success('退出成功')
        router.push({
          path: '/login'
        })
      }
    })
  }
};

const saveWith = debounce(function (type) {
  cbMap[type] && typeof cbMap[type] === 'function' && cbMap[type]();
}, 300);

/**
 * @desc clear canvas 清空画布
 */
const clear = () => {
  canvasEditor.clear();
};

const beforeClear = () => {
  Modal.confirm({
    title: t('tip'),
    content: `<p>${t('clearTip')}</p>`,
    okText: t('ok'),
    cancelText: t('cancel'),
    onOk: () => clear(),
  });
};
</script>

<style scoped lang="less">
.save-box {
  display: inline-block;
  padding-right: 10px;
}
</style>
