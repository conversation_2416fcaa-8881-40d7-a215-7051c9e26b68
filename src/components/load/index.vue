<!-- file 加载loading -->
<template>
  <div v-show="isShow" class="c-loading-template">
    <div class="c-loading">
      <div></div>
      <div></div>
      <div></div>
      <div></div>
    </div>
    <div class="c-load-text">{{ loadText }}</div>
  </div>
</template>

<script setup name="Loading">
const isShow = ref(false)

const loadText = ref('')
const show = (text) => {
  loadText.value = text || '加载中'
  isShow.value = true
}

const hide = () => {
  isShow.value = false
}

defineExpose({ show, hide })
</script>

<style lang="less">
@main: #2d8cf0;

.c-loading-template {
  position: fixed;
  z-index: 1000;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(255, 255, 255, .7);
  backdrop-filter: blur(3px);
}
.c-load-text {
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: 40px;
  font-size: 16px;
  font-weight: 600;
  transform: translate(-50%, -50%);
}
@keyframes text-change {
  0% { content: "."; }
  33% { content: ".."; }
  66% { content: "..."; }
}
.c-load-text::after {
  content: '.';
  animation: text-change 2s infinite;
}
.c-loading {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.c-loading,
.c-loading > div {
  position: relative;
  box-sizing: border-box;
}

.c-loading {
  display: block;
  font-size: 0;
  color: @main;
}

.c-loading.la-dark {
  color: #333;
}

.c-loading > div {
  display: inline-block;
  float: none;
  background-color: currentColor;
  border: 0 solid currentColor;
}

.c-loading {
  width: 8px;
  height: 8px;
}

.c-loading > div {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 100%;
  transform: translate(-50%, -50%);
  animation: ball-fussion-ball1 1s 0s ease infinite;
}

.c-loading > div:nth-child(1) {
  top: 0;
  left: 50%;
  z-index: 1;
}

.c-loading > div:nth-child(2) {
  top: 50%;
  left: 100%;
  z-index: 2;
  animation-name: ball-fussion-ball2;
}

.c-loading > div:nth-child(3) {
  top: 100%;
  left: 50%;
  z-index: 1;
  animation-name: ball-fussion-ball3;
}

.c-loading > div:nth-child(4) {
  top: 50%;
  left: 0;
  z-index: 2;
  animation-name: ball-fussion-ball4;
}

.c-loading.la-sm {
  width: 4px;
  height: 4px;
}

.c-loading.la-sm > div {
  width: 6px;
  height: 6px;
}

.c-loading.la-2x {
  width: 16px;
  height: 16px;
}

.c-loading.la-2x > div {
  width: 24px;
  height: 24px;
}

.c-loading.la-3x {
  width: 24px;
  height: 24px;
}

.c-loading.la-3x > div {
  width: 36px;
  height: 36px;
}

@keyframes ball-fussion-ball2 {
  0% {
    opacity: 0.35;
  }

  50% {
    top: 200%;
    left: 200%;
    opacity: 1;
  }

  100% {
    top: 100%;
    left: 50%;
    z-index: 1;
    opacity: 0.35;
  }
}

@keyframes ball-fussion-ball1 {
  0% {
    opacity: 0.35;
  }

  50% {
    top: -100%;
    left: 200%;
    opacity: 1;
  }

  100% {
    top: 50%;
    left: 100%;
    z-index: 2;
    opacity: 0.35;
  }
}

@keyframes ball-fussion-ball3 {
  0% {
    opacity: 0.35;
  }

  50% {
    top: 200%;
    left: -100%;
    opacity: 1;
  }

  100% {
    top: 50%;
    left: 0;
    z-index: 2;
    opacity: 0.35;
  }
}

@keyframes ball-fussion-ball4 {
  0% {
    opacity: 0.35;
  }

  50% {
    top: -100%;
    left: -100%;
    opacity: 1;
  }

  100% {
    top: 0;
    left: 50%;
    z-index: 1;
    opacity: 0.35;
  }
}
</style>
