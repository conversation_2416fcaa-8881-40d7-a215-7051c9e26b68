import Loading from './index.vue'
import { createApp, getCurrentInstance, h } from 'vue'

Loading.newInstance = function () {
  let _instance = null
  const Instance = createApp({
    render() {
      return h(Loading, { ref: 'loadingRef' })
    },
    created () {
      _instance = getCurrentInstance()
    }
  })
  const container = document.createElement('div')
  document.body.appendChild(container)
  Instance.mount(container)
  const load = _instance.refs.loadingRef

  return {
    show(text) {
      load.show(text)
    },
    hide() {
      load.hide()
    }
  }
}

let loadComp = null

export default {
  show(text) {
    if (!loadComp) {
      loadComp = Loading.newInstance()
    }
    loadComp.show(text)
  },
  hide() {
    if (loadComp) {
      loadComp.hide()
    }
  }
}
