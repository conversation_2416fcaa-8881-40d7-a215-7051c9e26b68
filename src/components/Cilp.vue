<!-- file 裁剪 --->
<template>
  <Divider plain orientation="left">
    <h4>裁剪调整</h4>
  </Divider>
  <div class="bg-item c-size-container">
    <div class="c-size-container ivu-mr-8">
      <span class="ivu-mr-8">长: </span><Input v-model="size.width" />
    </div>
    <div class="c-size-container ivu-mr-8">
      <span class="ivu-mr-8">宽: </span><Input v-model="size.height" />
    </div>
    <Button type="primary" @click="handleOk">调整裁剪</Button>
  </div>
</template>

<script setup name="Clip">
import useSelect from '@/hooks/select'

const { canvasEditor } = useSelect()

const emits = defineEmits(['cancel'])

const props = defineProps({
  record: {
    type: Object,
    default: () => {},
  }
})

let shell = null
let clipPath = null
let activeObject = null
let canvas = null
const size = ref({ width: 0, height: 0 })
const handleInitClipRecord = (record) => {
  console.log(record)
  if (!record) return
  shell = record.shell
  clipPath = record.clipPath
  activeObject = record.activeObject
  canvas = record.canvas
  size.value.width = Math.ceil(shell.width)
  size.value.height = Math.ceil(shell.height)
}

const handleOk = () => {
  const scaleX = size.value.width / shell.width
  const scaleY = size.value.height / shell.height
  shell.set({ scaleX, scaleY })
  clipPath.set({ scaleX, scaleY })
  // shell.setPositionByOrigin(shell.getCenterPoint(), 'center', 'center');
  clipPath.setPositionByOrigin(shell.getCenterPoint(), 'center', 'center');
  activeObject.set('dirty', true)
  canvas.renderAll()
}

const handleCancel = () => {
  shell = null
  clipPath = null
  activeObject = null
  canvas = null
  size.value = { width: 0, height: 0 }
  emits('cancel')
}

onMounted(() => {
  canvasEditor.on('selectCancel', handleCancel)
  handleInitClipRecord(props.record)
});

onBeforeUnmount(() => {
  canvasEditor.off('selectCancel', handleCancel)
});
</script>

<style lang="less" scoped>
.attr-item-box {
  margin-top: 8px;
}
.c-size-container {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
