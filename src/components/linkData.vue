<template>
  <div
    class="box attr-item-box"
    v-if="mixinState.mSelectMode === 'one' && matchType.includes(mixinState.mSelectOneType)"
  >
    <Divider plain orientation="left"><h4>关联数据</h4></Divider>

    <Row :gutter="10">
      <Col flex="1">
        <Select
          v-if="columns.length"
          v-model="baseAttr.linkData[1]"
          filterable
          allow-create
          @on-change="changeCommon"
          clearable
        >
          <Option :value="item.key" v-for="item in columns" :key="item.key"></Option>
        </Select>
        <Input v-else disabled v-model="baseAttr.linkData[1]" />
      </Col>
    </Row>
  </div>
</template>

<script setup name="AttrBute">
import useSelect from '@/hooks/select';

const update = getCurrentInstance();
const { mixinState, canvasEditor } = useSelect();

const matchType = ref(['text', 'i-text', 'textbox', 'image']);
const baseAttr = reactive({
  linkData: ['', ''],
});

const columns = ref([]);
const getObjectAttr = (e) => {
  const activeObject = canvasEditor.canvas.getActiveObject();
  if (e && e.target && e.target !== activeObject) return;
  if (activeObject && matchType.value.includes(mixinState.mSelectOneType)) {
    baseAttr.linkData = activeObject.get('linkData') || ['', ''];
    columns.value = canvasEditor.getColumns();
  }
};

const changeCommon = () => {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  if (activeObject) {
    const { type } = activeObject;
    const typeMap = {
      image: 'src',
      text: 'text',
      'i-text': 'text',
      textbox: 'text',
    };
    const key = typeMap[type];
    console.log(typeMap[type]);
    activeObject.set('linkData', [key, baseAttr.linkData[1]]);
    canvasEditor.canvas.renderAll();
  }
};

const selectCancel = () => {
  update?.proxy?.$forceUpdate();
};

onMounted(() => {
  getObjectAttr();
  canvasEditor.on('selectCancel', selectCancel);
  canvasEditor.on('selectOne', getObjectAttr);
  canvasEditor.canvas.on('object:modified', getObjectAttr);
});

onBeforeUnmount(() => {
  canvasEditor.off('selectCancel', selectCancel);
  canvasEditor.off('selectOne', getObjectAttr);
  canvasEditor.canvas.off('object:modified', getObjectAttr);
});
</script>

<style scoped lang="less">
:deep(.ivu-input-number) {
  display: block;
  width: 100%;
}

.ivu-form-item {
  background: #f6f7f9;
  border-radius: 5px;
  padding: 0 5px;
  margin-bottom: 10px;
}

.ivu-row {
  margin-bottom: 10px;
}
</style>
