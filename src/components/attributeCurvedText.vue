<template>
  <div class="box attr-item-box" v-if="isOne && isMatchType">
    <div>
      <Row :gutter="10">
        <Col flex="1">
          <InputNumber
            v-model="baseAttr.diameter"
            @on-change="(value) => changeCommon('diameter', value)"
            append="曲线"
          ></InputNumber>
        </Col>
        <Col flex="1">
          <InputNumber
            v-model="baseAttr.kerning"
            @on-change="(value) => changeCommon('kerning', value)"
            append="间距"
          ></InputNumber>
        </Col>
      </Row>

      <Row :gutter="10">
        <Col flex="1">
          <Input
            v-model="baseAttr.text"
            maxlength="100"
            show-word-limit
            @on-change="changeCommon('text', baseAttr.text)"
            type="textarea"
            placeholder="请输入文字"
          />
        </Col>
      </Row>
    </div>
  </div>
</template>

<script setup name="AttrBute">
import useSelect from '@/hooks/select';
import InputNumber from '@/components/inputNumber';
const { isOne, isMatchType, canvasEditor } = useSelect(['curved-text']);

const baseAttr = reactive({
  diameter: 0,
  kerning: 0,
  text: '',
  fill: '',
  fontSize: 0,
});

const getObjectAttr = () => {
  const activeObject = canvasEditor.canvas.getActiveObject();
  if (activeObject && isMatchType) {
    baseAttr.text = activeObject.get('text');
    baseAttr.diameter = activeObject.get('diameter');
    baseAttr.kerning = activeObject.get('kerning');
    baseAttr.fill = activeObject.get('fill');
    baseAttr.fontSize = activeObject.get('fontSize');
  }
};

const changeCommon = (key, value) => {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  if (activeObject) {
    activeObject.set(key, value);
    canvasEditor.canvas.renderAll();
  }
};

const update = getCurrentInstance();
const selectCancel = () => {
  update?.proxy?.$forceUpdate();
};

onMounted(() => {
  getObjectAttr();
  canvasEditor.on('selectCancel', selectCancel);
  canvasEditor.on('selectOne', getObjectAttr);
  canvasEditor.canvas.on('object:modified', getObjectAttr);
});

onBeforeUnmount(() => {
  canvasEditor.off('selectCancel', selectCancel);
  canvasEditor.off('selectOne', getObjectAttr);
  canvasEditor.canvas.off('object:modified', getObjectAttr);
});
</script>

<style scoped lang="less">
.content-box {
  display: flex;
  flex-wrap: wrap;
}

.poptip-btn {
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 100%;
  font-size: 14px;
  font-weight: bold;

  span {
    font-size: 30px;
    color: #fed835;
    -webkit-text-stroke: 1px #000;
    text-shadow: 1px 1px #333;
    font-weight: bold;
    background: rgb(255, 255, 255);
    border-radius: 10px;
    margin: 5px;
    width: 50px;
    text-align: center;
    margin-right: 15px;
  }
}
.img-item {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background: rgb(241, 242, 244);
  border-radius: 10px;
  cursor: pointer;
  margin-right: 10px;
  margin-bottom: 10px;

  &:hover {
    background: #e8e8e9;
  }
  img {
    width: 50%;
  }
}

:deep(.ivu-poptip) {
  display: block;
}
:deep(.ivu-poptip-rel) {
  display: block;
}
:deep(.ivu-poptip-body) {
  padding: 10px 0 0 10px;
}
:deep(.ivu-input-number) {
  display: block;
  width: 100%;
}

:deep(.ivu-color-picker) {
  display: block;
}
.ivu-row {
  margin-bottom: 8px;
  .ivu-col {
    position: inherit;
    &__box {
      display: flex;
      align-items: center;
      background: #f8f8f8;
      border-radius: 4px;
      gap: 8px;
    }
  }

  .label {
    padding-left: 8px;
  }
  .content {
    flex: 1;
    :deep(.--input),
    :deep(.ivu-select-selection) {
      background-color: transparent;
      border: none !important;
      box-shadow: none !important;
    }
  }
}
</style>
