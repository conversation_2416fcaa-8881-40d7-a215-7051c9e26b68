<!-- file 夹具功能 -->
<template>
  <div class="c-fixture" v-if="mixinState.mSelectMode === 'one' && state.type === 'image'">
    <Divider plain orientation="left">
      <h4>夹具功能</h4>
    </Divider>
    <div class="bg-item">
      <Button @click="handleFixture" type="text" long>画板平铺</Button>
    </div>
    <Modal v-model="visible" width="800px" title="画板平铺">
      <div class="c-fixture-form">
        <Form ref="formValidateRef" inline :model="formValidate" :rules="ruleValidate" :label-width="80">
          <FormItem label="上下间距" prop="marginY">
            <Input v-model.trim="formValidate.marginY" type="number" placeholder="请输入"></Input>
          </FormItem>
          <FormItem label="左右间距" prop="marginX">
            <Input v-model.trim="formValidate.marginX" type="number" placeholder="请输入"></Input>
          </FormItem>
        </Form>
      </div>
      <template #footer>
        <Button @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleOk">确定</Button>
      </template>
    </Modal>
  </div>
</template>

<script setup name="Fixture">
import useSelect from '@/hooks/select'
import useMaterial from '@/hooks/useMaterial'
import dayjs from 'dayjs'
import { cos } from '@/utils/cos'
import { v4 as uuid } from 'uuid'
const { dataURLtoFile } = useMaterial()

const { mixinState, canvasEditor, fabric } = useSelect()

const state = reactive({
  type: ''
})

const handleSelectOne = () => {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0]
  if (activeObject && activeObject.type === 'image') {
    state.type = activeObject.type
  }
}

const visible = ref(false)
const formValidateRef = ref(null)
const formValidate = reactive({
  marginX: '',
  marginY: ''
})
const ruleValidate = reactive({
  marginX: { required: true, message: '请输入间距', trigger: 'change' },
  marginY: { required: true, message: '请输入间距', trigger: 'change' }
})
const handleFixture = () => {
  visible.value = true
}

const handleOk = () => {
  formValidateRef.value.validate((valid) => {
    if (!valid) return
    const currentClipPath = canvasEditor.canvas.clipPath
    const canvasInfo = {
      width: currentClipPath.width,
      height: currentClipPath.height
    }
    const activeObject = canvasEditor.canvas.getActiveObjects()[0]
    const marginX = Number(formValidate.marginX)
    const marginY = Number(formValidate.marginY)
    // 当前图片缩放比例
    const { scaleX, scaleY } = activeObject
    // 当前图片大小
    const width = Math.round(activeObject.getScaledWidth()) + marginX
    const height = Math.round(activeObject.getScaledHeight()) + marginY
    // x 轴和 y 轴 需要画的数量
    const xCount = Math.floor(canvasInfo.width / width)
    const yCount = Math.floor(canvasInfo.height / height)
    for (let i = 0; i < yCount; i++) {
      for (let j = 0; j < xCount; j++) {
        if (j === 0 && i === 0) continue
        createImg(activeObject.getSrc(), { left: j * width + marginX, top: i * height + marginY, scaleX, scaleY })
      }
    }
    // 调整当前图片位置
    activeObject.set('left', marginX)
    activeObject.set('top', marginY)
    canvasEditor.canvas.renderAll()
    visible.value = false
  })
}

let imgElement = null
const createImg = (src, options) => {
  let imgInstance = null
  if (src.toString().includes('http')) {
    fabric.Image.fromURL(src, (img) => {
      img.set({
        ...options,
        id: uuid()
      })
      canvasEditor.canvas.add(img)
      canvasEditor.canvas.requestRenderAll()
    }, { crossOrigin: 'anonymous' })
  } else {
    imgElement.src = src
    imgInstance = new fabric.Image(imgElement, options)
    canvasEditor.canvas.add(imgInstance)
  }
}

const handleCancel = () => {
  visible.value = false
  formValidateRef.value.resetFields()
}

onMounted(() => {
  canvasEditor.on('selectOne', handleSelectOne)
  imgElement = document.createElement('img')
})

onBeforeUnmount(() => {
  canvasEditor.off('selectOne', handleSelectOne)
  imgElement = null
})
</script>
