<template>
  <Modal
    v-model="visible"
    :title="isEdit ? '编辑用户' : '新增用户'"
    :width="600"
    :mask-closable="false"
    :closable="true"
    class="user-form-modal"
    @on-cancel="handleCancel"
  >
    <Form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="100"
      class="user-form"
    >
      <FormItem label="邮箱" prop="email">
        <Input
          v-model="formData.email"
          placeholder="请输入邮箱地址"
          clearable
          prefix="ios-mail"
        />
      </FormItem>

      <FormItem label="昵称" prop="nickName">
        <Input
          v-model="formData.nickName"
          placeholder="请输入昵称"
          clearable
          prefix="ios-contact"
        />
      </FormItem>

      <FormItem label="账号" prop="userName">
        <Input
          v-model="formData.userName"
          placeholder="请输入账号"
          clearable
          prefix="ios-contact"
          :disabled="!!isEdit"
        />
      </FormItem>

      <FormItem label="密码" prop="password">
        <Input
          v-model="formData.password"
          type="password"
          placeholder="请输入密码"
          clearable
          show-password
          prefix="ios-lock"
        />
      </FormItem>

      <FormItem label="确认密码" prop="userPassword">
        <Input
          v-model="formData.userPassword"
          type="password"
          placeholder="请再次输入密码"
          clearable
          show-password
          prefix="ios-lock"
        />
      </FormItem>

      <FormItem label="账号类型" prop="userType">
        <Select
          v-model="formData.userType"
          placeholder="请选择账号类型"
          :disabled="!!isEdit"
        >
          <Option value="-1">
            <span class="option-item">
              <Icon type="ios-globe" color="#52c41a" />
              外部用户
            </span>
          </Option>
          <Option value="1">
            <span class="option-item">
              <Icon type="ios-people" color="#1890ff" />
              内部用户
            </span>
          </Option>
        </Select>
      </FormItem>
    </Form>

    <template #footer>
      <div class="modal-footer">
        <Button @click="handleCancel" class="cancel-btn">
          <Icon type="ios-close" />
          取消
        </Button>
        <Button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
          class="submit-btn"
        >
          <Icon :type="isEdit ? 'ios-checkmark' : 'ios-add'" />
          {{ isEdit ? '更新' : '创建' }}
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { Encrypt } from '@/contants'

// 重置表单
const resetForm = () => {
  formData.email = ''
  formData.userName = ''
  formData.nickName = ''
  formData.password = ''
  formData.userPassword = ''
  formData.userType = 1
}


// Props
interface Props {
  modelValue: boolean
  userData?: any | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  userData: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [data: any, isEdit: boolean]
}>()

// 响应式数据
const formRef = ref()
const submitLoading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.userData)

// 表单数据
const formData = reactive({
  email: '',
  userName: '',
  nickName: '',
  password: '',
  userPassword: '',
  userType: 1
})

// 表单验证规则
const formRules = {
  userName: [
    { required: true, message: '请输入账号', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
  ],
  userPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== formData.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  userType: [
    { required: true, message: '请选择账号类型', trigger: 'change' }
  ]
}

// 监听用户数据变化，用于编辑回显
watch(
  () => props.userData,
  (newData) => {
    if (newData) {
      // 编辑模式，回显数据
      formData.email = newData.email || ''
      formData.userName = newData.userName || ''
      formData.nickName = newData.nickName || ''
      formData.userType = newData.userType
      formData.password = ''
      formData.userPassword = ''
    } else {
      // 新增模式，重置表单
      resetForm()
    }
  },
  { immediate: true }
)

// 处理取消
const handleCancel = () => {
  visible.value = false
  resetForm()
}

// 处理提交
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitLoading.value = true

    // 准备提交数据
    const submitData = {
      email: formData.email,
      userName: formData.userName,
      userType: formData.userType,
      nickName: formData.nickName,
      password: Encrypt(formData.password.trim())
    }

    // 新增模式需要密码
    if (isEdit.value) {
      submitData.id = props.userData.id
    }

    // 触发提交事件
    emit('submit', submitData, isEdit.value)

    // 提交成功后关闭弹窗
    visible.value = false
    resetForm()

  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 暴露方法
defineExpose({
  resetForm
})
</script>

<style lang="less" scoped>
.user-form-modal {
  :deep(.ivu-modal-content) {
    background: var(--modal-bg);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  :deep(.ivu-modal-header) {
    background: linear-gradient(135deg, var(--modal-bg) 0%, var(--bg-color-secondary) 100%);
    border-bottom: 1px solid var(--border-color-light);
    padding: 24px 32px 20px;

    .ivu-modal-header-inner {
      color: var(--text-color);
      font-size: 18px;
      font-weight: 600;
    }
  }

  :deep(.ivu-modal-body) {
    background: var(--modal-bg);
    padding: 32px;
  }

  :deep(.ivu-modal-footer) {
    background: var(--modal-bg);
    border-top: 1px solid var(--border-color-light);
    padding: 20px 32px;
  }
}

.user-form {
  :deep(.ivu-form-item) {
    margin-bottom: 24px;

    .ivu-form-item-label {
      color: var(--text-color);
      font-weight: 600;
      font-size: 14px;
    }

    .ivu-form-item-content {
      .ivu-input,
      .ivu-select {
        .ivu-input,
        .ivu-select-selection {
          background: var(--input-bg);
          border: 1px solid var(--border-color);
          border-radius: 8px;
          color: var(--text-color);
          transition: all 0.3s ease;

          &:hover {
            border-color: #1890ff;
          }

          &:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .ivu-input::placeholder,
        .ivu-select-placeholder {
          color: var(--text-color-secondary);
        }
      }
    }

    .ivu-form-item-error-tip {
      color: #f5222d;
      font-size: 12px;
      margin-top: 4px;
    }
  }
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;

  .cancel-btn {
    background: var(--button-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;

    &:hover {
      background: var(--button-hover-bg);
      border-color: var(--border-color);
      transform: translateY(-1px);
    }
  }

  .submit-btn {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    border: none;
    border-radius: 8px;
    color: white;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);
    }

    &.ivu-btn-loading {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    }
  }
}

// 下拉菜单样式
:deep(.ivu-select-dropdown) {
  background: var(--modal-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

  .ivu-option {
    color: var(--text-color);
    padding: 12px 16px;
    transition: all 0.2s ease;

    &:hover {
      background: var(--button-hover-bg);
    }

    &.ivu-option-selected {
      background: rgba(24, 144, 255, 0.1);
      color: #1890ff;
    }
  }
}
</style>
