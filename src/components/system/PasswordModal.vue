<template>
  <Modal
    v-model="visible"
    title="更改密码"
    :width="500"
    :mask-closable="false"
    :closable="true"
    class="password-modal"
    @on-ok="handleSubmit"
    @on-cancel="handleCancel"
  >
    <Form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="100"
      class="password-form"
    >
      <FormItem label="新密码" prop="password">
        <Input
          v-model="formData.password"
          type="password"
          placeholder="请输入新密码"
          clearable
          show-password
          prefix="ios-lock"
        />
      </FormItem>

      <FormItem label="确认密码" prop="confirmPassword">
        <Input
          v-model="formData.confirmPassword"
          type="password"
          placeholder="请再次输入新密码"
          clearable
          show-password
          prefix="ios-lock"
        />
      </FormItem>
    </Form>

    <template #footer>
      <div class="modal-footer">
        <Button @click="handleCancel" class="cancel-btn">
          <Icon type="ios-close" />
          取消
        </Button>
        <Button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
          class="submit-btn"
        >
          <Icon type="ios-checkmark" />
          确认修改
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { Message } from 'view-ui-plus'
import { updatePassword } from '@/api/v2'
import { Encrypt } from '@/contants'

// Props
interface Props {
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [password: string]
}>()

// 响应式数据
const formRef = ref()
const submitLoading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const formData = reactive({
  password: '',
  confirmPassword: ''
})

// 表单验证规则
const formRules = {
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== formData.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 监听弹窗显示状态
watch(visible, (newVisible) => {
  if (newVisible) {
    resetForm()
    nextTick(() => {
      formRef.value?.resetFields()
    })
  }
})

// 重置表单
const resetForm = () => {
  formData.password = ''
  formData.confirmPassword = ''
}

// 处理取消
const handleCancel = () => {
  visible.value = false
  resetForm()
}

// 处理提交
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    // 二次确认密码是否一致
    if (formData.password !== formData.confirmPassword) {
      Message.error('两次输入的密码不一致')
      return
    }

    submitLoading.value = true

    // 触发提交事件
    await updatePassword({
      newPassword: Encrypt(formData.password.trim()),
      oldPassword: Encrypt(formData.confirmPassword.trim())
    })

    Message.success('密码修改成功')

    // 提交成功后关闭弹窗
    visible.value = false
    resetForm()

  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 暴露方法
defineExpose({
  resetForm
})
</script>

<style lang="less" scoped>
.password-modal {
  :deep(.ivu-modal-content) {
    background: var(--modal-bg);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  :deep(.ivu-modal-header) {
    background: linear-gradient(135deg, var(--modal-bg) 0%, var(--bg-color-secondary) 100%);
    border-bottom: 1px solid var(--border-color-light);
    padding: 24px 32px 20px;

    .ivu-modal-header-inner {
      color: var(--text-color);
      font-size: 18px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '🔒';
        font-size: 20px;
      }
    }
  }

  :deep(.ivu-modal-body) {
    background: var(--modal-bg);
    padding: 32px;
  }

  :deep(.ivu-modal-footer) {
    background: var(--modal-bg);
    border-top: 1px solid var(--border-color-light);
    padding: 20px 32px;
  }
}

.password-form {
  :deep(.ivu-form-item) {
    margin-bottom: 24px;

    .ivu-form-item-label {
      color: var(--text-color);
      font-weight: 600;
      font-size: 14px;
    }

    .ivu-form-item-content {
      .ivu-input {
        background: var(--input-bg);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        color: var(--text-color);
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
        }

        &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        &::placeholder {
          color: var(--text-color-secondary);
        }
      }
    }

    .ivu-form-item-error-tip {
      color: #f5222d;
      font-size: 12px;
      margin-top: 4px;
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;

  .cancel-btn {
    background: var(--button-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;

    &:hover {
      background: var(--button-hover-bg);
      border-color: var(--border-color);
      transform: translateY(-1px);
    }
  }

  .submit-btn {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    border: none;
    border-radius: 8px;
    color: white;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, #73d13d 0%, #95de64 100%);
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(82, 196, 26, 0.4);
    }

    &.ivu-btn-loading {
      background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    }
  }
}
</style>
