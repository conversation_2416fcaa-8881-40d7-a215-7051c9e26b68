<template>
  <div class="virtual-font-select" ref="selectRef">
    <!-- 选择器触发器 -->
    <div
      class="font-select-trigger"
      :class="{ 'is-open': isOpen }"
      @click="toggleDropdown"
    >
      <div class="selected-font">
        <div v-if="selectedFont" class="selected-font-name">
          {{ selectedFont.name }}
        </div>
        <div v-else class="placeholder">选择字体</div>
      </div>
      <Icon type="ios-arrow-down" class="arrow-icon" />
    </div>

    <!-- 下拉面板 -->
    <Teleport to="body">
      <div
        v-if="isOpen"
        class="font-select-dropdown"
        :style="dropdownStyle"
        @click.stop
      >
        <!-- 搜索框 -->
        <div class="search-box">
          <Input
            v-model="searchQuery"
            placeholder="搜索字体..."
            prefix="ios-search"
            @input="onSearch"
            clearable
          />
        </div>

        <!-- 历史记录 -->
        <div v-if="historyFont.length > 0" class="history-section">
          <h3 class="section-title">字体使用记录</h3>
          <div class="history-fonts">
            <div
              v-for="item in historyFont"
              :key="item.id"
              class="font-item font-item__history"
              :class="{ 'font-item__active': selectedFont && selectedFont.id === item.id }"
              :style="item.img ? `background-image:url('${item.img}');` : ''"
              @click="selectFont(item)"
            >
              {{ item.img ? '' : item.name }}
            </div>
          </div>
        </div>

        <!-- 虚拟滚动字体列表 -->
        <div class="virtual-list-container">
          <h3 v-if="historyFont.length > 0" class="section-title">字体搜索定位</h3>
          <RecycleScroller
            ref="recycleScrollerRef"
            class="font-scroller"
            :items="filteredFonts"
            :item-size="50"
            key-field="id"
            v-slot="{ item }"
          >
            <div
              class="font-item"
              :class="{ 'font-item__active': selectedFont && selectedFont.id === item.id }"
              :style="item.img ? `background-image:url('${item.img}');` : ''"
              @click="selectFont(item)"
            >
              {{ item.img ? '' : item.name }}
            </div>
          </RecycleScroller>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup name="VirtualFontSelect">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { Input, Icon } from 'view-ui-plus'

// Props
const props = defineProps({
  modelValue: {
    type: [String, Object],
    default: null
  },
  options: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: '选择字体'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'search', 'open'])

// Refs
const selectRef = ref(null)
const recycleScrollerRef = ref(null)
const isOpen = ref(false)
const searchQuery = ref('')
const dropdownStyle = ref({})

// 历史字体记录
const historyFont = ref([])

// 初始化历史记录
onMounted(() => {
  try {
    const stored = localStorage.getItem('history-font')
    if (stored) {
      historyFont.value = JSON.parse(stored)
    }
  } catch (error) {
    console.error('Failed to load font history:', error)
    historyFont.value = []
  }
})

// 当前选中的字体
const selectedFont = computed(() => {
  if (!props.modelValue) return null

  if (typeof props.modelValue === 'string') {
    return props.options.find(font => font.name === props.modelValue)
  }

  return props.modelValue
})

// 过滤后的字体列表
const filteredFonts = computed(() => {
  if (!searchQuery.value) {
    return props.options
  }

  return props.options.filter(font =>
    font.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 切换下拉状态
const toggleDropdown = async () => {
  if (isOpen.value) {
    closeDropdown()
  } else {
    await openDropdown()
  }
}

// 打开下拉面板
const openDropdown = async () => {
  isOpen.value = true
  emit('open')

  await nextTick()
  updateDropdownPosition()

  // 滚动到当前选中的字体位置
  await scrollToSelectedFont()

  // 添加点击外部关闭的监听
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('resize', updateDropdownPosition)
}

// 关闭下拉面板
const closeDropdown = () => {
  isOpen.value = false

  // 移除监听器
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('resize', updateDropdownPosition)
}

// 更新下拉面板位置
const updateDropdownPosition = () => {
  if (!selectRef.value || !isOpen.value) return

  const rect = selectRef.value.getBoundingClientRect()
  const dropdownHeight = 400 // 下拉面板高度
  const spacing = 4 // 间距

  let top = rect.bottom + spacing
  let left = rect.left

  // 检查是否超出视窗底部
  if (top + dropdownHeight > window.innerHeight) {
    top = rect.top - dropdownHeight - spacing
  }

  // 检查是否超出视窗右侧
  const dropdownWidth = Math.max(rect.width, 300)
  if (left + dropdownWidth > window.innerWidth) {
    left = window.innerWidth - dropdownWidth - 10
  }

  dropdownStyle.value = {
    position: 'fixed',
    top: `${top}px`,
    left: `${left}px`,
    width: `${Math.max(rect.width, 300)}px`,
    zIndex: 9999
  }
}

// 滚动到当前选中的字体位置
const scrollToSelectedFont = async () => {
  if (!selectedFont.value || !recycleScrollerRef.value) return

  // 等待 RecycleScroller 完全渲染
  await nextTick()

  // 在过滤后的列表中查找当前选中字体的索引
  const selectedIndex = filteredFonts.value.findIndex(
    font => font.id === selectedFont.value.id
  )

  if (selectedIndex >= 0) {
    try {
      // 使用 RecycleScroller 的 scrollToItem 方法
      recycleScrollerRef.value.scrollToItem(selectedIndex)
    } catch (error) {
      console.warn('Failed to scroll to selected font:', error)
    }
  }
}

// 点击外部关闭
const handleClickOutside = (event) => {
  if (!selectRef.value?.contains(event.target)) {
    closeDropdown()
  }
}

// 选择字体
const selectFont = (font) => {
  // 更新历史记录
  updateHistory(font)

  // 发出事件
  emit('update:modelValue', font)
  emit('change', font)

  // 关闭下拉面板
  closeDropdown()
}

// 更新历史记录
const updateHistory = (font) => {
  const index = historyFont.value.findIndex(item => item.id === font.id)

  if (index > -1) {
    historyFont.value.splice(index, 1)
  }

  historyFont.value.unshift({
    id: font.id,
    file: font.file,
    name: font.name,
    img: font.img
  })

  // 限制历史记录数量
  if (historyFont.value.length > 5) {
    historyFont.value.pop()
  }

  // 保存到本地存储
  try {
    localStorage.setItem('history-font', JSON.stringify(historyFont.value))
  } catch (error) {
    console.error('Failed to save font history:', error)
  }
}

// 搜索
const onSearch = async (value) => {
  searchQuery.value = value
  emit('search', value)

  // 搜索后如果当前选中的字体仍在结果中，滚动到对应位置
  if (selectedFont.value && isOpen.value) {
    await nextTick()
    await scrollToSelectedFont()
  }
}

// 清理
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('resize', updateDropdownPosition)
})
</script>

<style scoped>
.virtual-font-select {
  position: relative;
  width: 100%;
}

.font-select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 32px;
}

.font-select-trigger:hover {
  border-color: #57a3f3;
}

.font-select-trigger.is-open {
  border-color: #57a3f3;
  box-shadow: 0 0 0 2px rgba(87, 163, 243, 0.2);
}

.selected-font {
  flex: 1;
  min-width: 0;
}

.selected-font-name {
  color: #515a6e;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.placeholder {
  color: #c5c8ce;
}

.arrow-icon {
  transition: transform 0.2s ease;
  color: #c5c8ce;
}

.is-open .arrow-icon {
  transform: rotate(180deg);
}

.font-select-dropdown {
  background: #fff;
  border: 1px solid #e8eaec;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  max-height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-box {
  padding: 12px;
  border-bottom: 1px solid #e8eaec;
  flex-shrink: 0;
}

.history-section {
  padding: 12px;
  border-bottom: 1px solid #e8eaec;
  max-height: 300px;
  overflow-y: auto;
  flex-shrink: 0;
}

.section-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.history-fonts {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.font-item__history {
  width: 100%;
  min-height: 50px;
  border: 1px solid #e8eaec;
  border-radius: 4px;
  justify-content: flex-start;
  text-align: left;
  font-size: 14px;
  padding: 8px 12px;
  background-color: #fff;
  margin: 0;
}

.divider {
  margin: 12px 0 0 0;
  border: none;
  border-top: 1px solid #e8eaec;
}

.virtual-list-container {
  flex: 1;
  min-height: 0;
  padding: 12px 0 0 0;
  display: flex;
  flex-direction: column;
}

.virtual-list-container .section-title {
  padding: 0 12px 8px 12px;
}

.font-scroller {
  flex: 1;
  min-height: 200px;
}

.font-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  min-height: 50px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  color: #515a6e;
  font-size: 14px;
}

.font-item:hover {
  background-color: #f8f8f9;
}

.font-item__active {
  background-color: #f0f7ff;
  color: #2d8cf0;
}

.font-item__history {
  border: 1px solid #e8eaec;
  border-radius: 4px;
  min-height: 40px;
  justify-content: center;
  text-align: center;
  font-size: 12px;
}

.font-item__history:hover {
  border-color: #57a3f3;
}

.font-item__history.font-item__active {
  border-color: #2d8cf0;
}
</style>
