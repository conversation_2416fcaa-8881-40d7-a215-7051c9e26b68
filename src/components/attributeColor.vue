<template>
  <div
    class="box attr-item-box"
    v-if="
      mixinState.mSelectMode === 'one' &&
      mixinState.mSelectOneType !== 'image' &&
      mixinState.mSelectOneType !== 'group'
    "
  >
    <Divider plain orientation="left"><h4>颜色</h4></Divider>
    <div class="bg-item">
      <ColorPicker
        style="width: 100%"
        useType="both"
        format="rgb"
        v-model:pureColor="pureColor"
        v-model:gradientColor="gradientColor"
        :activeKey="activeKey"
        @pureColorChange="changeColor"
        @gradientColorChange="changeColor"
        ref="colorPickerRef"
      ></ColorPicker>
    </div>
  </div>
</template>

<script setup name="AttrBute">
import useSelect from '@/hooks/select';
import { ColorPicker } from 'vue3-colorpicker';
import 'vue3-colorpicker/style.css';
import { toRaw } from 'vue';

const update = getCurrentInstance();
const { fabric, mixinState, canvasEditor } = useSelect();
const angleKey = 'gradientAngle';

const colorPickerRef = ref(null);
const pureColor = ref('');
const gradientColor = ref('');
const activeKey = ref('');
const getObjectAttr = async (e) => {
  const activeObject = canvasEditor.canvas.getActiveObject();
  if (e && e.target && e.target !== activeObject) return;
  if (activeObject && mixinState.mSelectMode === 'one') {
    const fill = activeObject.get('fill');
    if (typeof fill === 'string') {
      pureColor.value = fill;
      await nextTick();
      if (colorPickerRef.value) colorPickerRef.value.state.activeKey = 'pure';
    } else {
      gradientColor.value = fabricGradientToCss(toRaw(fill), activeObject);
      await nextTick();
      if (colorPickerRef.value) colorPickerRef.value.state.activeKey = 'gradient';
    }
  }
};

const changeColor = () => {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  if (activeObject) {
    const { activeKey } = colorPickerRef.value.state;
    const { angle, type, startColor, startColorStop, endColor, endColorStop } =
      colorPickerRef.value.getBindArgs;

    if (activeKey === 'pure') {
      activeObject.set('fill', pureColor.value);
    } else {
      const params = {
        startColor,
        startColorStop,
        endColor,
        endColorStop,
        activeObject,
        angle,
      };
      if (type === 'linear') {
        const linearGradient = createLinearGradient(params);
        activeObject.set(angleKey, angle);
        activeObject.set('fill', linearGradient, angle);
      } else {
        const radialGradient = createRadialGradient(params);
        activeObject.set('fill', radialGradient);
      }
    }
    canvasEditor.canvas.renderAll();
  }
};

const fabricGradientToCss = (fill, activeObject) => {
  if (!fill || fill.type === 'pattern' || fill.source instanceof Image) return;
  const colorStops = fill.colorStops.map((item) => {
    return item.color + ' ' + item.offset * 100 + '%';
  });
  const angle = activeObject.get(angleKey);
  if (fill.type === 'linear') {
    return `linear-gradient(${angle}deg, ${colorStops})`;
  }
  return `radial-gradient(circle, ${colorStops})`;
};

const createRadialGradient = ({
  startColor,
  startColorStop,
  endColor,
  endColorStop,
  activeObject,
}) => {
  return new fabric.Gradient({
    type: 'radial',
    gradientUnits: 'pencentage',
    coords: {
      x1: activeObject.width / 2,
      y1: activeObject.height / 2,
      r1: 0,
      x2: activeObject.width / 2,
      y2: activeObject.height / 2,
      r2: activeObject.width / 2,
    },
    colorStops: [
      {
        color: startColor.toRgbString(),
        offset: startColorStop / 100,
      },
      {
        color: endColor.toRgbString(),
        offset: endColorStop / 100,
      },
    ],
  });
};
const createLinearGradient = ({
  startColor,
  startColorStop,
  endColor,
  endColorStop,
  activeObject,
  angle,
}) => {
  const { width, height } = activeObject;
  const gradAngleToCoords = (paramsAngle) => {
    const anglePI = -parseInt(paramsAngle, 10) * (Math.PI / 180);
    return {
      x1: Math.round(50 + Math.sin(anglePI) * 50) / 100,
      y1: Math.round(50 + Math.cos(anglePI) * 50) / 100,
      x2: Math.round(50 + Math.sin(anglePI + Math.PI) * 50) / 100,
      y2: Math.round(50 + Math.cos(anglePI + Math.PI) * 50) / 100,
    };
  };

  const angleCoords = gradAngleToCoords(angle);
  return new fabric.Gradient({
    type: 'linear',
    gradientUnits: 'pencentage',
    coords: {
      x1: angleCoords.x1 * width,
      y1: angleCoords.y1 * height,
      x2: angleCoords.x2 * width,
      y2: angleCoords.y2 * height,
    },
    colorStops: [
      {
        color: startColor.toRgbString(),
        offset: startColorStop / 100,
      },
      {
        color: endColor.toRgbString(),
        offset: endColorStop / 100,
      },
    ],
  });
};

const selectCancel = () => {
  update?.proxy?.$forceUpdate();
};

onMounted(() => {
  getObjectAttr();
  canvasEditor.on('selectCancel', selectCancel);
  canvasEditor.on('selectOne', getObjectAttr);
  canvasEditor.canvas.on('object:modified', getObjectAttr);
});

onBeforeUnmount(() => {
  canvasEditor.off('selectCancel', selectCancel);
  canvasEditor.off('selectOne', getObjectAttr);
  canvasEditor.canvas.off('object:modified', getObjectAttr);
});
</script>

<style scoped lang="less">
/deep/.vc-color-wrap {
  width: 100%;
  margin-right: 0;
}

:deep(.ivu-input-number) {
  display: block;
  width: 100%;
}

.ivu-form-item {
  background: #f6f7f9;
  border-radius: 5px;
  padding: 0 5px;
  margin-bottom: 10px;
}

.ivu-row {
  margin-bottom: 10px;
}
</style>
