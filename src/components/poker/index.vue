<!-- file 扑克牌 -->
<template>
  <div class="c-batch-poker">
    <Divider plain orientation="left">扑克牌生成</Divider>
    <Steps :current="current" direction="vertical" size="small">
      <Step title="定义花色">
        <template #content>
          <Design ref="designRef" />
        </template>
      </Step>
      <Step title="选择图片（可多选）">
        <template #content>
          <Picture ref="pictureRef" />
        </template>
      </Step>
      <Step title="选择模板">
        <template #content>
          <SelectTemplateV2 @change="handleSelectTemplate" />
        </template>
      </Step>
      <Step title="文件命名">
        <template #content>
          <span>指定文件保存的格式</span>
          <Input size="small" disabled placeholder="文件默认命名.png" />
        </template>
      </Step>
      <Step title="开始生成">
        <template #content>
          <Generate ref="generateRef" @preview="handleGenerate" :id="selectTempls[0]" />
        </template>
      </Step>
    </Steps>
  </div>
</template>

<script setup>
import SelectTemplateV2 from '@/components/batch/components/SelectTemplateV2'
import Design from './components/Design'
import Picture from './components/Picture'
import Generate from './components/Generate'
import { Message } from 'view-ui-plus'

const current = ref(0)

const selectTempls = ref([]);
const handleSelectTemplate = (id) => {
  selectTempls.value = [id]
}

const generateRef = ref(null)
const designRef = ref(null)
const pictureRef = ref(null)

const handleGenerate = () => {
  const renderData = designRef.value.getData()
  if (!renderData) return
  if (!renderData.length) return Message.error('请勾选花色')
  const imgList = pictureRef.value.getData()
  if (!imgList.length) return Message.error('请上传图片')
  if (!selectTempls.value.length) return Message.error('请选择模板')
  console.clear()
  console.log(renderData)
  generateRef.value.handleOk({ renderData, imgList  })
}
</script>

<style lang="less"></style>
