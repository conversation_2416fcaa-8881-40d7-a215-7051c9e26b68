<!-- file 花色 -->
<template>
  <div class="c-design">
    <div class="c-design-text">
      <Button class="c-design-text__button" size="small" icon="md-add" type="primary" shape="circle" @click="addTextBox('text', '')"></Button>
      <span style="margin: 0 8px; font-size: 14px;">文字</span>
      <RadioGroup v-model="checked.textChecked">
        <Radio label="num">
          <span>数字</span>
        </Radio>
        <Radio label="file">
          <span>文件</span>
        </Radio>
      </RadioGroup>
      <Upload action="/" class="c-design-text__file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" :before-upload="handleFileUpload">
        <Button size="small">选择文件</Button>
        <span class="c-design-text__file__desc" v-if="files">
          <span>{{ files.name }}</span>
        </span>
      </Upload>
    </div>
    <div class="c-design-info">
      <Button style="margin-top: 17px" class="c-design-text__button" size="small" icon="md-add" type="primary" shape="circle" @click="addTextBox('img', 'https://prod-env-1317082621.cos.ap-hongkong.myqcloud.com/static/joker.png?imageMogr2/thumbnail/100x100')"></Button>
      <span style="margin: 0 8px; font-size: 14px;padding-top: 16px">花色</span>
      <div>
        <div class="c-design-info__inner c-design-info__inner__center" v-for="item in Jokers" :key="item.key">
          <Checkbox size="small" v-model="checked[item.key]">{{ item.label }}</Checkbox>
          <img style="height: 20px" :src="item.icon" alt="" />
          <div>
            <Upload action="/" accept="image/*" :before-upload="(file) => handleImgUpload(file, item)">
              <Button icon="ios-cloud-upload-outline" size="small">替换图片</Button>
            </Upload>
            <ColorPicker transfer size="small" style="width: 100%; margin-top: 5px" v-model="item.color" />
          </div>
        </div>
      </div>
    </div>
    <div class="c-design-info">
      <Button class="c-design-text__button" size="small" icon="md-add" type="primary" shape="circle" @click="addTextBox('img', 'https://prod-env-1317082621.cos.ap-hongkong.myqcloud.com/static/jocker_holder.png')"></Button>
      <span style="margin: 0 8px; font-size: 14px">王牌</span>
      <div>
        <div class="c-design-info__inner" v-for="item in Jokers2" :key="item.key">
          <Checkbox size="small" v-model="checked[item.key]">{{ item.label }}</Checkbox>
          <img :src="item.icon" alt="" />
          <Upload action="/" accept="image/*" :before-upload="(file) => handleImgUpload(file, item)">
            <Button icon="ios-cloud-upload-outline" size="small">替换图片</Button>
          </Upload>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { v4 as uuid } from 'uuid'
import { Message } from 'view-ui-plus'
import useSelect from '@/hooks/select'
import { cos } from '@/utils/cos'

const { fabric, canvasEditor } = useSelect()

const checked = ref({
  textChecked: 'num', // 数字
  spadesChecked: false, // 黑桃
  heartChecked: false, // 红桃
  plumChecked: false, // 梅花
  blockChecked: false, // 方块
  joker1Checked: false, // 大鬼
  joker2Checked: false // 小鬼
})

// 扑克牌
const Jokers = ref([
  {
    label: '黑桃',
    value: 0,
    key: 'spadesChecked',
    icon: '//prod-env-1317082621.cos.ap-hongkong.myqcloud.com/static/spades.png',
    color: '#000000'
  },
  {
    label: '红桃',
    value: 1,
    key: 'heartChecked',
    icon: '//prod-env-1317082621.cos.ap-hongkong.myqcloud.com/static/heart.png',
    color: '#d83621'
  },
  {
    label: '梅花',
    value: 2,
    key: 'plumChecked',
    icon: '//prod-env-1317082621.cos.ap-hongkong.myqcloud.com/static/plum.png',
    color: '#000000'
  },
  {
    label: '方块',
    value: 3,
    key: 'blockChecked',
    icon: '//prod-env-1317082621.cos.ap-hongkong.myqcloud.com/static/block.png',
    color: '#d83621'
  }
])

// 大小王
const Jokers2 = ref([
  {
    label: '大鬼',
    value: 4,
    key: 'joker1Checked',
    icon: '//prod-env-1317082621.cos.ap-hongkong.myqcloud.com/static/JOKER.png'
  },
  {
    label: '小鬼',
    value: 5,
    key: 'joker2Checked',
    icon: '//prod-env-1317082621.cos.ap-hongkong.myqcloud.com/static/JOKER2.png'
  }
])

// 文件上传
const excelData = ref([])
const files = ref(null)
const handleFileUpload = async (file) => {
  const { jsonData, columns } = await canvasEditor.getExcelData(file)
  files.value = file
  const key = columns[0].key
  excelData.value = jsonData.map(item => item[key])
}

// 图片上传
const handleImgUpload = async (file, record) => {
  const { Location } = await cos.uploadFileToPoker(file, file.name)
  record.icon = '//' + Location
}

const addTextBox = (type, url = undefined) => {
  if (type === 'text') {
    const text = new fabric.Textbox('{D}', {
      width: 120,
      fontSize: 80,
      fill: '#000000FF',
      textAlign: 'center',
      linkData: ['text', 'D'],
      id: uuid(),
    });
    text.setControlVisible('mt', false)
    text.setControlVisible('mb', false)
    canvasEditor.canvas.add(text)
    canvasEditor.canvas.setActiveObject(text)
    canvasEditor.position('center')
  } else {
    insertImg(url)
  }
}

const insertImg = (url) => {
  fabric.Image.fromURL(
    url,
    (imgEl) => {
      imgEl.set({
        id: uuid()
      })
      canvasEditor.canvas.add(imgEl)
      canvasEditor.canvas.requestRenderAll()
    },
    { crossOrigin: 'anonymous' }
  )
}

const getData = () => {
  if (checked.value.textChecked !== 'num' && !files.value) {
    Message.error('请选择文件')
    return false
  }
  const numTexts = ['A', 2, 3, 4, 5, 6, 7, 8, 9, 10, 'J', 'Q', 'K']
  const data = checked.value.textChecked === 'num' ? numTexts : excelData.value
  const ruleKeys = ['spadesChecked', 'plumChecked', 'blockChecked', 'heartChecked']
  const renderData = []
  ruleKeys.forEach(key => {
    if (checked.value[key]) {
      const joker = Jokers.value.find(item => item.key === key)
      for (const num of data) {
        renderData.push({
          img: joker.icon,
          label: num,
          isDesign: true,
          color: joker.color
        })
      }
    }
  })
  if (checked.value.joker1Checked) {
    renderData.push({
      img: Jokers2.value[0].icon,
      label: 'JOKER',
      isDesign: true
    })
  }
  if (checked.value.joker2Checked) {
    renderData.push({
      img: Jokers2.value[1].icon,
      label: 'JOKER',
      isDesign: true
    })
  }
  return renderData
}

defineExpose({
  getData
})
</script>

<style lang="less">
.c-design .ivu-upload-list {
  display: none;
}
.c-design-text {
  display: flex;
  align-items: center;
  margin: 20px 0;
}
.c-design-text__button {
  width: 17px !important;
  height: 17px !important;
  font-size: 10px !important;
}
.c-design-info__inner__center {
  align-items: center;
}
.c-design-info__inner {
  display: flex;
  margin-bottom: 10px;
  img {
    width: 20px;
    margin-right: 15px;
  }
}
.c-design-info {
  margin-top: 15px;
  display: flex;
}
.c-design-text__file {
  position: relative;
  .c-design-text__file__desc {
    position: absolute;
    bottom: -18px;
    left: -10px;
    white-space: nowrap;
    color: red;
  }
}
</style>
