<!-- file 扑克牌生成 -->
<template>
  <div class="c-poker-generate">
    <div>
      <Button size="small" @click="handleUploadPreview">
        上传设计图
      </Button>
      <span class="c-poker-generate__text" @click="handleUploadsPreviewModel">已上传 {{uploadsImages.length}} 张牌</span>
    </div>

    <div style="margin-top: 10px">
      <Button size="small" type="primary" @click="handlePreview">预览</Button>
      <span class="c-poker-generate__text" @click="handlePreviewModel">已生成 {{previewList.length}} 张牌</span>
    </div>
  </div>

  <!-- 生成预览 -->
  <Modal
    v-model="previewVisible"
    title="批量生成中，请耐心等待"
    :mask-closable="false"
    closable
    :footer-hide="true"
    :width="800"
  >
    <Progress
      :percent="parseInt((currentImages[0].length / currentImages[1]) * 100)"
      :stroke-width="20"
      text-inside
    />
    <div style="text-align: center">进度：{{ currentImages[0].length }}/{{ currentImages[1] }}</div>

    <Space class="c-generate-model" wrap>
      <template v-for="(item, index) in currentImages[0]" :key="item.name">
        <div class="c-generate-image">
          <Image
            :src="item.src"
            fit="contain"
            width="120px"
            height="156px"
            preview
            :preview-list="currentImages[0].map((info) => info.src)"
            :initial-index="index"
          />
        </div>
      </template>
    </Space>

    <div v-if="currentImages[0].length === currentImages[1]">
      <Divider size="small" plain orientation="left"></Divider>
      <div class="btn-tip">
        <Button v-if="currentImages[0].length" type="error" style="margin-right: 10px" @click="handleClampTemplate">夹具模板</Button>
        <Button type="primary" @click="handleDownFile(false)">下载</Button>
      </div>
    </div>
  </Modal>

  <!-- 夹具批量 -->
  <Modal v-model="visible" width="800px" title="现有夹具" footer-hide @on-cancel="handleClearPath">
    <div class="c-clamp-template__desc">
      <Breadcrumb>
        <BreadcrumbItem v-for="item in paths" :key="item.id" @click="handlePathClick(item)">{{ item.name }}</BreadcrumbItem>
      </Breadcrumb>
    </div>
    <div class="c-clamp-scroll__null" v-if="!clampList.length && !isLoading">--暂无数据--</div>
    <template v-if="isLoading">
      <div class="c-clamp-template__loading first-loading-wrp">
        <div class="loading-wrp">
          <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
        </div>
      </div>
    </template>
    <Scroll v-else :on-reach-bottom="getClampList" :on-reach-edg="handleResetSearch" height="400">
      <FileItem
        v-for="item in clampList"
        :key="item.id"
        :record="item"
        is-clamp
        @folder="handleFolder"
        @template="handleSelectClamp"
      />
    </Scroll>
  </Modal>

  <!-- 夹具批量 -->
  <Modal
    v-model="clampVisible"
    title="批量生成夹具中，请耐心等待"
    :mask-closable="false"
    closable
    :footer-hide="true"
    :width="800"
  >
    <Progress
      :percent="parseInt((clampTemplateList.length / allClampDoneLength) * 100)"
      :stroke-width="20"
      text-inside
    />
    <div style="text-align: center">进度：{{ clampTemplateList.length }}/{{ allClampDoneLength }}</div>

    <Space class="c-generate-model" wrap>
      <template v-for="(item, index) in clampTemplateList" :key="item.name">
        <Image
          :src="item.src"
          fit="contain"
          width="120px"
          height="156px"
          preview
          :preview-list="clampTemplateList.map((info) => info.src)"
          :initial-index="index"
        />
      </template>
    </Space>
    <div v-if="clampTemplateList.length === allClampDoneLength">
      <Divider size="small" plain orientation="left"></Divider>
      <div class="btn-tip">
        <Button type="error" style="margin-right: 10px" @click="handleDownFile(true)">下载图片</Button>
        <Button type="primary" @click="clampVisible = false">完成</Button>
      </div>
    </div>
  </Modal>
</template>

<script setup>
import JSZip from 'jszip'
import useSelect from '@/hooks/select'
import useClamp from '@/hooks/useClamp'
import { cloneDeep } from 'lodash-es'
import { useRoute } from 'vue-router'
import { getFixtureDetail, getTemplateDetail } from '@/api/v2'
import { Utils } from '@kuaitu/core'
import { Button, Modal } from 'view-ui-plus'
import FileItem from '@/components/clamp/FileItem'
import useMaterial from '@/hooks/useMaterial'
import Loading from "@/components/load";
import {cos} from "@/utils/cos";
import dayjs from "dayjs";

const { canvasEditor } = useSelect()
const { getTemplInfo } = useMaterial()
const { insertImgFile, selectFiles } = Utils
const route = useRoute()

const props = defineProps({
  id: {
    type: String,
    default: 0
  }
})

const emits = defineEmits(['preview'])

const handlePreview = () => {
  previewList.value = []
  clonePreviewImageList.value = []
  previewAllLength.value = 0
  isUploadsImages.value = false
  emits('preview')
}

function sleep(time = 10) {
  const rightButton = document.querySelector('.right-btn')
  return new Promise((resolve) => {
    setTimeout(() => {
      rightButton.click()
      resolve();
    }, time);
  });
}

// 预览的图片
const previewList = ref([])
const previewAllLength = ref(0)
const handleOk = async (record) => {
  const { data } = await getTemplateDetail(props.id || 0)
  const json = JSON.parse(data.json)
  previewAllLength.value = record.renderData.length
  previewVisible.value = true
  await startRender(json, record.renderData, record.imgList)
  await loadJson(json)
}

const jokerImg = '/static/joker.png' // 花色地址
const picImg = '/static/joker-pic.png' // 图片占位图地址
const jokerImg2 = 'static/jocker_holder.png' // 大小鬼地址
const startRender = async (json, renderData = [], imgList = []) => {
  const maxImgIndex = imgList.length
  let index = 0
  let nameIndex = 0
  for (const item of renderData) {
    const cloneJson = cloneDeep(json)
    cloneJson.objects.forEach((rect) => {
      const match = rect.text?.match(/\{([^{}]*)\}/)
      // 文字替换
      if (match && match[1]) {
        rect.fill = item.color
        rect.text = item.label === 'JOKER' ? '' : item.label.toString()
      }
    })
    if(item.label === 'JOKER') {
      cloneJson.objects = cloneJson.objects.filter(item => {
        if (item.linkData && item.linkData.length) return false
        return !(item.type === 'image' && item.src.includes(jokerImg))
      })
    } else {
      cloneJson.objects = cloneJson.objects.filter(item => !(item.type === 'image' && item.src.includes(jokerImg2)))
    }
    await loadJson(cloneJson)
    const containers = canvasEditor.canvas.getObjects().filter(item => item.type === 'image' && (item.src.includes(picImg) || item.src.includes(jokerImg) || item.src.includes(jokerImg2)))
    await handleReplaceImg(containers, item.img, imgList[index].src)
    const base64 = await canvasEditor.preview()
    index++
    nameIndex++
    if (maxImgIndex === index) {
      index = 0
    }
    previewList.value.push({
      name: nameIndex.toString().padStart(3, '0') + '.png',
      src: base64
    })
    await sleep(500)
  }
}

function loadJson(json) {
  return new Promise((resolve) => {
    canvasEditor.loadJSON(json, resolve)
  })
}

// 替换图片、花色地址
const handleReplaceImg = (canvas, imgSrc, picSrc) => {
  return new Promise(async resolve => {
    const imgEl = await insertImgFile(imgSrc)
    const picEl = await insertImgFile(picSrc)
    let num = 0
    let maxLength = canvas.length
    for (let index in canvas) {
      const width = canvas[index].get('width')
      const height = canvas[index].get('height')
      const scaleX = canvas[index].get('scaleX')
      const scaleY = canvas[index].get('scaleY')

      canvas[index].setSrc((canvas[index].src.includes(jokerImg) || canvas[index].src.includes(jokerImg2)) ? imgSrc : picSrc, () => {
        const sX = (width * scaleX) / ((canvas[index].src.includes(jokerImg) || canvas[index].src.includes(jokerImg2)) ? imgEl.width : picEl.width)
        const sY = (height * scaleY) / ((canvas[index].src.includes(jokerImg) || canvas[index].src.includes(jokerImg2)) ?  imgEl.height: picEl.height)
        canvas[index].set('scaleX', sX)
        canvas[index].set('scaleY', sY)
        num += 1
        if (num === maxLength) {
          canvasEditor.canvas.renderAll()
          resolve()
        }
      }, { crossOrigin: 'anonymous' })
    }
  })
}

// 夹具替换
const handleReplaceClampImg = async (activeCanvas, imgSrc) => {
  const imgEl = await insertImgFile(imgSrc)
  const width = activeCanvas.get('width')
  const height = activeCanvas.get('height')
  const scaleX = activeCanvas.get('scaleX')
  const scaleY = activeCanvas.get('scaleY')
  activeCanvas.setSrc(imgSrc, () => {
    activeCanvas.set('scaleX', (width * scaleX) / imgEl.width)
    activeCanvas.set('scaleY', (height * scaleY) / imgEl.height)
  })
}

// 预览弹层
const previewVisible = ref(false)
const handlePreviewModel = () => {
  if (!previewList.value.length) return
  isUploadsImages.value = false
  previewVisible.value = true
}
const handleDownFile = (isClamp = false) => {
  const imgList = []
  let index = 0
  const previewLists = isClamp ? clampTemplateList.value : currentImages.value[0]
  for (const clamp of previewLists) {
    imgList.push({
      fileName: `${(index + 1).toString().padStart(3, '0')}.${clamp.name.split('.').at(-1)}`,
      src: canvasEditor.changeDpiDataUrl(clamp.src, 300)
    })
    index += 1
  }
  downZip(imgList)
}

// 夹具批量生成
const { paths, isLoading, clampList, handleResetSearch, getClampList, handleClearPath } = useClamp()
const visible = ref(false)
const handlePathClick = (record) => {
  const index = paths.value.findIndex(item => item.id === record.id)
  paths.value = paths.value.slice(0, index + 1)
  handleResetSearch(record.id)
}
const handleFolder = (record) => {
  paths.value.push({
    id: record.id,
    name: record.name
  })
  handleResetSearch(record.id)
}
const handleSelectClamp = async (record) => {
  visible.value = false
  handleClearPath()
  const result = await getFixtureDetail(record.id)
  const json = JSON.parse(result.data.jsonStr)
  if (record.isChecked) {
    json.objects.forEach(item => {
      if (item.isClamp) {
        item.filters = item.filters || []
        item.filters.push({
          type: 'RemoveColor',
          color: record.color,
          distance: record.distance
        })
      }
    })
  }
  multiSort(json.objects, (a, b) => a.top - b.top, (a, b) => a.left - b.left)
  await generateClampTemplate(json)
}
const handleClampTemplate = () => {
  visible.value = true
  handleResetSearch()
}

const clampTemplateList = ref([])
const clonePreviewImageList = ref([])
const allClampDoneLength = ref(0)
const clampVisible = ref(false)
const generateClampTemplate = async (json) => {
  clonePreviewImageList.value = [...currentImages.value[0]]
  const imgLength = clonePreviewImageList.value.length
  const clampImgLength = json.objects.filter(item => item.isClamp).length
  allClampDoneLength.value = clampImgLength ? Math.ceil(imgLength / clampImgLength) : 0
  clampTemplateList.value = []
  clampVisible.value = true

  for (let i = 0; i < allClampDoneLength.value; i++) {
    await getGenerateClamp(json, (i + 1).toString().padStart(3, '0'))
  }

  await getTempData()
}

async function getGenerateClamp(json, prefix) {
  const olbCanvasObjects = []
  const newCanvasObjects = []

  for (const item of json.objects) {
    if (!clonePreviewImageList.value.length && item.isClamp) {
      continue
    }
    olbCanvasObjects.push(cloneDeep(item))
    if (clonePreviewImageList.value.length && item.isClamp) {
      item.src = clonePreviewImageList.value.shift().src
      newCanvasObjects.push(item)
    }
  }

  json.objects = olbCanvasObjects

  await loadJson(json)
  const filterCanvas = newCanvasObjects.filter(item => item.isClamp) || []
  const containers = canvasEditor.canvas.getObjects().filter(item => item.isClamp)
  for (const item of containers) {
    await handleReplaceClampImg(item, filterCanvas.find(record => record.id === item.id).src)
  }
  canvasEditor.canvas.renderAll()
  await sleep()
  const base64 = await canvasEditor.preview()
  clampTemplateList.value.push({
    src: base64,
    name: `${prefix}.png`
  })
  await sleep(1000)
}


const getTempData = async () => {
  if (route.query.id) {
    const data = await getTemplInfo(route.query.id);
    canvasEditor.loadJSON(JSON.stringify(data.data.json));
  } else {
    canvasEditor.clear();
  }
}

// 多条件排序
const multiSort = (array, ...compairers) => {
  return array.sort((a, b) => {
    for (const c of compairers) {
      const r = c(a, b)
      if (r !== 0) {
        return r
      }
    }
  })
}

// 下载图片
const downZip = async (imgList) => {
  const zip = new JSZip()
  for (const item of imgList) {
    const [base64Blob] = canvasEditor.Utils.base64ToBlob(item.src)
    zip.file(item.fileName, base64Blob)
  }

  zip.generateAsync({ type: 'blob' }).then(function (content) {
    const anchorEl = document.createElement('a')
    anchorEl.href = URL.createObjectURL(content)
    anchorEl.download = '设计批量生成.zip'
    document.body.appendChild(anchorEl)
    anchorEl.click()
    anchorEl.remove()
  })
}

// 上传预览图
let imageIndex = 0
const uploadsImages = ref([])
const handleUploadPreview = () => {
  selectFiles({ accept: 'image/*', multiple: true }).then((fileList) => {
    Loading.show('正在上传图片')
    imageIndex = 0
    uploadsImages.value = []
    Array.from(fileList).forEach(async (item, index) => {
      const name = `${dayjs().format('YYYY-MM-DD_HH-mm-ss')}_${Math.ceil(Math.random() * 1000000)}Poker.png`
      const { Location } = await cos.uploadFileToPoker(item, name)
      const src = `//${Location}`
      const image = new Image()
      image.src = src
      image.crossOrigin = 'anonymous'
      image.onload = () => {
        const canvas = document.createElement('canvas')
        canvas.width = image.width
        canvas.height = image.height
        const context = canvas.getContext('2d')
        context.drawImage(image, 0, 0, image.width, image.height)
        const base64 = canvas.toDataURL('image/png')
        uploadsImages.value.push({
          src: base64,
          name
        })
        imageIndex += 1
        if (imageIndex === fileList.length) {
          Loading.hide()
        }
      }
    });
  });
}

const currentImages = computed(() => {
  return [isUploadsImages.value ? uploadsImages.value : previewList.value, isUploadsImages.value ? uploadsImages.value.length : previewAllLength.value]
})

const isUploadsImages = ref(false)
const handleUploadsPreviewModel = () => {
  isUploadsImages.value = true
  previewVisible.value = true
}

defineExpose({
  handleOk
})
</script>

<style lang="less">
.c-poker-generate {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.c-poker-generate__text {
  color: #e0502c;
  cursor: pointer;
  margin-left: 8px;
}
.c-generate-model {
  max-height: 500px;
  overflow-y: auto;
}
.c-generate-image:hover {
  position: relative;
  .c-generate-text {
    display: block;
    z-index: 999;
  }
}
.btn-tip {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.c-poker-picture__modal__img {
  display: inline-block;
  width: 150px;
  margin-right: 15px;
  img {
    width: 100%;
  }
}
</style>
