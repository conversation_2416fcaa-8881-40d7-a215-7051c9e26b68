<!-- file 选择图片 -->
<template>
  <div class="c-poker-picture">
    <Button class="c-poker-picture__button" size="small" icon="md-add" type="primary" shape="circle" @click="insertImg"></Button>
    <span style="margin: 0 10px 0 5px">图片</span>
    <span v-if="imgList.length" class="c-poker-picture__upload" @click="handlePreview">预览 {{ imgList.length }} 张图片</span>
    <Upload accept="image/*" multiple :before-upload="handleImgUpload">
      <Button icon="ios-cloud-upload-outline" size="small">选择图片</Button>
    </Upload>
  </div>
  <Modal
    v-model="visible"
    width="700"
    title="预览"
    @on-ok="handleOk"
    @on-cancel="handleCancel">
    <div class="c-poker-picture__top">
      <span>以下为上传的图片，根据其顺序生成扑克牌，您可以通过拖拽改变其布局。</span>
      <Button size="small" type="error" @click="handleClear">清空所有图片</Button>
    </div>
    <div class="c-poker-picture__modal">
      <div ref="draggableRef">
        <div class="c-poker-picture__modal__img" v-for="item in imgList" :key="item.id">
          <img :src="item.src" alt="" />
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup>
import { v4 as uuid } from 'uuid'
import { cos } from '@/utils/cos'
import { useDraggable } from 'vue-draggable-plus'
import { Modal } from 'view-ui-plus'
import Loading from '@/components/load/index.js'
import useSelect from '@/hooks/select'

const { fabric, canvasEditor } = useSelect()

const imgList = ref([])
let index = 0
const handleImgUpload = async (file) => {
  imgList.value = []
  index += 1
  Loading.show('上传图片中...')
  const { Location } = await cos.uploadFileToPoker(file, file.name)
  imgList.value.push({
    id: uuid(),
    src: '//' + Location,
    name: file.name
  })
  index -= 1
  if (!index) {
    Loading.hide()
  }
}

const visible = ref(false)
const handlePreview = () => {
  visible.value = true
}
const handleOk = () => {}
const handleCancel = () => {
  visible.value= false
}
const handleClear = () => {
  Modal.confirm({
    title: '提示',
    content: '是否清除已上传的图片',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      imgList.value = []
    }
  })
}
const insertImg = () => {
  fabric.Image.fromURL(
    'https://prod-env-1317082621.cos.ap-hongkong.myqcloud.com/static/joker-pic.png?imageMogr2/thumbnail/450x600',
    (imgEl) => {
      imgEl.set({
        id: uuid()
      })
      canvasEditor.canvas.add(imgEl)
      canvasEditor.canvas.requestRenderAll()
    },
    { crossOrigin: 'anonymous' }
  )
}

const draggableRef = ref(null)
useDraggable(draggableRef, imgList, {
  animation: 150
})

const getData = () => {
  return imgList.value
}

defineExpose({
  getData
})
</script>

<style lang="less">
.c-poker-picture {
  margin: 10px 0;
  display: flex;
  align-items: center;
  .ivu-upload-list {
    display: none;
  }
}
.c-poker-picture__button {
  width: 17px !important;
  height: 17px !important;
  font-size: 10px !important;
}
.c-poker-picture__upload {
  cursor: pointer;
  margin-right: 5px;
  color: #e56f4e;
}
.c-poker-picture__modal__img {
  display: inline-block;
  width: 150px;
  margin-right: 15px;
  img {
    width: 100%;
  }
}
.c-poker-picture__top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
</style>
