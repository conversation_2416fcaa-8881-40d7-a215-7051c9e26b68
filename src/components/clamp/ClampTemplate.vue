<!-- file 模板夹具功能 -->
<template>
  <div class="c-clamp-template" v-if="!route.query.clampId">
    <Divider plain orientation="left">
      <h4>夹具</h4>
    </Divider>
    <div class="bg-item">
      <Button v-if="!isOuter" @click="handleClampTemplate" type="primary" long>夹具模板</Button>
    </div>
  </div>
  <div v-if="route.query.clampId">
    <Divider plain orientation="left">
      <h4>夹具</h4>
    </Divider>
    <div class="bg-item">
      <Button long @click="handleLock(false)" icon="md-lock" type="text"></Button>
      <Button long @click="handleLock(true)" icon="md-unlock" type="text"></Button>
      <Button long @click="handleHide(false)" icon="md-eye-off" type="text"></Button>
      <Button long @click="handleHide(true)" icon="md-eye" type="text"></Button>
      <Button long @click="handleClone" icon="ios-copy" type="text"></Button>
    </div>
    <div style="margin-top: 10px">
      <Button @click="handleAddClamp" long>添加夹具占位图</Button>
      <Button style="margin-top: 10px" @click="handleOneClamp" long>单个替换</Button>
      <Button style="margin-top: 10px" @click="handleMachClamp" long>批量替换</Button>
    </div>
  </div>
  <Modal v-model="visible" width="800px" title="现有夹具" footer-hide>
    <div class="c-clamp-template__desc">
      <Breadcrumb>
        <BreadcrumbItem v-for="item in paths" :key="item.id" @click="handlePathClick(item)">{{ item.name }}</BreadcrumbItem>
      </Breadcrumb>
    </div>
    <div class="c-clamp-scroll__null" v-if="!clampList.length && !isLoading">--暂无数据--</div>
    <template v-if="isLoading">
      <div class="c-clamp-template__loading first-loading-wrp">
        <div class="loading-wrp">
          <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
        </div>
      </div>
    </template>
    <Scroll v-else :on-reach-bottom="getClampList" :on-reach-edg="handleResetSearch" height="400">
      <FileItem
        v-for="item in clampList"
        :key="item.id"
        :record="item"
        is-clamp
        @folder="handleFolder"
        @template="handleSelectClamp"
      />
    </Scroll>
  </Modal>
</template>

<script setup name="ClampTemplate">
import { Button, Message } from 'view-ui-plus'
import { useRoute } from 'vue-router'
import useClamp from '@/hooks/useClamp'
import FileItem from '@/components/clamp/FileItem'
import useSelect from '@/hooks/select'
import { getFixtureDetail } from '@/api/v2'
import { Utils } from '@kuaitu/core'
import { $bus, EVENT_NAME } from '@/utils/mitt'
import Loading from '@/components/load/index.js'
import ClampImg from '@/assets/clamp/clamp_img.png'
import useSystemPermission from '@/hooks/permission'

const { isOuter } = useSystemPermission()

const canvasEditor = inject('canvasEditor')
const { fabric } = useSelect()
const route = useRoute()

const { selectFiles, insertImgFile } = Utils
const { paths, isLoading, clampList, handleResetSearch, getClampList, handleClearPath } = useClamp()

const visible = ref(false)
const handleClampTemplate = () => {
  visible.value = true
  handleResetSearch()
}

const handleFolder = (record) => {
  paths.value.push({
    id: record.id,
    name: record.name
  })
  handleResetSearch(record.id)
}

const handlePathClick = (record) => {
  const index = paths.value.findIndex(item => item.id === record.id)
  paths.value = paths.value.slice(0, index + 1)
  handleResetSearch(record.id)
}

const handleSelectClamp = async (record) => {
  visible.value = false
  Loading.show()
  const base64 = await canvasEditor.preview()
  const result = await getFixtureDetail(record.id)
  const json = JSON.parse(result.data.jsonStr)
  if (record.isChecked) {
    json.objects.forEach(item => {
      if (item.isClamp) {
        item.filters = item.filters || []
        item.filters.push({
          type: 'RemoveColor',
          color: record.color,
          distance: record.distance
        })
      }
    })
  }
  handleClearPath()
  canvasEditor.loadJSON(json, () => {
    const containers = canvasEditor.canvas.getObjects().filter(item => item.isClamp)
    containers.forEach(item => {
      handleReplaceImg(item, base64)
    })
    Loading.hide()
    $bus.emit(EVENT_NAME.IS_HIDE_SHOW_BUTTON, false)
  })
}

const selectImg = () => {
  return new Promise(async (resolve) => {
    const result = await selectFiles({
      accept: 'image/*',
      multiple: false
    })
    const file = new FileReader()
    file.readAsDataURL(result[0])
    file.onload = (base64) => {
      resolve(base64.target.result)
    }
  })
}

const handleAddClamp = () => {
  canvasEditor.addImgByElement({ src: ClampImg }, true)
}

const handleReplaceImg = async (activeCanvas, imgSrc) => {
  const imgEl = await insertImgFile(imgSrc)
  const width = activeCanvas.get('width')
  const height = activeCanvas.get('height')
  const scaleX = activeCanvas.get('scaleX')
  const scaleY = activeCanvas.get('scaleY')
  activeCanvas.setSrc(imgSrc, () => {
    activeCanvas.set('scaleX', (width * scaleX) / imgEl.width)
    activeCanvas.set('scaleY', (height * scaleY) / imgEl.height)
    canvasEditor.canvas.renderAll()
  })
}

const handleOneClamp = async () => {
  const activeCanvas = canvasEditor.canvas.getActiveObject()
  if (!activeCanvas) return Message.error('请选择替换的元素')
  const base64 = await selectImg()
  await handleReplaceImg(activeCanvas, base64)
}
const handleMachClamp = async () => {
  const containers = canvasEditor.canvas.getActiveObjects()
  if (!containers.length) return Message.error('没有可替换的元素')
  const base64 = await selectImg()
  for (const item of containers) {
    await handleReplaceImg(item, base64)
  }
}

const lockAttrs = [
  'lockMovementX',
  'lockMovementY',
  'lockRotation',
  'lockScalingX',
  'lockScalingY',
]
const handleLock = (flag) => {
  const activeCanvas = canvasEditor.canvas.getActiveObjects()
  for (const canvas of activeCanvas) {
    lockAttrs.forEach(key => {
      canvas.set(key, !flag)
    })
    canvas.hasControls = flag
    canvas.draggable = !flag
  }
  canvasEditor.canvas.discardActiveObject()
  canvasEditor.canvas.renderAll()
}

const handleHide = (hide) => {
  const activeCanvas = canvasEditor.canvas.getActiveObjects()
  for (const canvas of activeCanvas) {
    canvas.set('visible', hide)
  }
  canvasEditor.canvas.renderAll()
}

const handleClone = () => {
  canvasEditor.clone()
}

const handleSelected = (canvas) => {
  const [item] = canvas
  item.on('mouse:down', () => {
    console.log(123)
  })
  if (!item.hasControls) {
    item.lockMovementX = true
    item.lockMovementY = true
    canvasEditor.canvas.renderAll()
  }
}

onMounted(() => {
  canvasEditor.on('selectOne', handleSelected)
})
onUnmounted(() => {
  canvasEditor.off('selectOne', handleSelected);
})
</script>

<style lang="less">
.c-clamp-template__loading {
  height: 400px;
}
.c-clamp-scroll__null {
  text-align: center;
}
.c-clamp-template__desc {
  cursor: pointer;
}
.bg-item {
  width: 100%;
  padding: 5px;
  display: flex;
  flex: 1;
  justify-content: space-between;
  border-radius: 5px;
}
</style>
