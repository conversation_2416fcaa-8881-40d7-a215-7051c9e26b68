<!-- file 文件类型 -->
<template>
  <div class="c-file-item" @contextmenu.stop="onContextMenu">
    <template v-if="isFile">
      <div class="c-file-item__template" @click.stop="handleFileClick">
        <Image lazy fit="contain" :src="toWebp(record.url, 132, 94)" />
        <p>{{ record.name }}</p>
      </div>
    </template>
    <template v-else>
      <div class="c-file-item__folder" @click.stop="handleFolderClick">
        <img :src="fileTypeIcon" alt="" />
        <span>{{ record.name }}</span>
      </div>
    </template>
  </div>
</template>

<script setup name="FileItem">
import ContextMenu from '@imengyu/vue3-context-menu'
import fileTypeIcon from '@/assets/icon/fileType.png'
import Loading from '@/components/load/index.js'
import Confirm from './Confirm'
import { Input, Modal, Message } from 'view-ui-plus'
import { toWebp } from '@/utils/cos'
import { useI18n } from 'vue-i18n'
import { editFixtureName, removeFixture, pasteFixture } from '@/api/v2'

const { t } = useI18n()

const emits = defineEmits(['loadTemplate', 'remove', 'folder', 'copy', 'template'])

const CLAMP_TYPE = {
  FILE: '0',
  FOLDER: '1'
}

const props = defineProps({
  record: {
    type: Object,
    default: () => ({})
  },
  copyIds: {
    type: Array,
    default: () => []
  },
  isClamp: {
    type: Boolean,
    default: false
  }
})

const isFile = computed(() => props.record.type === CLAMP_TYPE.FILE)

const onContextMenu = (e) => {
  e.preventDefault()
  if (props.isClamp) return
  const items = [
    {
      label: '删除',
      onClick: () => {
        Modal.confirm({
          title: '提示',
          content: `是否删除 <span class="c-remove-name">${props.record.name}</span> ？`,
          onOk: async () => {
            try {
              const result = await removeFixture({ ids: [props.record.id] })
              if (result.code === 50000) {
                throw new Error('删除失败')
              }
              Message.success('删除成功')
              emits('remove', props.record.id)
            } catch (e) {
              Message.error(e.message)
            }
          }
        })
      }
    },
    {
      label: '复制',
      onClick: () => {
        emits('copy', [props.record])
      }
    },
    {
      label: '修改名称',
      onClick: () => {
        const fileName = ref(props.record.name)
        Modal.confirm({
          title: '修改名称',
          render: (h) => {
            return h(Input, {
              size: 'default',
              modelValue: fileName,
              autofocus: true,
              placeholder: '请输入图层名称',
            })
          },
          onOk: async () => {
            try {
              const result = await editFixtureName({
                id: props.record.id,
                name: fileName.value
              })
              if (result.code === 50000) {
                throw new Error('修改名称失败')
              }
              props.record.name = fileName.value
              Message.success('修改名称成功')
            } catch (e) {
              Message.error(e.message)
            }
          }
        })
      }
    },
  ]
  if (props.record.type === CLAMP_TYPE.FOLDER && props.copyIds.length) {
    items.splice(2, 0, {
      label: '粘贴',
      onClick: async () => {
        Modal.confirm({
          title: '提示',
          content: `是否粘贴 <span class="c-paste-name">${props.copyIds.map(item => item.name).join('、')}</span> 到 <span class="c-remove-name">${props.record.name}</span> ？`,
          onOk: async () => {
            try {
              Loading.show('正在粘贴')
              const result = await pasteFixture({
                resourceIds: props.copyIds.map(item => item.id),
                targetId: props.record.id
              })
              if (result.code === 50000) {
                throw new Error('粘贴失败')
              }
              Message.success('粘贴成功')
            } catch (e) {
              Message.error(e.message)
            } finally {
              Loading.hide()
            }
          }
        })
      }
    })
  }
  ContextMenu.showContextMenu({
    x: e.x,
    y: e.y,
    customClass: 'c-main-menu',
    items
  })
}

const handleFileClick = () => {
  const checked = ref(false)
  const color = ref('#ffffff')
  const distance = ref(0.16)
  Modal.confirm({
    title: t('tip'),
    render: (h) => {
      return h(Confirm, { isClamp: props.isClamp, callBack: (record) => { color.value = record.color; checked.value = record.isChecked; distance.value = record.distance } })
    },
    okText: t('ok'),
    cancelText: t('cancel'),
    onOk: () => {
      if (props.isClamp) {
        emits('template', { ...props.record, isChecked: checked.value, color: color.value, distance: distance.value })
        return
      }
      emits('loadTemplate', props.record)
    }
  })
}

const handleFolderClick = () => {
  emits('folder', props.record)
}
</script>

<style lang="less">
.c-file-item {
  width: 134px;
  height: 116px;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  border: 1px solid var(--border-color);
  border-radius: 10px;
  margin-right: 10px;
  &:hover {
    background: var(--file-type-bg);
    border: 1px solid #e1e6ef;
  }
  .c-file-item__template {
    display: flex;
    align-items: center;
    flex-direction: column;
    p {
      padding: 0 5px;
      width: 100%;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .ivu-image {
      height: 85px !important;
    }
    span {
      white-space: nowrap;
    }
  }
  .c-file-item__folder {
    display: flex;
    align-items: center;
    flex-direction: column;
    img {
      width: 60px;
      margin-top: 20px;
    }
    span {
      width: 100%;
      padding: 0 5px;
      margin-top: 12px;
      display: block;
      white-space: nowrap;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.c-clamp-entry {
  display: flex;
  margin-top: 5px;
  align-items: center;
  margin-left: -10px;
  input {
    cursor: pointer;
  }
  span {
    margin-left: 5px;
    white-space: nowrap;
  }
}
</style>
