<!-- file 夹具 -->
<template>
  <div class="c-clamp" ref="clampRef">
    <Input v-model="searchInputValue" placeholder="请输入关键词">
      <template #suffix>
        <Icon type="ios-search" />
      </template>
    </Input>
    <div class="c-clamp-desc">
      <Breadcrumb>
        <BreadcrumbItem v-for="item in paths" :key="item.id" @click="handlePathClick(item)">{{ item.name }}</BreadcrumbItem>
      </Breadcrumb>
      <div class="c-clamp-desc__top">
        <Dropdown style="margin-left: 20px">
          <a href="javascript:void(0)">
            排序<Icon type="ios-arrow-down"></Icon>
          </a>
          <template #list>
            <DropdownMenu>
              <DropdownItem :class="{ 'is-active': sortKey === '' }" @click="handleSortSearch('')">默认</DropdownItem>
              <DropdownItem :class="{ 'is-active': sortKey === 'name' }" @click="handleSortSearch('name')">文件名</DropdownItem>
              <DropdownItem :class="{ 'is-active': sortKey === 'createdAt' }" @click="handleSortSearch('createdAt')">创建时间</DropdownItem>
              <DropdownItem :class="{ 'is-active': sortKey === 'updatedAt' }" @click="handleSortSearch('updatedAt')">更新时间</DropdownItem>
            </DropdownMenu>
          </template>
        </Dropdown>
      </div>
    </div>
    <div class="c-clamp-scroll" @contextmenu.stop="onContextMenu">
      <div class="c-clamp-scroll__null" v-if="!clampList.length && !isLoading">--暂无数据--</div>
      <template v-show="isLoading">
        <div class="first-loading-wrp">
          <div class="loading-wrp">
            <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
          </div>
        </div>
      </template>
      <Scroll v-if="clampList.length" :on-reach-bottom="getClampList" :on-reach-edg="handleResetSearch" :height="scrollHeight">
        <FileItem
          v-for="item in clampList"
          :key="item.id"
          :record="item"
          :copy-ids="copyIds"
          @loadTemplate="handleLoadTemplate"
          @remove="handleRemove"
          @folder="handleFolder"
          @copy="(ids) => copyIds = ids"
        />
      </Scroll>
    </div>
  </div>
  <modalSzie :title="$t('importFiles.createDesign.title')" ref="modalSizeRef" @set="handleCreateCustomSize"/>
</template>

<script name="Clamp" setup>
import useClamp from '@/hooks/useClamp'
import ContextMenu from '@imengyu/vue3-context-menu'
import modalSzie from '@/components/common/modalSzie'
import FileItem from '@/components/clamp/FileItem'
import Loading from '@/components/load/index.js'
import { pasteFixture } from '@/api/v2'
import { $bus, EVENT_NAME } from '@/utils/mitt'
import { Input, Message, Modal } from 'view-ui-plus'

const {
  paths,
  isLoading,
  sortKey,
  pageConfig,
  CLAMP_TYPE,
  clampList,
  routerToClampId,
  createClampCanvas,
  getClampList,
  handleResetSearch,
  updateClampCanvas,
  handleClampIdDetail,
  getClampFolderList
} = useClamp()

const searchInputValue = ref('')
const copyIds = ref([])

const handleSortSearch = (key = '') => {
  sortKey.value = key
  handleResetSearch(pageConfig.parentId)
}

const modalSizeRef = ref(null)
const onContextMenu  = (e) => {
  e.preventDefault()
  const items = [
    {
      label: '刷新',
      onClick: () => {
        handleResetSearch(pageConfig.parentId)
      }
    },
    {
      label: '新建文件',
      onClick: () => {
        modalSizeRef.value.showSetSize()
      }
    },
    {
      label: '新建文件夹',
      onClick: () => {
        const fileName = ref('')
        Modal.confirm({
          title: '新建文件夹',
          render: (h) => {
            return h(Input, {
              size: 'default',
              modelValue: fileName,
              autofocus: true,
              placeholder: '请输入文件夹名称',
            })
          },
          onOk: async () => {
            createClampCanvas(0, 0, CLAMP_TYPE.FOLDER, fileName.value)
          }
        })
      }
    }
  ]
  if (copyIds.value.length) {
    items.splice(1, 0, {
      label: '粘贴',
      onClick: () => {
        Modal.confirm({
          title: '提示',
          content: `是否粘贴 <span class="c-paste-name">${copyIds.value.map(item => item.name).join('、')}</span> 到 <span class="c-remove-name">${paths.value.at(-1).name}</span> ？`,
          onOk: async () => {
            try {
              Loading.show('正在粘贴')
              const result = await pasteFixture({
                resourceIds: copyIds.value.map(item => item.id),
                targetId: pageConfig.parentId
              })
              if (result.code === 50000 || result.code === 50001) {
                throw new Error(result.message || '粘贴失败')
              }
              Message.success('粘贴成功')
              handleResetSearch(pageConfig.parentId)
            } catch (e) {
              Message.error(e.message)
            } finally {
              Loading.hide()
            }
          }
        })
      }
    })
  }
  ContextMenu.showContextMenu({
    x: e.x,
    y: e.y,
    customClass: 'c-main-menu',
    items
  })
}

const handleCreateCustomSize  = (width, height) => {
  $bus.emit(EVENT_NAME.IS_HIDE_SHOW_BUTTON, true)
  createClampCanvas(width, height, CLAMP_TYPE.FILE)
}

const handleLoadTemplate = async (record) => {
  $bus.emit(EVENT_NAME.FILE_NAME, record.name)
  await routerToClampId(record.id)
  handleClampIdDetail()
}

const handleRemove = (id) => {
  clampList.value = clampList.value.filter(item => item.id !== id)
}

const handleFolder = (record) => {
  paths.value.push({
    id: record.id,
    name: record.name
  })
  handleResetSearch(record.id)
}

const handlePathClick = (record) => {
  const index = paths.value.findIndex(item => item.id === record.id)
  paths.value = paths.value.slice(0, index + 1)
  handleResetSearch(record.id || 0)
}

const scrollHeight = ref(0)
const clampRef = ref(null)
onMounted(() => {
  nextTick(async () => {
    handleClampIdDetail()
    await getClampFolderList()
    scrollHeight.value = clampRef.value.clientHeight ? clampRef.value.clientHeight - 102 : 600
  })
  $bus.on(EVENT_NAME.SAVE_CLAMP_TEMPLATE, updateClampCanvas)
})
onUnmounted(() => {
  $bus.off(EVENT_NAME.SAVE_CLAMP_TEMPLATE, updateClampCanvas)
})
</script>

<style lang="less" scoped>
.c-clamp {
  display: flex;
  flex-direction: column;
  padding-top: 10px;
  height: calc(-50px + 100vh);
  overflow-y: auto;
}
.c-clamp-desc {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  margin-bottom: 10px;
  /deep/.ivu-breadcrumb {
    flex: 1;
    & > span {
      display: inline-block;
      cursor: pointer;
    }
  }
}
.c-clamp-desc__top {
  display: flex;
  align-items: center;
  justify-content: center;
}
.c-clamp-scroll {
  height: 100%;
  overflow: hidden;
}
.c-clamp-scroll__null {
  display: flex;
  justify-content: center;
  height: 100%;
}
</style>
<style lang="less">
.c-main-menu .mx-context-menu-item .label {
  overflow: auto;
  text-overflow: initial;
  width: 110px;
  text-align: center;
}
.c-paste-name {
  color: orangered;
}
</style>
