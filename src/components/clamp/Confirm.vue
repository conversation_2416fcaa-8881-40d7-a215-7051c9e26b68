<!-- file 夹具确认框 -->
<template>
  <span>{{ props.isClamp ? '确定选择该夹具吗？' : `打开该模板` }}</span>
  <div v-if="props.isClamp" class="c-clamp-entry">
    <Checkbox size="small" v-model="isChecked" type="checkbox" @on-change="handleClick" />
    <span>去掉当前模板设计图的白色背景（勾选时: 默认去白底）</span>
  </div>
  <div v-if="props.isClamp" class="c-clamp-entry__bottom">
    <ColorPicker v-model="color" size="small" alpha @on-change="handleClick" />
    <Slider :min="0" :max="1" :step="0.01" v-model="distance" @on-change="handleClick"></Slider>
  </div>
</template>

<script setup>
import {ColorPicker, Checkbox, Slider} from 'view-ui-plus'
const props = defineProps({
  isClamp: {
    type: Boolean,
    default: false
  },
  callBack: {
    type: Function,
    default: () => {}
  }
})

const isChecked = ref(false)
const color = ref('rgba(255,255,255,1)')
const distance = ref(0.16)

const handleClick = () => {
  props.callBack({ isChecked: isChecked.value, color: color.value, distance: distance.value })
}
</script>

<style lang="less">
.c-clamp-entry__bottom {
  margin-left: 26px;
  margin-top: 5px;
  display: flex;
  align-items: center;
  .ivu-slider {
    width: 200px;
    margin-left: 10px;
  }
}
</style>
