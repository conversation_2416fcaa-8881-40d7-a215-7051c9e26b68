<template>
  <ThemeToggle />
  <Button type="text" @click="handleFileMode">
    {{ isFileMode ? '画板模式' : '文件模式' }}
  </Button>
  <Button type="text" @click="preview">
    {{ $t('preview') }}
  </Button>
</template>

<script lang="ts" setup>
import { ImagePreview } from 'view-ui-plus';
import ThemeToggle from './ThemeToggle.vue';

const emits = defineEmits(['mode']);

const canvasEditor: any = inject('canvasEditor');
const preview = () => {
  canvasEditor.preview().then((dataUrl: string) => {
    ImagePreview.show({
      previewList: [dataUrl],
    });
  });
};

const isFileMode = ref(false)
const handleFileMode = () => {
  isFileMode.value = !isFileMode.value
  emits('mode', isFileMode.value)
}
</script>

<style scoped lang="less"></style>
