<template>
  <div style="display: inline-block">
    <Dropdown transfer-class-name="fix" @on-click="insertTypeHand">
      <a href="javascript:void(0)">
        {{ $t('insertFile.insert') }}
        <Icon type="ios-arrow-down"></Icon>
      </a>
      <template #list>
        <DropdownMenu>
          <DropdownItem name="insertImg">{{ $t('insertFile.insert_picture') }}</DropdownItem>
        </DropdownMenu>
      </template>
    </Dropdown>
    <Modal
      v-model="state.showModal"
      :title="$t('insertFile.modal_tittle')"
      @on-ok="insertTypeHand('insertSvgStr')"
      @on-cancel="showModal = false"
    >
      <Input
        v-model="state.svgStr"
        show-word-limit
        type="textarea"
        :placeholder="$t('insertFile.insert_SVGStr_placeholder')"
      />
    </Modal>
  </div>
</template>

<script name="ImportFile" setup>
import { Utils } from '@kuaitu/core';
const { getImgStr, selectFiles } = Utils;
import { cos } from '@/utils/cos'
import Loading from '@/components/load/index.js'

import useSelect from '@/hooks/select';
import { v4 as uuid } from 'uuid';
import dayjs from 'dayjs';

const { fabric, canvasEditor } = useSelect();
const state = reactive({
  showModal: false,
  svgStr: '',
});
let index = 0
const HANDLEMAP = {
  insertImg: function () {
    selectFiles({ accept: 'image/*', multiple: true }).then((fileList) => {
      Loading.show('正在上传图片')
      index = 0
      try {
        Array.from(fileList).forEach(async (item) => {
          const { Location } = await cos.uploadFileToTemplate(item, `${dayjs().format('YYYY-MM-DD_HH-mm-ss')}_${Math.ceil(Math.random() * 1000000)}template.png`)
          const fileSrc = `//${Location}`
          insertImgFile(fileSrc, fileList.length)
        });
      } catch (e) {
        Loading.hide()
      }
    });
  },
  insertSvg: function () {
    selectFiles({ accept: '.svg', multiple: true }).then((fileList) => {
      Array.from(fileList).forEach((item) => {
        getImgStr(item).then((file) => {
          insertSvgFile(file);
        });
      });
    });
  },
  insertSvgStrModal: function () {
    state.svgStr = '';
    state.showModal = true;
  },
  insertSvgStr: function () {
    fabric.loadSVGFromString(state.svgStr, (objects, options) => {
      const item = fabric.util.groupSVGElements(objects, {
        ...options,
        name: 'defaultSVG',
        id: uuid(),
      });
      canvasEditor.canvas.add(item).centerObject(item).renderAll();
    });
  },
};

const insertTypeHand = (type) => {
  const cb = HANDLEMAP[type];
  cb && typeof cb === 'function' && cb();
};
function insertImgFile(file, length) {
  if (!file) throw new Error('file is undefined')
  fabric.Image.fromURL(file, (image) => {
    image.set({
      id: uuid(),
      name: '图片1',
      left: 100,
      top: 100,
    })
    index = index + 1
    if (index === length) {
      Loading.hide()
    }
    canvasEditor.canvas.add(image)
    canvasEditor.canvas.setActiveObject(image)
    canvasEditor.canvas.renderAll()
  }, { crossOrigin: 'anonymous' })
}

function insertSvgFile(svgFile) {
  if (!svgFile) throw new Error('file is undefined');
  fabric.loadSVGFromURL(svgFile, (objects, options) => {
    const item = fabric.util.groupSVGElements(objects, {
      ...options,
      name: 'defaultSVG',
      id: uuid(),
    });
    canvasEditor.canvas.add(item).centerObject(item).renderAll();
  });
}
</script>

<style scoped lang="less">
:deep(.ivu-select-dropdown) {
  z-index: 999;
}
</style>
