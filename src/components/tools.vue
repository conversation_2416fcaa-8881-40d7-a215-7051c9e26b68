<template>
  <div>
    <Divider plain orientation="left">{{ $t('common_elements') }}</Divider>
    <div class="tool-box">
      <span @click="() => addTextBox()" :draggable="true" @dragend="onDragend('textBox', $event)">
        <svg
          t="1650854954008"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="14038"
          width="26"
          height="26"
        >
          <path
            d="M720.832 692.352h-12.64L530.208 260.448a19.968 19.968 0 0 0-36.416 0L316.608 692.352h-13.44c-7.904 0-15.04 3.968-17.408 11.072a19.872 19.872 0 0 0 18.208 27.68h56.96c9.504 0 18.208-6.336 19.776-15.808a18.752 18.752 0 0 0-15.808-21.344l36.384-87.808h159.776l34.816 87.808a18.88 18.88 0 0 0-15.808 21.344c1.568 9.504 10.272 15.808 19.776 15.808h121.024c7.904 0 15.04-3.968 17.408-11.072a19.2 19.2 0 0 0-17.408-27.68z m-306.112-125.76l64.864-158.208 64.864 158.208H414.72z m-246.816-80.704c0 6.336 5.536 11.872 11.872 11.872s11.872-5.536 11.872-11.872v-23.744c0-6.336-5.536-11.872-11.872-11.872s-11.872 5.536-11.872 11.872v23.744z m0-75.936c0 6.336 5.536 11.872 11.872 11.872s11.872-5.536 11.872-11.872v-23.744c0-6.336-5.536-11.872-11.872-11.872s-11.872 5.536-11.872 11.872v23.744z m0 151.872c0 6.336 5.536 11.872 11.872 11.872s11.872-5.536 11.872-11.872v-23.744c0-6.336-5.536-11.872-11.872-11.872s-11.872 5.536-11.872 11.872v23.744z m11.872-216.736a12.16 12.16 0 0 0 11.872-11.872v-23.744c-0.8-6.336-5.536-11.072-11.872-11.072s-11.872 5.536-11.872 11.872v23.744c0 6.336 4.736 11.072 11.872 11.072z m-11.872 292.672c0 6.336 5.536 11.872 11.872 11.872s11.872-5.536 11.872-11.872v-23.744c0-6.336-5.536-11.872-11.872-11.872s-11.872 5.536-11.872 11.872v23.744z m0 75.936c0 7.104 4.736 11.872 11.872 11.872a12.16 12.16 0 0 0 11.872-11.872v-23.744c0-6.336-5.536-11.872-11.872-11.872s-11.872 5.536-11.872 11.872v23.744z m665.248-227.808c0 6.336 5.536 11.872 11.872 11.872s11.872-5.536 11.872-11.872v-23.744c0-6.336-5.536-11.872-11.872-11.872s-11.872 5.536-11.872 11.872v23.744z m0-75.936c0 6.336 5.536 11.872 11.872 11.872s11.872-5.536 11.872-11.872v-23.744c0-6.336-5.536-11.872-11.872-11.872s-11.872 5.536-11.872 11.872v23.744z m0 151.872c0 6.336 5.536 11.872 11.872 11.872s11.872-5.536 11.872-11.872v-23.744c0-6.336-5.536-11.872-11.872-11.872s-11.872 5.536-11.872 11.872v23.744z m11.072-216.736a12.16 12.16 0 0 0 11.872-11.872v-23.744c0-6.336-5.536-11.872-11.872-11.872s-11.872 5.536-11.872 11.872v23.744c0.8 7.104 5.536 11.872 11.872 11.872z m-11.072 292.672c0 6.336 5.536 11.872 11.872 11.872s11.872-5.536 11.872-11.872v-23.744c0-6.336-5.536-11.872-11.872-11.872s-11.872 5.536-11.872 11.872v23.744z m0 75.936c0 6.336 5.536 11.872 11.872 11.872s11.872-5.536 11.872-11.872v-23.744c0-6.336-5.536-11.872-11.872-11.872s-11.872 5.536-11.872 11.872v23.744z m-347.264 119.456h-23.744c-6.336 0-11.872 5.536-11.872 11.872s5.536 11.872 11.872 11.872h23.744c6.336 0 11.872-5.536 11.872-11.872s-5.536-11.872-11.872-11.872z m-75.936 0h-23.744c-6.336 0-11.872 5.536-11.872 11.872s5.536 11.872 11.872 11.872h23.744c6.336 0 11.872-5.536 11.872-11.872s-5.536-11.872-11.872-11.872z m151.872 0h-23.744c-6.336 0-11.872 5.536-11.872 11.872s5.536 11.872 11.872 11.872h23.744c6.336 0 11.872-5.536 11.872-11.872s-5.536-11.872-11.872-11.872z m-228.608 0h-23.744c-6.336 0-11.872 5.536-11.872 11.872s5.536 11.872 11.872 11.872h23.744c6.336 0 11.872-5.536 11.872-11.872s-4.736-11.872-11.872-11.872z m-75.136-7.904H222.496v-11.072a12.48 12.48 0 0 0-12.672-12.64h-18.976v-37.984c0-7.104-5.536-12.64-11.872-12.64s-11.872 5.536-11.872 12.64v37.984h-10.272a12.512 12.512 0 0 0-12.672 12.64v52.992a12.48 12.48 0 0 0 12.672 12.64h52.992a12.512 12.512 0 0 0 12.672-12.64v-11.072h35.584c7.104-1.568 12.672-7.904 12.672-14.24 0-7.104-12.672-16.608-12.672-16.608z m379.68 7.904h-23.744c-6.336 0-11.872 5.536-11.872 11.872s5.536 11.872 11.872 11.872h23.744c6.336 0 11.872-5.536 11.872-11.872s-5.536-11.872-11.872-11.872z m75.936 0h-23.744c-6.336 0-11.872 5.536-11.872 11.872s5.536 11.872 11.872 11.872h23.744c6.336 0 11.872-5.536 11.872-11.872s-4.736-11.872-11.872-11.872z m-251.52-642.304h23.744c6.336 0 11.872-5.536 11.872-11.872s-5.536-11.872-11.872-11.872h-23.744c-6.336 0-11.872 5.536-11.872 11.872s5.536 11.872 11.872 11.872z m-75.936 0h23.744c6.336 0 11.872-5.536 11.872-11.872s-5.536-11.872-11.872-11.872h-23.744c-6.336 0-11.872 5.536-11.872 11.872s5.536 11.872 11.872 11.872z m151.872 0h23.744c6.336 0 11.872-5.536 11.872-11.872s-5.536-11.872-11.872-11.872h-23.744c-6.336 0-11.872 5.536-11.872 11.872s5.536 11.872 11.872 11.872z m-227.808 0h23.744c6.336 0 11.872-5.536 11.872-11.872s-5.536-11.872-11.872-11.872h-23.744c-7.104 0.8-11.872 5.536-11.872 12.672s4.736 11.072 11.872 11.072zM257.28 167.904H221.696v-11.072a12.512 12.512 0 0 0-12.672-12.672H156.832a12.512 12.512 0 0 0-12.672 12.672v52.992c0 7.104 5.536 12.672 12.672 12.672h11.072v35.584c1.568 7.104 7.904 12.672 14.24 12.672s16.608-12.672 16.608-12.672V222.496h11.072a12.512 12.512 0 0 0 12.672-12.672v-18.976h35.584a12.16 12.16 0 0 0 11.872-11.872 12.224 12.224 0 0 0-12.672-11.072z m356.768 22.944h23.744c6.336 0 11.872-5.536 11.872-11.872s-5.536-11.872-11.872-11.872h-23.744c-6.336 0-11.872 5.536-11.872 11.872s5.536 11.872 11.872 11.872z m76.736 0h23.744c6.336 0 11.072-4.736 11.072-11.072s-5.536-11.872-11.872-11.872h-23.744c-6.336 0-11.872 5.536-11.872 11.872s5.536 11.072 12.64 11.072z m176.384-46.656h-52.992a12.48 12.48 0 0 0-12.64 12.672v11.072h-35.584c-7.104 1.568-12.64 7.904-12.64 14.24s12.64 16.608 12.64 16.608h35.584v11.072c0 7.104 5.536 12.672 12.64 12.672h18.976v37.984c0 7.104 5.536 12.672 11.872 12.672s11.872-5.536 11.872-12.672V222.528h11.072a12.48 12.48 0 0 0 12.64-12.672V156.864a13.76 13.76 0 0 0-13.44-12.672z m0 657.312h-11.072v-35.584c-1.568-7.104-7.904-12.64-14.24-12.64-7.104 0-16.608 12.64-16.608 12.64v35.584h-11.072a12.48 12.48 0 0 0-12.64 12.64v18.976h-35.584c-6.336 0-11.872 5.536-11.872 11.872s5.536 11.872 11.872 11.872h35.584v11.072a12.48 12.48 0 0 0 12.64 12.64h52.992a12.48 12.48 0 0 0 12.64-12.64v-52.992c0-7.904-5.536-13.44-12.64-13.44z"
            p-id="14039"
          ></path>
        </svg>
      </span>
      <span @click="() => addRect()" :draggable="true" @dragend="onDragend('rect', $event)">
        <svg
          t="1650855811131"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="18499"
          width="26"
          height="26"
        >
          <path
            d="M864 896H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h704a32 32 0 0 1 32 32v704a32 32 0 0 1-32 32zM192 832h640V192H192v640z"
            p-id="18500"
          ></path>
        </svg>
      </span>
      <span @click="() => addCircle()" :draggable="true" @dragend="onDragend('circle', $event)">
        <svg
          t="1650855860236"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="19440"
          width="26"
          height="26"
        >
          <path
            d="M512 928C282.624 928 96 741.376 96 512S282.624 96 512 96s416 186.624 416 416-186.624 416-416 416z m0-768C317.92 160 160 317.92 160 512s157.92 352 352 352 352-157.92 352-352S706.08 160 512 160z"
            p-id="19441"
          ></path>
        </svg>
      </span>
      <span @click="() => addTriangle()" :draggable="true" @dragend="onDragend('triangle', $event)">
        <svg
          t="1650874633978"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="2032"
          width="26"
          height="26"
        >
          <path
            d="M928.64 896a2.144 2.144 0 0 1-0.64 0H96a32.032 32.032 0 0 1-27.552-48.288l416-704c11.488-19.456 43.552-19.456 55.104 0l413.152 699.2A31.936 31.936 0 0 1 928.64 896zM152.064 832h719.84L512 222.912 152.064 832z"
            p-id="2033"
          ></path>
        </svg>
      </span>
      <span @click="() => addPolygon()" :draggable="true" @dragend="onDragend('polygon', $event)">
        <svg
          t="1650874633978"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="2032"
          width="26"
          height="26"
        >
          <path
            d="M161.152 398.016l134.016 412.416h433.664l134.016-412.416L512 143.104 161.152 398.08zM512 64l426.048 309.568-162.752 500.864H248.704L85.952 373.568 512 64z"
            p-id="2033"
          ></path>
        </svg>
      </span>
      <span
        @click="() => addCurvedText()"
        :draggable="true"
        @dragend="onDragend('curvedText', $event)"
      >
        <svg
          t="1719824533076"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="4287"
          width="20"
          height="20"
        >
          <path
            d="M523.008 733.44c189.2608 0 362.88 81.792 423.5264 197.7344a34.56 34.56 0 0 1-61.2352 32.0512c-47.2576-90.3424-196.5568-160.6656-362.2656-160.6656-173.4656 0-321.792 68.3264-363.648 159.104a34.56 34.56 0 0 1-62.7712-28.928c54.9888-119.296 228.6592-199.296 426.4192-199.296zM754.1504 716.8c5.2736 0 9.856-0.8448 13.824-2.56 7.8592-2.2528 14.08-6.0928 18.7136-11.52 4.5568-5.2992 6.9888-11.7504 6.912-18.3552a21.7856 21.7856 0 0 0-2.9696-11.0848L548.0192 73.3952a37.7344 37.7344 0 0 0-14.7712-16.2304A41.0624 41.0624 0 0 0 511.5392 51.2a36.7104 36.7104 0 0 0-21.1968 6.4 43.4688 43.4688 0 0 0-14.3104 15.7952L232.448 673.28c-3.2768 8.1408-2.6112 17.0752 1.8688 24.7808 4.4544 7.7312 12.288 13.568 21.7856 16.1792 3.9936 1.7152 8.3712 2.5856 12.8256 2.56 16.1792 0.1024 30.5408-8.8832 35.5072-22.1952l61.1328-156.16h292.9152l61.1584 156.16c2.5856 6.8352 7.04 12.1856 13.312 16.2304 6.1184 3.9424 13.568 6.0416 21.1968 5.9648z m-118.3488-235.52h-247.552l123.2896-314.88 124.2624 314.88z"
            fill="#64646E"
            p-id="4288"
          ></path>
        </svg>
      </span>
      <span
        @click="() => addCurvedText({flipped: true})"
        :draggable="true"
        @dragend="onDragend('curvedText', $event)"
      >
        <svg
          t="1719824533076"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="4287"
          width="20"
          height="20"
        >
          <path
            d="M523.008 733.44c189.2608 0 362.88 81.792 423.5264 197.7344a34.56 34.56 0 0 1-61.2352 32.0512c-47.2576-90.3424-196.5568-160.6656-362.2656-160.6656-173.4656 0-321.792 68.3264-363.648 159.104a34.56 34.56 0 0 1-62.7712-28.928c54.9888-119.296 228.6592-199.296 426.4192-199.296zM754.1504 716.8c5.2736 0 9.856-0.8448 13.824-2.56 7.8592-2.2528 14.08-6.0928 18.7136-11.52 4.5568-5.2992 6.9888-11.7504 6.912-18.3552a21.7856 21.7856 0 0 0-2.9696-11.0848L548.0192 73.3952a37.7344 37.7344 0 0 0-14.7712-16.2304A41.0624 41.0624 0 0 0 511.5392 51.2a36.7104 36.7104 0 0 0-21.1968 6.4 43.4688 43.4688 0 0 0-14.3104 15.7952L232.448 673.28c-3.2768 8.1408-2.6112 17.0752 1.8688 24.7808 4.4544 7.7312 12.288 13.568 21.7856 16.1792 3.9936 1.7152 8.3712 2.5856 12.8256 2.56 16.1792 0.1024 30.5408-8.8832 35.5072-22.1952l61.1328-156.16h292.9152l61.1584 156.16c2.5856 6.8352 7.04 12.1856 13.312 16.2304 6.1184 3.9424 13.568 6.0416 21.1968 5.9648z m-118.3488-235.52h-247.552l123.2896-314.88 124.2624 314.88z"
            fill="#64646E"
            p-id="4288"
          ></path>
        </svg>
      </span>
    </div>
    <Divider plain orientation="left">{{ $t('draw_elements') }}</Divider>
    <div class="tool-box">
      <span
        @click="drawingLineModeSwitch('line')"
        :class="state.isDrawingLineMode && state.lineType === 'line' && 'bg'"
      >
        <svg
          t="1673022047861"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="4206"
          width="20"
          height="20"
        >
          <path
            d="M187.733333 1024h-170.666666c-10.24 0-17.066667-6.826667-17.066667-17.066667v-170.666666c0-10.24 6.826667-17.066667 17.066667-17.066667h170.666666c10.24 0 17.066667 6.826667 17.066667 17.066667v170.666666c0 10.24-6.826667 17.066667-17.066667 17.066667zM34.133333 989.866667h136.533334v-136.533334H34.133333v136.533334zM1006.933333 204.8h-170.666666c-10.24 0-17.066667-6.826667-17.066667-17.066667v-170.666666c0-10.24 6.826667-17.066667 17.066667-17.066667h170.666666c10.24 0 17.066667 6.826667 17.066667 17.066667v170.666666c0 10.24-6.826667 17.066667-17.066667 17.066667zM853.333333 170.666667h136.533334V34.133333h-136.533334v136.533334z"
            fill=""
            p-id="4207"
          ></path>
          <path
            d="M187.733333 853.333333c-3.413333 0-10.24 0-13.653333-3.413333-6.826667-6.826667-6.826667-17.066667 0-23.893333l648.533333-648.533334c6.826667-6.826667 17.066667-6.826667 23.893334 0s6.826667 17.066667 0 23.893334l-648.533334 648.533333c0 3.413333-6.826667 3.413333-10.24 3.413333z"
            fill=""
            p-id="4208"
          ></path>
        </svg>
      </span>
      <span
        @click="drawingLineModeSwitch('arrow')"
        :class="state.isDrawingLineMode && state.lineType === 'arrow' && 'bg'"
      >
        <svg
          t="1673026778912"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="2659"
          width="20"
          height="20"
        >
          <path
            d="M320 738.133333L827.733333 230.4l-29.866666-29.866667L290.133333 708.266667v-268.8h-42.666666v341.333333h341.333333v-42.666667H320z"
            fill="#444444"
            p-id="2660"
          ></path>
        </svg>
      </span>
      <span
        @click="drawingLineModeSwitch('thinTailArrow')"
        :class="state.isDrawingLineMode && state.lineType === 'thinTailArrow' && 'bg'"
      >
        <svg
          t="1715323097309"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="24572"
          width="20"
          height="20"
        >
          <path
            d="M485.954269 735.978673 643.38051 811.107852C688.831327 832.798434 686.17686 860.459274 635.3838 871.903075L81.378406 996.721881C31.19882 1008.027444-1.313538 976.266799 10.130269 925.473745L134.949081 371.468357C146.254656 321.288783 173.611855 317.095036 195.744311 363.471653L270.873453 520.897858 903.670271 62.052983C986.301645 2.136458 1004.805285 20.426857 944.799125 103.181838L485.954269 735.978673Z"
            fill="#444444"
            p-id="24573"
          ></path>
        </svg>
      </span>
      <span
        @click="drawPolygon"
        :class="state.isDrawingLineMode && state.lineType === 'polygon' && 'bg'"
      >
        <svg
          t="1650874633978"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="203255"
          width="26"
          height="26"
        >
          <path
            d="M161.152 398.016l134.016 412.416h433.664l134.016-412.416L512 143.104 161.152 398.08zM512 64l426.048 309.568-162.752 500.864H248.704L85.952 373.568 512 64z"
            p-id="203355"
          ></path>
        </svg>
      </span>

      <span
        @click="freeDraw"
        :class="state.isDrawingLineMode && state.lineType === 'freeDraw' && 'bg'"
      >
        <Icon type="md-brush" :size="22" />
      </span>
    </div>
    <Divider plain orientation="left">{{ $t('code_img') }}</Divider>
    <div class="tool-box">
      <span @click="canvasEditor.addQrCode">
        <svg
          t="1717679888665"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          p-id="4558"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
        >
          <path
            d="M386.612245 470.204082H177.632653c-45.97551 0-83.591837-37.616327-83.591837-83.591837V177.632653c0-45.97551 37.616327-83.591837 83.591837-83.591837h208.979592c45.97551 0 83.591837 37.616327 83.591837 83.591837v208.979592c0 45.97551-37.616327 83.591837-83.591837 83.591837zM177.632653 135.836735c-22.987755 0-41.795918 18.808163-41.795918 41.795918v208.979592c0 22.987755 18.808163 41.795918 41.795918 41.795918h208.979592c22.987755 0 41.795918-18.808163 41.795918-41.795918V177.632653c0-22.987755-18.808163-41.795918-41.795918-41.795918H177.632653zM846.367347 470.204082h-208.979592c-45.97551 0-83.591837-37.616327-83.591837-83.591837V177.632653c0-45.97551 37.616327-83.591837 83.591837-83.591837h208.979592c45.97551 0 83.591837 37.616327 83.591837 83.591837v208.979592c0 45.97551-37.616327 83.591837-83.591837 83.591837z m-208.979592-334.367347c-22.987755 0-41.795918 18.808163-41.795918 41.795918v208.979592c0 22.987755 18.808163 41.795918 41.795918 41.795918h208.979592c22.987755 0 41.795918-18.808163 41.795918-41.795918V177.632653c0-22.987755-18.808163-41.795918-41.795918-41.795918h-208.979592z"
            fill="#333333"
            p-id="4559"
          ></path>
          <path
            d="M773.22449 344.816327h-62.693878c-17.240816 0-31.346939-14.106122-31.346939-31.346939V250.77551c0-17.240816 14.106122-31.346939 31.346939-31.346939h62.693878c17.240816 0 31.346939 14.106122 31.346939 31.346939v62.693878c0 17.240816-14.106122 31.346939-31.346939 31.346939zM313.469388 344.816327H250.77551c-17.240816 0-31.346939-14.106122-31.346939-31.346939V250.77551c0-17.240816 14.106122-31.346939 31.346939-31.346939h62.693878c17.240816 0 31.346939 14.106122 31.346939 31.346939v62.693878c0 17.240816-14.106122 31.346939-31.346939 31.346939zM313.469388 804.571429H250.77551c-17.240816 0-31.346939-14.106122-31.346939-31.346939v-62.693878c0-17.240816 14.106122-31.346939 31.346939-31.346939h62.693878c17.240816 0 31.346939 14.106122 31.346939 31.346939v62.693878c0 17.240816-14.106122 31.346939-31.346939 31.346939zM574.693878 929.959184c-11.493878 0-20.897959-9.404082-20.89796-20.89796v-271.673469c0-45.97551 37.616327-83.591837 83.591837-83.591837h41.795918c45.97551 0 83.591837 37.616327 83.591837 83.591837v62.693878c0 22.987755 18.808163 41.795918 41.795919 41.795918h41.795918c22.987755 0 41.795918-18.808163 41.795918-41.795918v-125.387755c0-11.493878 9.404082-20.897959 20.897959-20.89796s20.897959 9.404082 20.89796 20.89796v125.387755c0 45.97551-37.616327 83.591837-83.591837 83.591836h-41.795918c-45.97551 0-83.591837-37.616327-83.591837-83.591836v-62.693878c0-22.987755-18.808163-41.795918-41.795919-41.795918h-41.795918c-22.987755 0-41.795918 18.808163-41.795918 41.795918v271.673469c0 11.493878-9.404082 20.897959-20.897959 20.89796zM909.061224 929.959184h-167.183673c-11.493878 0-20.897959-9.404082-20.897959-20.89796s9.404082-20.897959 20.897959-20.897959h167.183673c11.493878 0 20.897959 9.404082 20.89796 20.897959s-9.404082 20.897959-20.89796 20.89796z"
            fill="#333333"
            p-id="4560"
          ></path>
          <path
            d="M386.612245 929.959184H177.632653c-45.97551 0-83.591837-37.616327-83.591837-83.591837v-208.979592c0-45.97551 37.616327-83.591837 83.591837-83.591837h208.979592c45.97551 0 83.591837 37.616327 83.591837 83.591837v208.979592c0 45.97551-37.616327 83.591837-83.591837 83.591837z m-208.979592-334.367347c-22.987755 0-41.795918 18.808163-41.795918 41.795918v208.979592c0 22.987755 18.808163 41.795918 41.795918 41.795918h208.979592c22.987755 0 41.795918-18.808163 41.795918-41.795918v-208.979592c0-22.987755-18.808163-41.795918-41.795918-41.795918H177.632653z"
            fill="#333333"
            p-id="4561"
          ></path>
        </svg>
      </span>
      <span
        @click="canvasEditor.addBarcode"
        :class="state.isDrawingLineMode && state.lineType === 'arrow' && 'bg'"
      >
        <svg
          t="1717679973041"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="5747"
          width="20"
          height="20"
        >
          <path
            d="M876.893 63.84h20.44c34.501 0 62.49 30.402 62.49 64.919V229.7h-0.13c0 16.569-11.355 22.409-22.855 22.409s-22.854-7.734-22.854-19.234c0-0.211-3.92-0.373-3.92-0.583v-82.72c0-23-10.56-35.975-33.56-35.975h-61.47c-0.21 0-1.36-2.786-1.555-2.786-11.5 0-21.817-12.213-21.817-23.713s18.885-20.813 18.885-20.813V63.84h66.345zM213.45 66.286V63.84h-86.914c-34.5 0-62.36 30.402-62.36 64.919V229.7c0 16.569 9.33 22.409 20.83 22.409s24.927-8.528 24.927-20.028c0-0.211 4.001 0.421 4.001 0.21v-82.72c0-23 10.432-35.974 33.432-35.974h63.008c0.194 0 0.372-2.786 0.583-2.786 11.5 0 22.06-10.772 22.06-22.263 0-11.5-2.98-22.263-19.567-22.263z m16.587 500.74V246.27h-33.173v530.764h33.173V567.026zM109.933 795.141c0-11.5-11.37-21.576-22.87-21.576s-22.888 3.468-22.888 20.053v105.638c0 34.5 27.86 60.224 62.36 60.224h83.837c0.194 0 0.372 0.679 0.583 0.679 11.5 0 22.06-9.038 22.06-20.538s-2.98-20.538-19.566-20.538v7.224h-66.344v-6.737c-33.173-2.624-33.173-19.923-33.173-41.14v-82.704c0.001-0.213-3.999-0.39-3.999-0.585z m186.448-228.115V246.27h-16.586V694.1h16.586V567.026z m16.585 160.249h-33.172v49.758h33.172v-49.758z m66.344-160.249V246.27h-16.585V694.1h16.585V567.026z m16.586 160.249h-33.171v49.758h33.171v-49.758z m82.931-160.249V246.27h-66.344V694.1h66.344V567.026z m0 210.007v-49.758h-33.173v49.758h33.173z m132.688-210.007V246.27h-49.758V694.1h49.758V567.026z m-82.93 210.007h33.172v-49.758h-33.172v49.758zM661.273 566.41V246.27H644.69V694.1h16.584V566.41z m-33.171 210.623h33.171v-49.758h-33.171v49.758zM744.205 566.41V246.27h-16.587V694.1h16.587V566.41z m-33.173 210.623h33.173v-49.758h-33.173v49.758z m116.103-210.007V246.27h-33.172v530.764h33.172V567.026z m70.198 392.454c34.5 0 62.489-25.723 62.489-60.224V793.618h-0.13c0-16.585-9.33-20.053-20.83-20.053s-24.88 9.688-24.88 21.188c0 0.226-3.918 0.76-3.918 0.97v82.705c0 20.7 0 37.738-33.172 40.978v6.899h-66.345v-7.224s-18.885 9.33-18.885 20.83 10.317 20.246 21.817 20.246c0.193 0 1.344-0.68 1.555-0.68h82.299z"
            p-id="5748"
          ></path>
        </svg>
      </span>
    </div>
    <Divider plain orientation="left">日历</Divider>
    <div class="tool-box carender-wrap">
      <calendar-com></calendar-com>
    </div>
  </div>
</template>

<script setup name="Tools">
import { v4 as uuid } from 'uuid';

import { getPolygonVertices } from '@/utils/math';
import useSelect from '@/hooks/select';
import useCalculate from '@/hooks/useCalculate';
import { useI18n } from 'vue-i18n';
import calendar from '@/utils/calendar.js'
import calendarList from '@/utils/calendarList.js'
import calendarCom from './calendarCom.vue'

const LINE_TYPE = {
  polygon: 'polygon',
  freeDraw: 'freeDraw',
  pathText: 'pathText',
};
const defaultPosition = { shadow: '', fontFamily: 'arial' };
const dragOption = {
  left: 0,
  top: 0,
};
const { t } = useI18n();
const { fabric, canvasEditor } = useSelect();
const { getCanvasBound, isOutsideCanvas } = useCalculate();
const state = reactive({
  isDrawingLineMode: false,
  lineType: false,
});

const addText = (option) => {
  cancelDraw();
  const text = new fabric.IText(t('everything_is_fine'), {
    ...defaultPosition,
    ...option,
    fontSize: 80,
    fill: '#000000FF',
    id: uuid(),
  });
  canvasEditor.canvas.add(text);
  canvasEditor.canvas.setActiveObject(text);
  if (!option) {
    canvasEditor.position('center');
  }
};

const addTextBox = (option) => {
  cancelDraw();
  const text = new fabric.IText(t('everything_goes_well'), {
    ...defaultPosition,
    ...option,
    splitByGrapheme: true,
    paintFirst: 'stroke',
    fontSize: 80,
    fill: '#000000FF',
    id: uuid(),
  });
  console.log({
    ...defaultPosition,
    ...option,
    splitByGrapheme: true,
    paintFirst: 'stroke',
    fontSize: 80,
    fill: '#000000FF',
    id: uuid(),
  })
  canvasEditor.canvas.add(text);
  canvasEditor.canvas.setActiveObject(text);
  // text.on('changed', function() {
  //   const activeObject = canvasEditor.canvas.getActiveObjects()[0]
  //   activeObject.set('text', '12323')
  // });
  //  text.off('changed'
  if (!option) {
    canvasEditor.position('center');
  }
};

const addTriangle = (option) => {
  cancelDraw();
  const triangle = new fabric.Triangle({
    ...defaultPosition,
    ...option,
    width: 400,
    height: 400,
    fill: '#92706BFF',
    id: uuid(),
    name: '三角形',
  });
  canvasEditor.canvas.add(triangle);
  canvasEditor.canvas.setActiveObject(triangle);
  if (!option) {
    canvasEditor.position('center');
  }
};

const addPolygon = (option) => {
  cancelDraw();
  const polygon = new fabric.Polygon(getPolygonVertices(5, 200), {
    ...defaultPosition,
    ...option,
    fill: '#CCCCCCFF',
    id: uuid(),
    name: '多边形',
  });
  polygon.set({
    width: 400,
    height: 400,
    pathOffset: {
      x: 0,
      y: 0,
    },
  });
  canvasEditor.canvas.add(polygon);
  canvasEditor.canvas.setActiveObject(polygon);
  if (!option) {
    canvasEditor.position('center');
  }
};

const addCircle = (option) => {
  cancelDraw();
  const circle = new fabric.Circle({
    ...defaultPosition,
    ...option,
    radius: 150,
    fill: '#57606BFF',
    id: uuid(),
    name: '圆形',
  });
  canvasEditor.canvas.add(circle);
  canvasEditor.canvas.setActiveObject(circle);
  if (!option) {
    canvasEditor.position('center');
  }
};

const addRect = (option) => {
  cancelDraw();
  const rect = new fabric.Rect({
    ...defaultPosition,
    ...option,
    fill: '#F57274FF',
    width: 400,
    height: 400,
    id: uuid(),
    name: '矩形',
  });
  canvasEditor.canvas.add(rect);
  canvasEditor.canvas.setActiveObject(rect);
  if (!option) {
    canvasEditor.position('center');
  }
};
const drawPolygon = () => {
  const onEnd = () => {
    state.lineType = false;
    state.isDrawingLineMode = false;
    ensureObjectSelEvStatus(!state.isDrawingLineMode, !state.isDrawingLineMode);
  };
  if (state.lineType !== LINE_TYPE.polygon) {
    endConflictTools();
    endDrawingLineMode();
    state.lineType = LINE_TYPE.polygon;
    state.isDrawingLineMode = true;
    canvasEditor.beginDrawPolygon(onEnd);
    canvasEditor.endDraw();
    ensureObjectSelEvStatus(!state.isDrawingLineMode, !state.isDrawingLineMode);
  } else {
    canvasEditor.discardPolygon();
  }
};

const drawPathText = () => {
  if (state.lineType === LINE_TYPE.pathText) {
    state.lineType = false;
    state.isDrawingLineMode = false;
    canvasEditor.endTextPathDraw();
  } else {
    endConflictTools();
    endDrawingLineMode();
    state.lineType = LINE_TYPE.pathText;
    state.isDrawingLineMode = true;
    canvasEditor.startTextPathDraw();
  }
};

const freeDraw = () => {
  if (state.lineType === LINE_TYPE.freeDraw) {
    canvasEditor.endDraw();
    state.lineType = false;
    state.isDrawingLineMode = false;
  } else {
    endConflictTools();
    endDrawingLineMode();
    state.lineType = LINE_TYPE.freeDraw;
    state.isDrawingLineMode = true;
    canvasEditor.startDraw({ width: 20 });
  }
};

const endConflictTools = () => {
  canvasEditor.discardPolygon();
  canvasEditor.endDraw();
  canvasEditor.endTextPathDraw();
};
const endDrawingLineMode = () => {
  state.isDrawingLineMode = false;
  state.lineType = '';
  canvasEditor.setMode(state.isDrawingLineMode);
  canvasEditor.setLineType(state.lineType);
};
const drawingLineModeSwitch = (type) => {
  if ([LINE_TYPE.polygon, LINE_TYPE.freeDraw, LINE_TYPE.pathText].includes(state.lineType)) {
    endConflictTools();
  }
  if (state.lineType === type) {
    state.isDrawingLineMode = false;
    state.lineType = '';
  } else {
    state.isDrawingLineMode = true;
    state.lineType = type;
  }
  canvasEditor.setMode(state.isDrawingLineMode);
  canvasEditor.setLineType(type);
  ensureObjectSelEvStatus(!state.isDrawingLineMode, !state.isDrawingLineMode);
};

const ensureObjectSelEvStatus = (evented, selectable) => {
  canvasEditor.canvas.forEachObject((obj) => {
    if (obj.id !== 'workspace') {
      obj.selectable = selectable;
      obj.evented = evented;
    }
  });
};

const addCurvedText = (option = {}) => {
  const text = new fabric.CurvedText({
    paintFirst: 'stroke',
    ...defaultPosition,
    ...option,
    text: '可视化设计系统',
    diameter: 360,
    kerning: 9,
    fontSize: 32,
    fill: 'rgb(254, 216, 53)',
    id: uuid()
  });


  console.log({
    paintFirst: 'stroke',
    ...defaultPosition,
    ...option,
    text: '可视化设计系统',
    diameter: 360,
    kerning: 9,
    fontSize: 32,
    fill: 'rgb(254, 216, 53)',
    id: uuid(),
  })

  canvasEditor.canvas.add(text);
  canvasEditor.canvas.setActiveObject(text);
};

const onDragend = (type, e) => {
  if (isOutsideCanvas(e.clientX, e.clientY)) return;
  switch (type) {
    case 'text':
      addText(dragOption);
      break;
    case 'textBox':
      addTextBox(dragOption);
      break;
    case 'rect':
      addRect(dragOption);
      break;
    case 'circle':
      addCircle(dragOption);
      break;
    case 'triangle':
      addTriangle(dragOption);
      break;
    case 'polygon':
      addPolygon(dragOption);
      break;
    case 'curvedText':
      addCurvedText(dragOption);
      break;
    default:
  }
};

onMounted(() => {
  nextTick(() => {
    canvasEditor.canvas.on('drop', (opt) => {
      const { left, top } = getCanvasBound();
      const offset = { left, top };
      const point = {
        x: opt.e.x - offset.left,
        y: opt.e.y - offset.top,
      };

      const pointerVpt = canvasEditor.canvas.restorePointerVpt(point);
      dragOption.left = pointerVpt.x;
      dragOption.top = pointerVpt.y;
    });
  });

});

const cancelDraw = () => {
  if (!state.isDrawingLineMode) return;
  state.isDrawingLineMode = false;
  state.lineType = '';
  canvasEditor.setMode(false);
  endConflictTools();
  ensureObjectSelEvStatus(true, true);
};

const addImg = data => {
  const { name='', url } = data

  const imgEl = document.createElement('img');
  imgEl.src = url;
  document.body.appendChild(imgEl);
  imgEl.onload = () => {
    const imgInstance = new fabric.Image(imgEl, {
      id: uuid(),
      name,
      left: 100,
      top: 100,
    });
    canvasEditor.canvas.add(imgInstance);
    canvasEditor.canvas.setActiveObject(imgInstance);
    canvasEditor.canvas.renderAll();
    imgEl.remove();
  };
}

const calendarmodal = ref(false)
const formData = reactive({})
const formRef = ref(null)

onDeactivated(() => {
  cancelDraw();
});
</script>

<style scoped lang="less">
.tool-box {
  display: flex;
  justify-content: space-around;
  span {
    flex: 1;
    text-align: center;
    padding: 5px 0;
    background: #f6f6f6;
    margin-left: 2px;
    cursor: pointer;
    &:hover {
      background: #edf9ff;
      svg {
        fill: #2d8cf0;
      }
    }
  }
  .bg {
    background: #d8d8d8;

    &:hover {
      svg {
        fill: #2d8cf0;
      }
    }
  }
}
.img {
  width: 20px;
}
.carender-wrap {
  display: flex;
  flex-flow: row wrap;
}
.carender-item {
  width: 95px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background: #f6f6f6;
  margin-bottom: 5px;
  cursor: pointer;
}
</style>
