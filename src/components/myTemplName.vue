<template>
  <div style="display: inline-block" v-if="isShowSave">
    <Input
      v-if="isShowSaveButton"
      class="c-my-template"
      v-model="fileName"
      placeholder="请输入文件名称"
      style="width: 190px"
      size="small"
      :loading="loading"
    />
    <Button v-if="isShowSaveButton" type="text" @click="saveTempl" :loading="loading">
      <Icon type="ios-cloud-done" :color="loading ? '#ff9900' : '#19be6b'" />
    </Button>
    <Button v-if="isTemplateId && isShowSaveButton" type="text" title="另存为" @click="handleSave">
      <Icon type="md-document" color="#19be6b" />
    </Button>
    <Divider type="vertical" />
    <!-- 弹层 -->
    <Modal v-model="otherSaveVisible" title="文件另存为" :mask-closable="false">
      <OtherSave ref="otherSaveRef" />
      <template #footer>
        <Button @click="() => otherSaveVisible = false">取消</Button>
        <Button type="primary" @click="handleOk" :loading="isLoad">确定</Button>
      </template>
    </Modal>
  </div>
</template>

<script name="ImportJson" setup>
import { debounce } from 'lodash-es'
import { Message, Modal } from 'view-ui-plus'
import useMaterial from '@/hooks/useMaterial'
import useTemplateList from '@/components/myMaterial/hooks/useTemplateList'
import { useRoute, useRouter } from 'vue-router'
import Loading from '@/components/load/index.js'
import useSelect from '@/hooks/select'
import { $bus, EVENT_NAME } from '@/utils/mitt'
import OtherSave from '@/components/save/index.vue'

const router = useRouter()
const { updateTemplateInfo } = useMaterial()
const { getTemplateInfo } = useTemplateList()

const { canvasEditor } = useSelect()

const fileName = ref('')
const route = useRoute()
const loading = ref(false)
const isLoad = ref(false)

const handleFileName = (record) => {
  if (record.type !== 'file') return
  fileName.value = record.name
}

const isShowSave = computed(() => {
  return route.query?.id || route.query?.clampId
})

const isTemplateId = computed(() => {
  return route.query?.id
})

const isShowSaveButton = ref(true)
const handleShowSaveButton = (value) => {
  isShowSaveButton.value = value
}

onMounted(async () => {
  if (route?.query?.id) {
    try {
      const result = await getTemplateInfo(route?.query?.id)
      fileName.value = result.name
      canvasEditor.loadJSON(result.json, () => {
        $bus.emit(EVENT_NAME.JSON_LOADED)
      })
      $bus.emit(EVENT_NAME.CURRENT_ID, { id: route?.query?.id, name: fileName.value })
      $bus.emit(EVENT_NAME.CURRENT_JSON, { json: result.json })
    } catch (e) {
      window.location.href = '/'
    }
  }
  $bus.on(EVENT_NAME.IS_HIDE_SHOW_BUTTON, handleShowSaveButton)
  $bus.on(EVENT_NAME.FILE_NAME, setFileName)
  $bus.on(EVENT_NAME.CURRENT_ID, handleFileName)
});
onBeforeMount(() => {
  $bus.off(EVENT_NAME.IS_HIDE_SHOW_BUTTON, handleShowSaveButton)
  $bus.off(EVENT_NAME.FILE_NAME, setFileName)
  $bus.off(EVENT_NAME.CURRENT_ID, handleFileName)
})

const saveTempl = () => {
  if (fileName.value === '') {
    Message.warning('文件名称不能为空')
    fileName.value = '默认文件名称'
    return
  }
  Loading.show('正在保存')
  if (route.query.clampId) {
    $bus.emit(EVENT_NAME.SAVE_CLAMP_TEMPLATE, fileName.value)
    return
  }
  updateTemplateInfo(route.query.id, fileName.value)
    .then()
    .finally(() => {
      canvasEditor.emitRefreshFileList()
      Loading.hide()
    })
}

let generateStatus = false;
const changeFileName = debounce(() => {
  if (route.query.id && !generateStatus) {
    saveTempl();
  }
}, 3000);

canvasEditor.on('generateEvent', (status) => {
  generateStatus = status;
});

// 文件另存为
const otherSaveVisible = ref(false)
const handleSave = () => {
  otherSaveVisible.value = true
}
const otherSaveRef = ref(null)
const handleOk = async () => {
  isLoad.value = true
  const result = await otherSaveRef.value.handleOk()
  isLoad.value = false
  if (!result) {
    otherSaveVisible.value = true
  } else {
    otherSaveVisible.value = false
    canvasEditor.emitRefreshFileList()
  }
}

const setFileName = (value) => {
  fileName.value = value
}
</script>
<style scoped lang="less">
h3 {
  margin-bottom: 10px;
}
.divider {
  margin-top: 0;
}
</style>
