<template>
  <Button type="text" @click="toggleTheme" class="theme-toggle">
    <Icon :type="isDark ? 'ios-sunny' : 'ios-moon'" size="16" />
    {{ isDark ? $t('theme.light') : $t('theme.dark') }}
  </Button>
</template>

<script setup name="ThemeToggle">
import { useTheme } from '@/hooks/useTheme'

const { toggleTheme, isDark } = useTheme()
</script>

<style scoped lang="less">
.theme-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-color);

  &:hover {
    background-color: var(--button-hover-bg);
  }

  :deep(.ivu-icon) {
    margin-right: 4px;
  }
}
</style>
