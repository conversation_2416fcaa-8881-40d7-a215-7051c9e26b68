<!-- file: 多图片替换 -->
<template>
  <div v-if="false" class="attr-item-box">
    <Divider plain orientation="left">
      <h4>夹具</h4>
    </Divider>
    <div class="bg-item">
      <Button @click="handleReplaceImgs" type="text" long>{{ $t('repleaceImg') }}</Button>
    </div>
    <Row v-if="isShowPosition" :gutter="10" style="margin-top: 10px">
      <Col flex="1">
        <InputNumber v-model="position.x" @on-blur="handleBlur" style="width: 100%" :defaultValue="0" :append="$t('attributes.offset_x')" />
      </Col>
      <Col flex="1">
        <InputNumber v-model="position.y" @on-blur="handleBlur" style="width: 100%" :defaultValue="0" :append="$t('attributes.offset_y')" />
      </Col>
    </Row>
  </div>
</template>

<script setup name="GroupImg">
import useSelect from '@/hooks/select';

import { Utils } from '@kuaitu/core';
import InputNumber from "@/components/inputNumber";
const { getImgStr, selectFiles, insertImgFile } = Utils;

const { mixinState, canvasEditor } = useSelect();

const handleReplaceImgs = async () => {
  const activeObjects = canvasEditor.canvas.getActiveObjects();
  if (activeObjects) {
    const [file] = await selectFiles({ accept: 'image/*', multiple: false });
    const fileStr = await getImgStr(file);
    const imgEl = await insertImgFile(fileStr);
    activeObjects.forEach(activeObject => {
      const width = activeObject.get('width');
      const height = activeObject.get('height');
      const scaleX = activeObject.get('scaleX');
      const scaleY = activeObject.get('scaleY');
      activeObject.setSrc(imgEl.src, () => {
        activeObject.set('scaleX', (width * scaleX) / imgEl.width);
        activeObject.set('scaleY', (height * scaleY) / imgEl.height);
        canvasEditor.canvas.renderAll();
      });
    })
    imgEl.remove();
  }
};

const isMultiple = computed(() => mixinState.mSelectMode === 'multiple')

const type = ref('image')
const isShowPosition = ref(false)
const init = () => {
  const activeObjects = canvasEditor.canvas.getActiveObjects()
  const imgObjectLength = canvasEditor.canvas.getObjects().filter(item => item.type === 'image').length
  const activeImgObjectLength = activeObjects.filter(item => item.type === 'image').length
  activeObjects.forEach(item => {
    type.value = item.type
  })

  isShowPosition.value = imgObjectLength === activeImgObjectLength
  if (isShowPosition.value) {
    position.x = activeObjects[0].group.left
    position.y = activeObjects[0].group.top
  }
}

const position = reactive({
  x: 0,
  y: 0
})
const handleBlur = () => {
  const activeObjects = canvasEditor.canvas.getActiveObjects()
  activeObjects[0].group.set({
    left: position.x,
    top: position.y
  })
  canvasEditor.canvas.renderAll()
}

onMounted(() => {
  canvasEditor.on('selectMultiple', init)
});

onBeforeUnmount(() => {
  canvasEditor.off('selectMultiple', init);
});
</script>
