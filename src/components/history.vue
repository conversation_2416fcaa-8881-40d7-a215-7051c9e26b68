<template>
  <div style="display: inline-block">
    <Tooltip :content="$t('history.revocation') + `(${canUndo})`">
      <Button @click="undo" type="text" size="small" :disabled="!canUndo">
        <Icon type="ios-undo" size="20" />
      </Button>
    </Tooltip>
    <Tooltip :content="$t('history.redo') + `(${canRedo})`">
      <Button @click="redo" type="text" size="small" :disabled="!canRedo">
        <Icon type="ios-redo" size="20" />
      </Button>
    </Tooltip>
  </div>
</template>

<script setup lang="ts">
import useSelect from '@/hooks/select';
const { canvasEditor } = useSelect() as { canvasEditor: any };
const canUndo = ref(0);
const canRedo = ref(0);
const undo = () => {
  canvasEditor.undo();
};
const redo = () => {
  canvasEditor.redo();
};

onMounted(() => {
  canvasEditor.on('historyUpdate', (canUndoParam: number, canRedoParam: number) => {
    canUndo.value = canUndoParam;
    canRedo.value = canRedoParam;
  });
});
</script>

<style scoped lang="less">
span.active {
  svg.icon {
    fill: #2d8cf0;
  }
}

.time {
  color: #c1c1c1;
}
</style>

<script lang="ts">
export default {
  name: 'ToolBar',
};
</script>
