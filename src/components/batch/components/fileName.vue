<template>
  <div>
    指定文件保存的格式
    <RadioGroup
      v-model="saveFileType"
      type="button"
      size="small"
      @on-change="saveFileTypeChange"
      button-style="solid"
      style="margin-bottom: 10px"
    >
      <Radio label="custom" :disabled="props.columns.length === 0">自定义</Radio>
      <Radio label="data" :disabled="props.columns.length === 0">按数据</Radio>
      <Radio label="templ" :disabled="props.columns.length === 0">按模板</Radio>
    </RadioGroup>

    <Select
      v-model="fileNameKey"
      :disabled="props.columns.length === 0"
      v-if="saveFileType === 'data'"
      transfer
      size="small"
      @on-change="emit('change', getFileNameFormat())"
    >
      <Option
        v-for="item in props.columns.filter((info) => info.type === 'text')"
        :value="item.format"
        :key="item.format"
      >
        {{ item.format }}.png
      </Option>
    </Select>
    <Select
      v-model="fileNameKey"
      :disabled="props.columns.length === 0"
      v-if="saveFileType === 'templ'"
      transfer
      size="small"
      @on-change="emit('change', getFileNameFormat())"
    >
      <Option
        v-for="item in columns.filter((info) => info.type === 'text')"
        :value="item.format"
        :key="item.format"
      >
        ${index}-${模板名称}-{{ item.format }}.png
      </Option>
    </Select>
    <Input
      v-if="saveFileType === 'custom'"
      v-model="fileNameKey"
      size="small"
      :disabled="props.columns.length === 0"
      @on-change="emit('change', getFileNameFormat())"
    >
      <template #append>
        <span>.png</span>
      </template>
    </Input>
  </div>
</template>

<script setup name="Batch">
const saveFileType = ref('custom');
const fileNameKey = ref('');

const props = defineProps({
  columns: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['change']);

const saveFileTypeChange = (value) => {
  if (value === 'custom') {
    fileNameKey.value = '${index}';
  } else {
    const [first] = props.columns;
    fileNameKey.value = first?.format;
  }

  emit('change', getFileNameFormat());
};

const setDefault = () => {
  saveFileType.value = 'custom';
  saveFileTypeChange('custom');
};

const getFileNameFormat = () => {
  if (fileNameKey.value === '') {
    return '';
  }
  if (saveFileType.value === 'custom' || saveFileType.value === 'data') {
    return fileNameKey.value;
  } else if (saveFileType.value === 'templ') {
    return '${index}-${模板名称}-' + fileNameKey.value;
  }
};

defineExpose({
  setDefault,
});
</script>

<style scoped lang="less">
</style>
