<template>
  <div>
    <Button @click="uploadFile" icon="ios-cloud-upload-outline" size="small" class="file-btn">
      选择数据文件
    </Button>
    <Table
      class="data-table"
      v-if="columns.length"
      size="small"
      :columns="[
        {
          key: 'title',
          slot: 'title',
          title: '数据',
          width: 120,
          fixed: 'left',
        },
        {
          key: 'desc',
          slot: 'desc',
          title: '一行显示',
          width: 85
        },
        {
          key: 'type',
          slot: 'type',
          width: 70,
          title: '类型',
        },
        {
          key: 'format',
          width: 120,
          slot: 'format',
          title: '格式/路径',
        },
      ]"
      :data="columns"
    >
      <template #title="{ row }">
        <Tooltip content="添加到画布" placement="right" transfer>
          <Button
            @click="addTextBox(row)"
            type="primary"
            size="small"
            shape="circle"
            icon="md-add"
          ></Button>
        </Tooltip>
        <Tooltip :content="'${' + row.title + '}'" placement="right" transfer>
          <span class="copyText" @click="Copy({ text: '${' + row.title + '}' })">
            {{ row.title }}
          </span>
        </Tooltip>
      </template>

      <template #desc="{ index }">
        <Switch v-model="columns[index].isWrap" />
      </template>

      <template #type="{ index }">
        <Switch v-model="columns[index].type" true-value="img" false-value="text">
          <template #open>
            <span>图</span>
          </template>
          <template #close>
            <span>字</span>
          </template>
        </Switch>
      </template>

      <template #format="{ row, index }">
        <Input v-if="row.type === 'text'" v-model="columns[index].format" size="small" />
        <Button
          v-if="row.type === 'img'"
          @click="selectImgFiles(columns[index])"
          size="small"
          icon="ios-cloud-upload-outline"
        >
          选择图片
        </Button>
      </template>
    </Table>
  </div>
</template>

<script setup name="Batch">
import { useFileDialog } from '@vueuse/core';
import useSelect from '@/hooks/select';
import { v4 as uuid } from 'uuid';
import { Copy } from 'view-ui-plus';
const { canvasEditor, fabric } = useSelect();
import { Utils } from '@kuaitu/core';
const { getImgStr, selectFiles } = Utils;

const emit = defineEmits(['change']);
let tableData = ref([]);
const columns = ref([]);

const uploadFile = () => {
  const { onChange, open } = useFileDialog({
    accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });
  onChange(async (files) => {
    const { jsonData, columns: cols } = await canvasEditor.getExcelData(files[0]);
    tableData.value = jsonData;
    columns.value = cols.map(item => {
      return {
        isWrap: false,
        ...item,
      }
    })

    emit('change', tableData.value, columns.value);
  });
  open();
};

const selectImgFiles = (item) => {
  const { onChange, open } = useFileDialog({
    accept: 'image/jpeg,image/png',
    multiple: true,
  });
  onChange(async (files) => {
    item.imgMap = {};
    for (var i = 0; i < files.length; i++) {
      const base64Code = await canvasEditor.Utils.blobToBase64(files[i]);
      item.imgMap[files[i].name] = base64Code;
    }
    emit('change', tableData.value, columns.value);
  });
  open();
};

const addTextBox = (row) => {
  if (row.type === 'text') {
    const text = new fabric.Textbox('{' + row.title + '}', {
      width: 600,
      fontSize: 80,
      fill: '#000000FF',
      textAlign: 'center',
      linkData: ['text', row.title],
      id: uuid(),
    });
    text.setControlVisible('mt', false)
    text.setControlVisible('mb', false)
    canvasEditor.canvas.add(text);
    canvasEditor.canvas.setActiveObject(text);
    canvasEditor.position('center');
  } else {
    insertImg(row);
  }
};

const insertImg = (row) => {
  selectFiles({ accept: 'image/*', multiple: true }).then((fileList) => {
    Array.from(fileList).forEach((item) => {
      getImgStr(item).then((file) => {
        insertImgFile(file, row);
      });
    });
  });
};

function insertImgFile(file, row) {
  if (!file) throw new Error('file is undefined');
  const imgEl = document.createElement('img');
  imgEl.src = file;
  document.body.appendChild(imgEl);
  imgEl.onload = () => {
    const imgInstance = new fabric.Image(imgEl, {
      id: uuid(),
      name: row.title,
      linkData: ['src', row.title],
    });

    canvasEditor.dragAddItem(imgInstance);
    canvasEditor.position('center');
    canvasEditor.canvas.renderAll();
    imgEl.remove();
  };
}
</script>

<style scoped lang="less">
.data-table {
  margin-top: 10px;
}

/deep/.ivu-divider-horizontal {
  margin: 12px 0;
}
.copyText {
  cursor: pointer;
  margin-left: 10px;
}
</style>
