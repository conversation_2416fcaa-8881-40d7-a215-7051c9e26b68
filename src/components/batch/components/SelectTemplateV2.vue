<!-- file 模板选择器 -->
<template>
  <TreeSelect v-model="selectValue" transfer show-checkbox :load-data="loadData" :data="data" @on-change="handleChange" />
</template>

<script setup name="SelectTemplateV2">
import { getTemplateList } from '@/api/v2'

const emits = defineEmits(['change'])

const selectValue = ref(undefined)
const data = ref([{
  title: '全部',
  value: '',
  loading: false,
  selected: false,
  checked: false,
  children: []
}])

const loadData = async (item, callback) => {
  const result = await getTemplateList({
    parentId: item.value,
    pageSize: 100,
    pageNum: 1,
    keyword: ''
  })
  callback(result.data.map(item => {
    const record = {
      title: item.name,
      value: item.id,
      selected: false,
      checked: false
    }
    if (item.type === 'fileType') {
      record.children = []
      record.loading = false
    }
    return record
  }))
}

const handleChange = (id) => {
  emits('change', id)
}
</script>
