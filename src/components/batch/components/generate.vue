<template>
  <div>
    <Space wrap>
      <Button type="primary" @click="start(false)">预览</Button>
      <Button type="primary" @click="start(true)">生成</Button>
      <span class="c-generate-top-text" @click="handleCheckPicture">已生成 {{ previewImageList.length }} 张图</span>
    </Space>

    <Modal
      v-model="modal"
      title="批量生成中，请耐心等待"
      :mask-closable="false"
      closable
      :footer-hide="true"
      :width="800"
    >
      <Progress
        :percent="parseInt((doneIndex / allGenerateLength) * 100)"
        :stroke-width="20"
        text-inside
      />
      <div style="text-align: center">进度：{{ doneIndex }}/{{ allGenerateLength }}</div>

      <Space class="c-generate-model" wrap>
        <template v-for="(item, index) in previewImageList" :key="item.name">
          <div class="c-generate-image">
            <Image
              :src="item.src"
              fit="contain"
              width="120px"
              height="156px"
              preview
              :preview-list="previewImageList.map((info) => info.src)"
              :initial-index="index"
            />
            <div class="c-generate-text" @click="handleReplace(item)">替换</div>
          </div>
        </template>
      </Space>
      <div v-if="doneIndex === allGenerateLength">
        <Divider size="small" plain orientation="left"></Divider>
        <div class="btn-tip">
          <div v-if="!previewImageList.length" style="margin-right: 10px">
            文件【设计批量生成.zip】已保存到您的浏览器下载目录
          </div>
          <Button v-if="previewImageList.length" type="error" style="margin-right: 10px" @click="handleClampTemplate">夹具模板</Button>
          <Button type="primary" @click="handleClose">完成</Button>
        </div>
      </div>
    </Modal>

    <!-- 夹具 -->
    <Modal v-model="visible" width="800px" title="现有夹具" footer-hide @on-cancel="handleClearPath">
      <div class="c-clamp-template__desc">
        <Breadcrumb>
          <BreadcrumbItem v-for="item in paths" :key="item.id" @click="handlePathClick(item)">{{ item.name }}</BreadcrumbItem>
        </Breadcrumb>
      </div>
      <div class="c-clamp-scroll__null" v-if="!clampList.length && !isLoading">--暂无数据--</div>
      <template v-if="isLoading">
        <div class="c-clamp-template__loading first-loading-wrp">
          <div class="loading-wrp">
            <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
          </div>
        </div>
      </template>
      <Scroll v-else :on-reach-bottom="getClampList" :on-reach-edg="handleResetSearch" height="400">
        <FileItem
          v-for="item in clampList"
          :key="item.id"
          :record="item"
          is-clamp
          @folder="handleFolder"
          @template="handleSelectClamp"
        />
      </Scroll>
    </Modal>

    <!-- 夹具批量 -->
    <Modal
      v-model="clampVisible"
      title="批量生成夹具中，请耐心等待"
      :mask-closable="false"
      closable
      :footer-hide="true"
      :width="800"
    >
      <Progress
        :percent="parseInt((clampTemplateList.length / allClampDoneLength) * 100)"
        :stroke-width="20"
        text-inside
      />
      <div style="text-align: center">进度：{{ clampTemplateList.length }}/{{ allClampDoneLength }}</div>

      <Space class="c-generate-model" wrap>
        <template v-for="(item, index) in clampTemplateList" :key="item.name">
          <Image
            :src="item.src"
            fit="contain"
            width="120px"
            height="156px"
            preview
            :preview-list="clampTemplateList.map((info) => info.src)"
            :initial-index="index"
          />
        </template>
      </Space>
      <div v-if="clampTemplateList.length === allClampDoneLength">
        <Divider size="small" plain orientation="left"></Divider>
        <div class="btn-tip">
          <Button type="error" style="margin-right: 10px" @click="handleDownFile">下载图片</Button>
          <Button type="primary" @click="clampVisible = false">完成</Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup name="generate">
import JSZip from 'jszip'
import fabricV2 from 'fabric'
import { v4 as uuid } from 'uuid'
import { Utils } from '@kuaitu/core'
import { useRoute } from 'vue-router'
import { Button, Message, Modal, Spin } from 'view-ui-plus'
import { cloneDeep } from 'lodash-es'
import useSelect from '@/hooks/select'
import { getFixtureDetail, getTemplateDetail } from '@/api/v2'
import useMaterial from '@/hooks/useMaterial'
import FileItem from '@/components/clamp/FileItem.vue'
import useClamp from '@/hooks/useClamp'

const { canvasEditor, fabric } = useSelect()
const route = useRoute()

const { getTemplInfo } = useMaterial()
const { insertImgFile, selectFiles, getImgStr } = Utils
const emit = defineEmits(['success'])

const props = defineProps({
  columns: {
    type: Array,
    default: () => [],
  },
  tableData: {
    type: Array,
    default: () => [],
  },
  contentTemplId: {
    type: [String, Number],
    default: '',
  },
  selectTempls: {
    type: Array,
    default: () => [],
  },
  fileNameFormat: {
    type: String,
    default: '',
  },
});

const doneIndex = ref(0);

const allGenerateLength = computed(() => {
  return props.tableData.length * props.selectTempls.length
})

const modal = ref(false);

const start = async (down) => {
  if (props.columns.length === 0) {
    Message.error('请选择数据文件');
    return;
  }
  if (props.selectTempls.length === 0 || props.fileNameFormat === '') {
    Message.error('请选择模板、文件名格式');
    return;
  }
  if (checkImgColumns()) {
    Message.error('请为图类型选择文件');
    return;
  }

  const typeText = down ? '生成' : '预览';
  Modal.confirm({
    title: '确认开始' + typeText + '？',
    content: `<p>将按照数据表格${typeText}${allGenerateLength.value}张图片</p>`,
    onOk: () => startGenerate(down),
  });
};
const previewImageList = ref([]);
const batchPreviewInfo = []
const startGenerate = async (idDown = false) => {
  try {
    const wrapKeys = props.columns.filter(item => item.isWrap).map(item => item.key)
    canvasEditor.emitGenerate(true)
    previewImageList.value = []

    const templMap = await getTemplsInfoData();
    Spin.hide();
    if (!templMap[Object.keys(templMap)[0]].json) {
      return Message.error('该模板为文件夹，无法批量生成')
    }
    const json = JSON.parse(templMap[Object.keys(templMap)[0]].json)
    json.objects.forEach(item => {
      if (item.type === 'textbox') {
        const match = item.text.match(/\{([^{}]*)\}/)
        if (match && match[1]) {
          item.linkData = ['text', match[1]]
        }
      }
    })
    templMap[Object.keys(templMap)[0]].json = JSON.stringify(json)
    doneIndex.value = 0;

    const imgList = [];

    let i = 0;
    modal.value = true;
    for (const templId of Object.keys(templMap)) {
      for (const item of props.tableData) {
        const templInfo = templMap[templId];
        const itemData = {
          ...item,
          index: i + 1,
          模板名称: templInfo.name,
        };
        // batchPreviewInfo.push({
        //   templInfo,
        //   itemData,
        //   wrapKeys
        // })
        const fileData = await generatePng(templInfo, itemData, wrapKeys)
        imgList.push(fileData)
        doneIndex.value = doneIndex.value + 1
        i++
      }
    }

    processingFiles(idDown, imgList)
    await getTempData()
    canvasEditor.emitGenerate(false);
  } catch (error) {
    console.log(error)
    Message.error('生成失败，请选择正确的模板')
  }
};

// 解决log task
const imgList = []
const handleLogTask = async () => {
  if (!batchPreviewInfo.length) return
  const { itemData, templInfo, wrapKeys } = batchPreviewInfo.shift()
  const fileData = await generatePng(templInfo, itemData, wrapKeys)
  imgList.push(fileData)
  if (!batchPreviewInfo.length) {
    await processingFiles(false, imgList)
    await getTempData()
  }
}

const processingFiles = async (idDown, imgList) => {
  if (idDown) {
    downZip(imgList);
  } else {
    previewImageList.value = [...imgList];
  }
};

const downZip = async (imgList) => {
  const zip = new JSZip();
  for (const item of imgList) {
    const [base64Blob] = canvasEditor.Utils.base64ToBlob(item.src);
    zip.file(item.fileName, base64Blob);
  }

  zip.generateAsync({ type: 'blob' }).then(function (content) {
    const anchorEl = document.createElement('a');
    anchorEl.href = URL.createObjectURL(content);
    anchorEl.download = '设计批量生成.zip';
    document.body.appendChild(anchorEl); // required for firefox
    anchorEl.click();
    anchorEl.remove();
    emit('success');
  });
};

const getTemplsInfoData = async () => {
  const templMap = {}
  for (const item of props.selectTempls) {
    const res = await getTemplateDetail(item)
    templMap[item] = {
      name: res.data.name,
      json: res.data.json,
    }
  }
  return templMap
};

const generatePng = async (templInfo, itemData, wrapKeys) => {
  const json = JSON.parse(templInfo.json)
  let { data, fileName } = canvasEditor.mreageData({
    item: itemData,
    columnFormats: props.columns,
    templ: json,
    fileNameFormat: props.fileNameFormat + '.png',
  });
  await loadJson(data)
  data = null
  generateText(json, itemData, wrapKeys)
  canvasEditor.canvas.requestRenderAll()
  await sleep()
  const base64 = await canvasEditor.preview()
  return {
    fileName,
    src: base64,
    data,
  }
}

const generateText = (json, itemData, wrapKeys) => {
  if (!wrapKeys.length) return
  for (const key of wrapKeys) {
    const record = json.objects.find(item => {
      return item.linkData && item.linkData[1] === key
    })
    if (record && record.type === 'image') {
      continue
    }
    const objects = canvasEditor.canvas.getObjects()
    const boxCanvas = objects.find(item => {
      return item.type === 'textbox' && item.text.toString() === itemData[record.linkData[1]].toString()
    })
    if (boxCanvas.height > record.height) {
      const width = record.width * record.scaleX
      const height = record.height * record.scaleY
      let strokeRecord = {}
      if (boxCanvas.strokeUniform) {
        strokeRecord = {
          strokeUniform: true,
          strokeWidth: boxCanvas.strokeWidth,
          stroke: boxCanvas.stroke
        }
      }
      const text = new fabric.IText(itemData[record.linkData[1]].trim(), {
        fontSize: record.fontSize,
        fontFamily: record.fontFamily,
        fill: record.fill,
        left: record.left,
        top: record.top,
        id: uuid(),
        ...strokeRecord
      })
      canvasEditor.canvas.add(text)
      canvasEditor.canvas.setActiveObject(text)
      const activeText = canvasEditor.canvas.getActiveObject()
      activeText.set({
        scaleX: width / activeText.width,
        scaleY: height / activeText.height
      })
      if (record.fill && record.fill.repeat === 'repeat') {
        const pattern = new fabricV2.fabric.Pattern({
          source: record.fill.source,
          repeat: 'repeat',
          offsetX: record.fill.offsetX
        })
        activeText.set('fill', pattern)
      }
      canvasEditor.canvas.remove(boxCanvas)
    }
  }
}

function sleep(time = 10) {
  const rightButton = document.querySelector('.right-btn')
  return new Promise((resolve) => {
    setTimeout(() => {
      rightButton.click()
      resolve();
    }, time);
  });
}

function loadJson(json) {
  return new Promise((resolve) => {
    canvasEditor.loadJSON(json, resolve);
  });
}
const checkImgColumns = () => {
  const hasImg = props.columns.some((item) => item.type === 'img');
  if (hasImg) {
    const imgCol = props.columns.filter((item) => item.type === 'img');
    const check = imgCol.every((item) => Object.keys(item.imgMap).length);
    if (!check) {
      return true;
    }
  }
  return false;
};

const getTempData = async () => {
  if (route.query.id) {
    const data = await getTemplInfo(route.query.id);
    canvasEditor.loadJSON(JSON.stringify(data.data.json));
  } else {
    canvasEditor.clear();
  }
}


/**
 * 夹具批量生成
 */
const { paths, isLoading, clampList, handleResetSearch, getClampList, handleClearPath } = useClamp()
const visible = ref(false)
const handleClampTemplate = () => {
  visible.value = true
  handleResetSearch()
}
const handlePathClick = (record) => {
  const index = paths.value.findIndex(item => item.id === record.id)
  paths.value = paths.value.slice(0, index + 1)
  handleResetSearch(record.id)
}
const handleFolder = (record) => {
  paths.value.push({
    id: record.id,
    name: record.name
  })
  handleResetSearch(record.id)
}
const handleSelectClamp = async (record) => {
  visible.value = false
  handleClearPath()
  const result = await getFixtureDetail(record.id)
  const json = JSON.parse(result.data.jsonStr)
  if (record.isChecked) {
    json.objects.forEach(item => {
      if (item.isClamp) {
        item.filters = item.filters || []
        item.filters.push({
          type: 'RemoveColor',
          color: record.color,
          distance: record.distance
        })
      }
    })
  }
  multiSort(json.objects, (a, b) => a.top - b.top, (a, b) => a.left - b.left)
  await generateClampTemplate(json)
}

const clampTemplateList = ref([])
const clonePreviewImageList = ref([])
const allClampDoneLength = ref(0)
const clampVisible = ref(false)
const generateClampTemplate = async (json) => {
  clonePreviewImageList.value = [...previewImageList.value]
  const imgLength = clonePreviewImageList.value.length
  const clampImgLength = json.objects.filter(item => item.isClamp).length
  allClampDoneLength.value = Math.ceil(imgLength / clampImgLength)
  clampTemplateList.value = []
  clampVisible.value = true

  for (let i = 0; i < allClampDoneLength.value; i++) {
    await getGenerateClamp(json, (i + 1).toString().padStart(3, '0'))
  }
  await getTempData()
}
async function getGenerateClamp(json, prefix) {
  const olbCanvasObjects = []
  const newCanvasObjects = []

  for (const item of json.objects) {
    if (!clonePreviewImageList.value.length && item.isClamp) {
      continue
    }
    olbCanvasObjects.push(cloneDeep(item))
    if (clonePreviewImageList.value.length && item.isClamp) {
      item.src = clonePreviewImageList.value.shift().src
      newCanvasObjects.push(item)
    }
  }

  json.objects = olbCanvasObjects

  await loadJson(json)
  const filterCanvas = newCanvasObjects.filter(item => item.isClamp) || []
  const containers = canvasEditor.canvas.getObjects().filter(item => item.isClamp)
  for (const item of containers) {
    await handleReplaceImg(item, filterCanvas.find(record => record.id === item.id).src)
  }
  canvasEditor.canvas.renderAll()
  await sleep()
  const base64 = await canvasEditor.preview()
  clampTemplateList.value.push({
    src: base64,
    name: `${prefix}.png`
  })
  await sleep(1000)
}

const handleReplaceImg = async (activeCanvas, imgSrc) => {
  const imgEl = await insertImgFile(imgSrc)
  const width = activeCanvas.get('width')
  const height = activeCanvas.get('height')
  const scaleX = activeCanvas.get('scaleX')
  const scaleY = activeCanvas.get('scaleY')
  activeCanvas.setSrc(imgSrc, () => {
    activeCanvas.set('scaleX', (width * scaleX) / imgEl.width)
    activeCanvas.set('scaleY', (height * scaleY) / imgEl.height)
  })
}

// 多条件排序
const multiSort = (array, ...compairers) => {
  return array.sort((a, b) => {
    for (const c of compairers) {
      const r = c(a, b)
      if (r !== 0) {
        return r
      }
    }
  })
}

const handleDownFile = () => {
  const imgList = []
  for (const clamp of clampTemplateList.value) {
    imgList.push({
      fileName: clamp.name,
      src: canvasEditor.changeDpiDataUrl(clamp.src, 300)
    })
  }
  downZip(imgList)
}

const handleCheckPicture = () => {
  if (!previewImageList.value.length) return
  modal.value = true
}

const handleClose = () => {
  modal.value = false
  doneIndex.value = 0
  clampTemplateList.value = []
  clonePreviewImageList.value = []
  allClampDoneLength.value = 0
  clampVisible.value = false
}

const handleReplace = async (record) => {
  const result = await selectFiles({
    accept: 'image/*',
  })
  record.src = await getImgStr(result[0])
}

onUnmounted(() => {
  previewImageList.value = []
})
</script>

<style scoped lang="less">
.btn-tip {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>

<style lang="less">
.c-generate-model {
  max-height: 500px;
  overflow-y: auto;
}
.c-generate-top-text {
  cursor: pointer;
  color: #e0502c;
  text-align: center;
}
.c-generate-image:hover {
  position: relative;
  .c-generate-text {
    display: block;
    z-index: 999;
  }
}
.c-generate-text {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  display: none;
  cursor: pointer;
  color: #e0502c;
  text-align: center;
}
</style>
