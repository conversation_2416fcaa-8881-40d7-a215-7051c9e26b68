<template>
  <div>
    <div class="tree-box">
      <TreeSelect
        multiple
        show-checkbox
        transfer
        size="small"
        v-model="selectTempls"
        :data="fileTree"
        @on-change="getSelectTemplsOption"
      />
    </div>
    <div class="tree-box bottom" v-if="selectTempls.length > 1">
      <Select
        v-model="contentTemplId"
        transfer
        size="small"
        clearable
        @on-change="change"
        placeholder="内容模板设置 非必填"
      >
        <Option v-for="item in selectTemplsOption" :value="item.id" :key="item.id + '--select'">
          {{ item.name }}
        </Option>
      </Select>
      <Tooltip transfer :max-width="650">
        <Icon type="ios-help-circle" :size="16" />
        <template #content>
          <img :src="tipImg" width="600" alt="内容模板批量生成 其他模板只生成1次" />
        </template>
      </Tooltip>
    </div>
  </div>
</template>

<script setup name="Batch">
import { getUerFileTree } from '@/api/user';
import tipImg from '@/assets/conent.png';
import useSelect from '@/hooks/select';
const emit = defineEmits(['change']);

const { canvasEditor } = useSelect();

const selectTempls = ref([]);

const fileTree = ref([]);
const loading = ref(false);
const isLogin = ref(true);
const getUerFileTreeHandle = async () => {
  try {
    loading.value = true;
    const res = await getUerFileTree();
    fileTree.value = checkDisabledTree([res.data.data]);
  } catch (error) {
    console.log(error);
    isLogin.value = false;
  }
  loading.value = false;
};

const selectTemplsOption = ref([]);
const contentTemplId = ref('');
const getSelectTemplsOption = async () => {
  selectTemplsOption.value = [];
  contentTemplId.value = '';
  for (const item of selectTempls.value) {
    const res = getItemInfoByTree(item, fileTree.value);
    selectTemplsOption.value.push({
      name: res.title,
      id: res.value,
    });
  }
  change();
};

const getItemInfoByTree = (id, arr) => {
  for (const item of arr) {
    if (item.value === id) {
      return item;
    }
    if (item.children) {
      const info = getItemInfoByTree(id, item.children);
      if (info) {
        return info;
      }
    }
  }
};

const checkDisabledTree = (arr) => {
  arr.forEach((item) => {
    if (item.type === 'fileType' || !item.type) {
      item.disabled = true;
    }
    if (item.children) {
      item.children = checkDisabledTree(item.children);
    }
  });
  return arr;
};

const change = () => {
  emit('change', selectTempls.value, contentTemplId.value);
};
onMounted(() => {
  getUerFileTreeHandle();
});

canvasEditor.on('refreshFileList', getUerFileTreeHandle);
</script>

<style scoped lang="less">
.data-table {
  margin-top: 10px;
}

/deep/.ivu-divider-horizontal {
  margin: 12px 0;
}

.tree-box {
  display: flex;
  align-items: center;
  button {
    margin-left: 5px;
  }
  /deep/.ivu-tooltip {
    margin-left: 11px;
  }

  &.bottom {
    padding-top: 10px;
  }
}
</style>
