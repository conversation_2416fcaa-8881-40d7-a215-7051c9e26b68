<template>
  <div class="box c-batch-comp">
    <Divider plain orientation="left">批量生成</Divider>
      <Steps :current="current" direction="vertical" size="small">
        <Step title="选择数据">
          <template v-slot:content>
            <columnsC @change="getTableData"></columnsC>
          </template>
        </Step>
        <Step title="字段关联">
          <template v-slot:content>找到模板中需要关联数据的字段，保存模板。</template>
        </Step>
        <Step title="选择模板">
          <template v-slot:content>
            <SelectTemplateV2 @change="handleSelectTemplate" />
          </template>
        </Step>
        <Step title="文件名称">
          <template v-slot:content>
            <fileName ref="fileNameRef" :columns="columns" @change="getFileNameFormat"></fileName>
          </template>
        </Step>
        <Step title="开始生成">
          <template v-slot:content>
            <generate
              :tableData="tableData"
              :columns="columns"
              :selectTempls="selectTempls"
              :fileNameFormat="fileNameFormat"
              ref="generateRef"
            ></generate>
          </template>
        </Step>
      </Steps>
  </div>
</template>

<script setup name="Batch">
import columnsC from './components/columns.vue';
import SelectTemplateV2 from '@/components/batch/components/SelectTemplateV2.vue'
import fileName from './components/fileName.vue';
import generate from './components/generate.vue';

let tableData = ref([]);
const columns = ref([]);
const getTableData = async (tab, col) => {
  tableData.value = tab;
  columns.value = col;
  console.log(columns.value)
};

const selectTempls = ref([]);

const fileNameRef = ref(null);
const fileNameFormat = ref('');
const getFileNameFormat = (format) => {
  fileNameFormat.value = format
}

const handleSelectTemplate = (id) => {
  selectTempls.value = [id]
}
</script>

<style scoped lang="less">
/deep/.ivu-divider-horizontal {
  margin: 12px 0;
}

.tip-box {
  padding-top: 10px;
  img {
    display: block;
    width: 100%;
  }
}
</style>
