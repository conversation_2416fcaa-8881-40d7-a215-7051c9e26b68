<template>
  <div class="box">
    <Divider plain orientation="left">批量生成</Divider>
    <div v-show="isLogin">
      <Steps :current="current" direction="vertical" size="small">
        <Step title="选择数据">
          <template v-slot:content>
            <Button
              @click="uploadFile"
              icon="ios-cloud-upload-outline"
              size="small"
              class="file-btn"
            >
              选择数据文件
            </Button>
            <Table
              class="data-table"
              v-if="columns.length"
              size="small"
              :columns="[
                {
                  key: 'title',
                  slot: 'title',
                  title: '数据',
                  width: 120,
                  fixed: 'left',
                },
                {
                  key: 'type',
                  slot: 'type',
                  width: 70,
                  title: '类型',
                },
                {
                  key: 'format',
                  width: 120,
                  slot: 'format',
                  title: '格式/路径',
                },
              ]"
              :data="columns"
            >
              <template #title="{ row }">
                <Tooltip content="添加到画布" placement="right" transfer>
                  <Button
                    @click="addTextBox(row)"
                    type="primary"
                    size="small"
                    shape="circle"
                    icon="md-add"
                  ></Button>
                </Tooltip>
                {{ row.title }}
              </template>

              <template #type="{ index }">
                <Switch v-model="columns[index].type" true-value="img" false-value="text">
                  <template #open>
                    <span>图</span>
                  </template>
                  <template #close>
                    <span>字</span>
                  </template>
                </Switch>
              </template>

              <template #format="{ row, index }">
                <Input v-if="row.type === 'text'" v-model="columns[index].format" size="small" />
                <Button
                  v-if="row.type === 'img'"
                  @click="selectImgFiles(columns[index])"
                  size="small"
                  icon="ios-cloud-upload-outline"
                >
                  选择图片
                </Button>
              </template>
            </Table>
          </template>
        </Step>
        <Step title="字段关联">
          <template v-slot:content>找到模板中需要关联数据的字段，保存模板。</template>
        </Step>
        <Step title="选择模板">
          <template v-slot:content>
            <div class="tree-box">
              <TreeSelect
                multiple
                show-checkbox
                transfer
                size="small"
                v-model="selectTempls"
                :data="fileTree"
                @on-change="getSelectTemplsOption"
              />
            </div>
            <div class="tree-box bottom" v-if="selectTempls.length > 1">
              <Select
                v-model="contentTemplId"
                transfer
                size="small"
                clearable
                placeholder="内容模板设置 非必填"
              >
                <Option v-for="item in selectTemplsOption" :value="item.id" :key="item.id">
                  {{ item.name }}
                </Option>
              </Select>
              <Tooltip transfer :max-width="650">
                <Icon type="ios-help-circle" :size="16" />
                <template #content>
                  <img :src="tipImg" width="600" alt="内容模板批量生成 其他模板只生成1次" />
                </template>
              </Tooltip>
            </div>
          </template>
        </Step>
        <Step title="文件名称">
          <template v-slot:content>
            指定文件保存的格式

            <RadioGroup v-model="saveFileType" type="button" button-style="solid">
              <Radio label="按数据" value="data"></Radio>
              <Radio label="按模板" value="templ"></Radio>
              <Radio label="自定义" value="custom"></Radio>
            </RadioGroup>
            <Select v-if="saveFileType === 'templ'" v-model="fileNameKey" transfer size="small">
              <Option
                v-for="item in columns.filter((info) => info.type === 'text')"
                :value="item.key"
                :key="item.format"
              >
                ${index}-${模板名称}-{{ item.format }}.png
              </Option>
            </Select>
            <Select v-model="fileNameKey" v-if="data" transfer size="small">
              <Option
                v-for="item in columns.filter((info) => info.type === 'text')"
                :value="item.key"
                :key="item.format"
              >
                ${index}-${模板名称}-{{ item.format }}.png
              </Option>
            </Select>
            <Input v-if="saveFileType === 'custom'" v-model="fileNameKeyText" size="small" />
          </template>
        </Step>
        <Step title="开始生成">
          <template v-slot:content>
            <Space wrap>
              <Button type="primary" @click="start(false)">预览</Button>
              <Button type="primary" @click="start(true)">生成</Button>
            </Space>
          </template>
        </Step>
      </Steps>
    </div>

    <div style="text-align: center" v-show="!isLogin">请登录</div>
    <Modal
      v-model="modal"
      title="批量生成中，请耐心等待"
      :mask-closable="false"
      :closable="false"
      :footer-hide="true"
      :width="800"
    >
      <Progress
        :percent="parseInt((doneIndex / allGenerateLength) * 100)"
        :stroke-width="20"
        text-inside
      />
      <div style="text-align: center">进度：{{ doneIndex }}/{{ allGenerateLength }}</div>

      <Space wrap>
        <template v-for="(item, index) in previewImageList" :key="item.name">
          <Image
            :src="item.src"
            fit="contain"
            width="120px"
            height="120px"
            preview
            :preview-list="previewImageList.map((info) => info.src)"
            :initial-index="index"
          />
        </template>
      </Space>
      <div v-if="doneIndex === allGenerateLength">
        <Divider size="small" plain orientation="left"></Divider>
        <div class="btn-tip">
          <div v-if="!previewImageList.length">
            文件【设计批量生成.zip】已保存到您的浏览器下载目录
          </div>
          <Button type="primary" @click="modal = false">完成</Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup name="Batch">

import { useFileDialog } from '@vueuse/core';
import JSZip from 'jszip';
import { getUerFileTree, getTmplInfo } from '@/api/user';
import tipImg from '@/assets/conent.png';
import qrcodeImg from '@/assets/qrcode.jpeg';
import { Message, Modal } from 'view-ui-plus';
import useSelect from '@/hooks/select';
import { v4 as uuid } from 'uuid';
const { canvasEditor, fabric } = useSelect();
import { Utils } from '@kuaitu/core';
import { useRoute } from 'vue-router';
const route = useRoute();
import useMaterial from '@/hooks/useMaterial';
const { getTemplInfo } = useMaterial();
const { getImgStr, selectFiles } = Utils;

let tableData = ref([]);
const columns = ref([]);

const selectTempls = ref([]);

const fileNameKey = ref('');

const uploadFile = () => {
  const { onChange, open } = useFileDialog({
    accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });
  onChange(async (files) => {
    const { jsonData, columns: cols } = await canvasEditor.getExcelData(files[0]);
    tableData.value = jsonData;
    columns.value = cols;
  });
  open();
};

const selectImgFiles = (item) => {
  const { onChange, open } = useFileDialog({
    accept: 'image/jpeg,image/png',
    multiple: true,
  });
  onChange(async (files) => {
    item.imgMap = {};
    for (var i = 0; i < files.length; i++) {
      const base64Code = await canvasEditor.Utils.blobToBase64(files[i]);
      item.imgMap[files[i].name] = base64Code;
    }
  });
  open();
};

const doneIndex = ref(0);

const allGenerateLength = computed(() => {
  if (contentTemplId.value) {
    return tableData.value.length + selectTempls.value.length - 1;
  } else {
    return tableData.value.length * selectTempls.value.length;
  }
});

const checkImgColumns = () => {
  const hasImg = columns.value.some((item) => item.type === 'img');
  if (hasImg) {
    const imgCol = columns.value.filter((item) => item.type === 'img');
    const check = imgCol.every((item) => Object.keys(item.imgMap).length);
    if (!check) {
      return true;
    }
  }
  return false;
};
const start = async (down) => {
  if (columns.value.length === 0 || selectTempls.value.length === 0 || fileNameKey.value === '') {
    Message.error('请选择数据、模板、文件名格式');
    return;
  }
  if (checkImgColumns()) {
    Message.error('请为图类型选择文件');
    return;
  }
  const typeText = down ? '生成' : '预览';
  Modal.confirm({
    title: '确认开始' + typeText + '？',
    content: `<p>将按照数据表格${typeText}${allGenerateLength.value}张图片</p>`,
    onOk: () => startGenerate(down),
  });
};
const previewImageList = ref([]);
const startGenerate = async (idDown = false) => {
  try {
    canvasEditor.emitGenerate(true);
    previewImageList.value = [];
    const templMap = await getTemplsInfoData();

    let i = 0;
    doneIndex.value = 0;
    modal.value = true;
    const zip = new JSZip();

    const imgList = [];
    const templateStatusMap = {};

    for (const item of tableData.value) {
      for (const templId of Object.keys(templMap)) {
        const isContentTempl = String(templId) === String(contentTemplId.value);
        if (!contentTemplId.value || isContentTempl) {
          await generatePng(templMap, templId, i, item, zip, idDown, imgList);
          i++;
        } else {
          const notGenerate = templateStatusMap[templId];
          if (!notGenerate) {
            templateStatusMap[templId] = true;
            await generatePng(templMap, templId, i, item, zip, idDown, imgList);
            i++;
          }
        }
      }
    }
    if (idDown) {
      downZip(zip);
    } else {
      previewImageList.value = [...imgList];
    }
    await getTempData();
    canvasEditor.emitGenerate(false);
  } catch (error) {
    Message.error('生成失败，请选择正确的模板');
  }
};

const getTemplsInfoData = async () => {
  const templMap = {};
  for (const item of selectTempls.value) {
    const res = await getTmplInfo(item);
    templMap[item] = {
      name: res.data.data.attributes.name,
      json: res.data.data.attributes.json,
    };
  }
  return templMap;
};

const downZip = (zip) => {
  zip.generateAsync({ type: 'blob' }).then(function (content) {
    const anchorEl = document.createElement('a');
    anchorEl.href = URL.createObjectURL(content);
    anchorEl.download = '设计批量生成.zip';
    document.body.appendChild(anchorEl);
    anchorEl.click();
    anchorEl.remove();
  });
};

const generatePng = async (templMap, templId, i, item, zip, idDown, imgList) => {
  const templInfo = templMap[templId];
  const { data, fileName } = canvasEditor.mreageData({
    item: {
      ...item,
      index: i + 1,
      模板名称: templInfo.name,
    },
    columnFormats: columns.value,
    templ: templInfo.json,
    fileNameFormat: '${index}-${模板名称}-${' + fileNameKey.value + '}.png',
  });
  await loadJson(data);

  const base64 = await canvasEditor.preview();
  const [base64Blob] = canvasEditor.Utils.base64ToBlob(base64);
  if (idDown) {
    zip.file(fileName, base64Blob);
  } else {
    imgList.push({
      name: fileName,
      src: base64,
    });
  }
  await sleep();
  doneIndex.value = doneIndex.value + 1;
};

function sleep() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, 10);
  });
}

function loadJson(json) {
  return new Promise((resolve) => {
    canvasEditor.loadJSON(json, resolve);
  });
}

const fileTree = ref([]);
const loading = ref(false);
const isLogin = ref(true);
const getUerFileTreeHandle = async () => {
  try {
    loading.value = true;
    const res = await getUerFileTree();
    fileTree.value = [res.data.data];
  } catch (error) {
    console.log(error);
    isLogin.value = false;
  }
  loading.value = false;
};

canvasEditor.on('refreshFileList', getUerFileTreeHandle);

const modal = ref(false);

const selectTemplsOption = ref([]);
const contentTemplId = ref('');
const getSelectTemplsOption = async () => {
  selectTemplsOption.value = [];
  contentTemplId.value = '';
  for (const item of selectTempls.value) {
    const res = await getTmplInfo(item);
    selectTemplsOption.value.push({
      name: res.data.data.attributes.name,
      id: res.data.data.id,
    });
  }
};

const addTextBox = (row) => {
  if (row.type === 'text') {
    const text = new fabric.Textbox('{' + row.title + '}', {
      splitByGrapheme: true,
      width: 600,
      fontSize: 80,
      fill: '#000000FF',
      textAlign: 'center',
      linkData: ['text', row.title],
      id: uuid(),
    });
    canvasEditor.canvas.add(text);
    canvasEditor.canvas.setActiveObject(text);
    canvasEditor.position('center');
  } else {
    insertImg(row);
  }
};

const insertImg = (row) => {
  selectFiles({ accept: 'image/*', multiple: true }).then((fileList) => {
    Array.from(fileList).forEach((item) => {
      getImgStr(item).then((file) => {
        insertImgFile(file, row);
      });
    });
  });
};

function insertImgFile(file, row) {
  if (!file) throw new Error('file is undefined');
  const imgEl = document.createElement('img');
  imgEl.src = file;
  document.body.appendChild(imgEl);
  imgEl.onload = () => {
    const imgInstance = new fabric.Image(imgEl, {
      id: uuid(),
      name: row.title,
      linkData: ['src', row.title],
    });

    canvasEditor.dragAddItem(imgInstance);
    canvasEditor.position('center');
    canvasEditor.canvas.renderAll();
    imgEl.remove();
  };
}

const getTempData = async () => {
  if (route.query.id) {
    const data = await getTemplInfo(route.query.id);
    canvasEditor.loadJSON(JSON.stringify(data.data.attributes.json));
  }
};

const saveFileType = ref('');
onMounted(() => {
  getUerFileTreeHandle();
});
</script>

<style scoped lang="less">
.data-table {
  margin-top: 10px;
}

/deep/.ivu-divider-horizontal {
  margin: 12px 0;
}

.tree-box {
  display: flex;
  align-items: center;
  button {
    margin-left: 5px;
  }
  /deep/.ivu-tooltip {
    margin-left: 11px;
  }

  &.bottom {
    padding-top: 10px;
  }
}

.btn-tip {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tip-box {
  padding-top: 60px;
  img {
    display: block;
    width: 100%;
  }
}
</style>
