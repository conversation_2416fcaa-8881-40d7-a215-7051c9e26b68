<template>
  <div>
    <uploadMaterial></uploadMaterial>
  </div>
</template>

<script setup name="ImportSvg">
import searchType from '@/components/common/searchType';
import typeList from '@/components/common/typeList.vue';
import pageList from '@/components/common/pageList.vue';
import useSelect from '@/hooks/select';
import { getMaterialInfoUrl, getMaterialPreviewUrl } from '@/hooks/usePageList';
import { getMaterialTypes, getMaterialsByType, getMaterials } from '@/api/material';
import useCalculate from '@/hooks/useCalculate';
import { fabric } from 'fabric';
import { v4 as uuid } from 'uuid';
import { useRoute } from 'vue-router';
import { Utils } from '@kuaitu/core';
import uploadMaterial from '@/components/myMaterial/uploadMaterial.vue';

const { canvasEditor } = useSelect();

const { isOutsideCanvas } = useCalculate();
const selectTypeRef = ref();

const filters = reactive({
  material_type: {
    $contains: '',
  },
  name: {
    $contains: '',
  },
});

const formatData = (data) => {
  return data.map((item) => {
    return {
      id: item.id,
      name: item.attributes.name,
      desc: item.attributes.desc,
      src: getMaterialInfoUrl(item.attributes.img),
      previewSrc: getMaterialPreviewUrl(item.attributes.img),
    };
  });
};

const searchChange = async ({ searchKeyWord, typeValue }) => {
  filters.name.$contains = '';
  filters.material_type.$contains = '';
  await nextTick();
  filters.name.$contains = searchKeyWord;
  filters.material_type.$contains = typeValue;
};

const selectType = async (type) => {
  filters.material_type.$contains = type;
  selectTypeRef.value.setType(type);
  await nextTick();
};

const dragItem = ({ e }) => {
  if (isOutsideCanvas(e.clientX, e.clientY)) return;
  const target = e.target;
  const imgType = canvasEditor.getImageExtension(target.src);
  if (imgType === 'svg') {
    fabric.loadSVGFromURL(target.src, (objects) => {
      const item = fabric.util.groupSVGElements(objects, {
        shadow: '',
        fontFamily: 'arial',
        id: uuid(),
        name: 'svg元素',
      });
      canvasEditor.dragAddItem(item, e);
    });
  } else {
    fabric.Image.fromURL(
      target.src,
      (imgEl) => {
        imgEl.set({
          left: 100,
          top: 100,
        });
        canvasEditor.dragAddItem(imgEl, e);
      },
      { crossOrigin: 'anonymous' }
    );
  }
};

const addItem = ({ e }) => {
  const target = e.target;
  const imgType = canvasEditor.getImageExtension(target.src);
  if (imgType === 'svg') {
    fabric.loadSVGFromURL(target.src, (objects) => {
      const item = fabric.util.groupSVGElements(objects, {
        shadow: '',
        fontFamily: 'arial',
        id: uuid(),
        name: 'svg元素',
      });
      canvasEditor.dragAddItem(item);
    });
  } else {
    fabric.Image.fromURL(
      target.src,
      (imgEl) => {
        imgEl.set({
          left: 100,
          top: 100,
        });
        canvasEditor.dragAddItem(imgEl);
      },
      { crossOrigin: 'anonymous' }
    );
  }
};
</script>

<style scoped lang="less"></style>
