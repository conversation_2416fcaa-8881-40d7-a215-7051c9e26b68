<template>
  <div class="box attr-item-box" v-if="mixinState.mSelectMode === 'one'">
    <!-- <h3>位置信息</h3> -->
    <!-- 通用属性 -->
    <div v-show="baseType.includes(mixinState.mSelectOneType)">
      <Divider plain orientation="left"><h4>位置信息</h4></Divider>
      <Row :gutter="10">
        <Col flex="1">
          <InputNumber
            v-model="baseAttr.left"
            @on-change="(value) => changeCommon('left', value)"
            :append="$t('attributes.left')"
          ></InputNumber>
        </Col>
        <Col flex="1">
          <InputNumber
            v-model="baseAttr.top"
            @on-change="(value) => changeCommon('top', value)"
            :append="$t('attributes.top')"
          ></InputNumber>
        </Col>
      </Row>
      <Form :label-width="40" class="form-wrap">
        <FormItem :label="$t('attributes.angle')" class="form-wrap-item">
          <Slider
            v-model="baseAttr.angle"
            :max="360"
            @on-input="(value) => changeCommon('angle', value)"
          ></Slider>
          <InputNumber v-model="baseAttr.angle" @on-input="(value) => changeCommon('angle', value)" />
        </FormItem>
        <FormItem :label="$t('attributes.opacity')" class="form-wrap-item">
          <Slider
            v-model="baseAttr.opacity"
            @on-input="(value) => changeCommon('opacity', value)"
          ></Slider>
          <InputNumber  v-model="baseAttr.opacity" @on-input="(value) => changeCommon('opacity', value)" />
        </FormItem>
      </Form>
    </div>
    <!-- <Divider plain></Divider> -->
  </div>
</template>

<script setup name="AttrBute">
import useSelect from '@/hooks/select';
import InputNumber from '@/components/inputNumber';

const update = getCurrentInstance();
const { mixinState, canvasEditor } = useSelect();

const baseType = [
  'text',
  'i-text',
  'textbox',
  'rect',
  'circle',
  'triangle',
  'polygon',
  'image',
  'group',
  'line',
  'arrow',
  'thinTailArrow',
  'curved-text'
];

const baseAttr = reactive({
  opacity: 0,
  angle: 0,
  left: 0,
  top: 0,
  rx: 0,
  ry: 0,
});

const getObjectAttr = (e) => {
  nextTick(() => {
    const activeObject = canvasEditor.canvas.getActiveObject();
    if (e && e.target && e.target !== activeObject) return;
    if (activeObject && baseType.includes(activeObject.type)) {
      baseAttr.opacity = activeObject.get('opacity') * 100;
      baseAttr.left = activeObject.get('left');
      baseAttr.top = activeObject.get('top');
      const angle = activeObject.get('angle') || 0;
      baseAttr.angle = angle >= 0 ? angle : angle + 360 ;
    }
  })
};

const changeCommon = (key, value) => {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  if (activeObject) {
    if (key === 'opacity') {
      activeObject && activeObject.set(key, value / 100);
      canvasEditor.canvas.renderAll();
      return;
    }
    if (key === 'angle') {
      activeObject.rotate(value);
      canvasEditor.canvas.renderAll();
      return;
    }
    activeObject && activeObject.set(key, value);
    canvasEditor.canvas.renderAll();
  }
};

const selectCancel = () => {
  update?.proxy?.$forceUpdate();
};

onMounted(() => {
  canvasEditor.on('selectCancel', selectCancel);
  canvasEditor.on('selectOne', getObjectAttr);
  canvasEditor.canvas.on('object:modified', getObjectAttr);
});

onBeforeUnmount(() => {
  canvasEditor.off('selectCancel', selectCancel);
  canvasEditor.off('selectOne', getObjectAttr);
  canvasEditor.canvas.off('object:modified', getObjectAttr);
});
</script>

<style scoped lang="less">
:deep(.ivu-input-number) {
  display: block;
  width: 100%;
}

.ivu-form-item {
  background: #f6f7f9;
  border-radius: 5px;
  padding: 0 5px;
  margin-bottom: 10px;
}

.ivu-row {
  margin-bottom: 10px;
}

</style>
<style lang="less">
.form-wrap-item .ivu-form-item-content {
  display: flex;
  .ivu-slider {
    flex: 1;
  }
  .ivu-input-number {
    margin-left: 10px;
    width: 100px;
    border: 1px solid #dcdee2;
  }
}
</style>
