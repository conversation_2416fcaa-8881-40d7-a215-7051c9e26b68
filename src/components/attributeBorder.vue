<template>
  <div class="box attr-item-box" v-if="mixinState.mSelectMode === 'one'">
    <!-- <h3>边框</h3> -->
    <Divider plain orientation="left"><h4>字体属性与边框</h4></Divider>
    <!-- 通用属性 -->
    <div>
      <Row :gutter="12">
        <Col flex="1">
          <div class="ivu-col__box">
            <span class="label">{{ $t('color') }}</span>
            <div class="content">
              <ColorPicker
                v-model="baseAttr.stroke"
                @on-change="(value) => changeCommon('stroke', value)"
                alpha
              />
            </div>
          </div>
        </Col>
        <Col flex="1">
          <InputNumber
            v-model="baseAttr.strokeWidth"
            @on-change="(value) => changeCommon('strokeWidth', value)"
            :append="$t('width')"
            :min="0"
          ></InputNumber>
        </Col>
      </Row>

      <Row :gutter="12">
        <Col flex="1">
          <div class="ivu-col__box">
            <span class="label">{{ $t('attributes.stroke') }}</span>
            <div class="content">
              <Select v-model="baseAttr.strokeDashArray" @on-change="borderSet">
                <Option
                  v-for="item in strokeDashList"
                  :value="item.label"
                  :key="`stroke-${item.label}`"
                >
                  {{ item.label }}
                </Option>
              </Select>
            </div>
          </div>
        </Col>
      </Row>
    </div>
    <!-- <Divider plain></Divider> -->
  </div>
</template>

<script setup name="AttrBute">
import useSelect from '@/hooks/select';
import InputNumber from '@/components/inputNumber';

const update = getCurrentInstance();
const { mixinState, canvasEditor } = useSelect();

const baseAttr = reactive({
  stroke: '#fff',
  strokeWidth: 0,
  strokeDashArray: [],
});

const strokeDashList = [
  {
    value: {
      strokeUniform: true,
      strokeDashArray: [],
      strokeLineCap: 'butt',
    },
    label: 'Stroke',
  },
];
const getObjectAttr = (e) => {
  const activeObject = canvasEditor.canvas.getActiveObject();
  if (e && e.target && e.target !== activeObject) return;
  if (activeObject) {
    baseAttr.stroke = activeObject.get('stroke');
    baseAttr.strokeWidth = activeObject.get('strokeWidth');
    const strokeDashArray = JSON.stringify(activeObject.get('strokeDashArray') || []);
    const target = strokeDashList.find((item) => {
      return (
        JSON.stringify(item.value.strokeDashArray) === strokeDashArray &&
        activeObject.get('strokeLineCap') === item.value.strokeLineCap
      );
    });
    if (target) {
      baseAttr.strokeDashArray = target.label;
    }
  }
};

const changeCommon = (key, value) => {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  if (activeObject.type === 'curved-text' && key === 'stroke') {
    activeObject.set('strokeStyle', value);
    activeObject.set('strokeUniform', true);
    canvasEditor.canvas.renderAll();
  }
  if (activeObject) {
    activeObject.set(key, value);
    activeObject.set('strokeUniform', true);
    canvasEditor.canvas.renderAll();
  }
};

const borderSet = (key) => {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  if (activeObject) {
    const stroke = strokeDashList.find((item) => item.label === key);
    activeObject.set(stroke.value);
    canvasEditor.canvas.renderAll();
  }
};

const selectCancel = () => {
  update?.proxy?.$forceUpdate();
};

onMounted(() => {
  getObjectAttr();
  canvasEditor.on('selectCancel', selectCancel);
  canvasEditor.on('selectOne', getObjectAttr);
  canvasEditor.canvas.on('object:modified', getObjectAttr);
});

onBeforeUnmount(() => {
  canvasEditor.off('selectCancel', selectCancel);
  canvasEditor.off('selectOne', getObjectAttr);
  canvasEditor.canvas.off('object:modified', getObjectAttr);
});
</script>

<style scoped lang="less">
:deep(.ivu-input-number) {
  display: block;
  width: 100%;
}

:deep(.ivu-color-picker) {
  display: block;
}
.ivu-row {
  margin-bottom: 8px;
  .ivu-col {
    position: inherit;
    &__box {
      display: flex;
      align-items: center;
      background: #f8f8f8;
      border-radius: 4px;
      gap: 8px;
    }
  }

  .label {
    padding-left: 8px;
  }
  .content {
    flex: 1;
    :deep(.--input),
    :deep(.ivu-select-selection) {
      background-color: transparent;
      border: none !important;
      box-shadow: none !important;
    }
  }
}
</style>
