<!-- file 文件另存为弹框 -->
<template>
  <Form :model="formData" :label-width="120">
    <FormItem label="文件名称：">
      <Input v-model="formData.name" style="width: 200px" placeholder="请输入名称" />
    </FormItem>
    <FormItem label="存储文件夹：">
      <div class="c-file-save">
        <Tree :data="data" :render="renderContent" :load-data="handleLoadData" @on-select-change="handleNodeClick"></Tree>
      </div>
    </FormItem>
  </Form>
</template>

<script setup>
import { saveAsFile, getTemplateList } from '@/api/v2'
import { Message } from 'view-ui-plus'
import { useRoute } from 'vue-router'

const route = useRoute()

const formData = reactive({
  name: ''
})

const data = ref([
  {
    title: '全部',
    loading: false,
    id: '',
    children: []
  }
])

const handleLoadData = async ({ id }, callback) => {
  const { data } = await getTemplateList({ parentId: id, pageSize: 100, pageNum: 1 })
  const result = []
  data.forEach(record => {
    if (record.type === 'fileType') {
      result.push({
        title: record.name,
        id: record.id,
        loading: false,
        children: []
      })
    }
  })
  callback(result)
}

let currentFolder = {}
const handleNodeClick = (item) => {
  currentFolder = item
}

const handleOk = async () => {
  if (!Object.keys(currentFolder).length) {
    Message.warning('请选择存储文件夹')
    return false
  }
  if (!formData.name) {
    return Message.warning('请输入文件名称')
  }
  const result = await saveAsFile({
    newFileName: formData.name,
    sourceId: Number(route.query.id),
    targetId: currentFolder[0].id
  })
  if (!result.code) {
    Message.success('保存成功')
    formData.name = ''
    return true
  } else {
    Message.error(result.message)
    return false
  }
}

const renderContent = (h, { root, node, data }) => {
  return h('span', { style: { display: 'inline-block', width: '100%' }}, [h('span', [h(resolveComponent('Icon'), { type: 'ios-folder-outline', style: { marginRight: '8px' } }), h('span', data.title)])])
}

defineExpose({
  handleOk
})
</script>

<style lang="less">
.c-file-save {
  height: 300px;
  overflow-y: auto;
  .ivu-tree-children {
    li:first-child {
      margin-top: 0;
    }
  }
}
</style>
