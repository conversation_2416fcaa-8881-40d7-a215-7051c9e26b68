<!-- file 图片大小 -->
<template>
  <div class="c-img-size" v-if="mixinState.mSelectMode === 'one' && state.type === 'image'">
    <Divider plain orientation="left">
      <h4>图片大小信息</h4>
    </Divider>
    <Row>
      <Col span="11" class="c-img-size__col">
        <span style="white-space: nowrap">宽度：</span>
        <Input v-model="state.width" @on-blur="handleBlur('width')" />
      </Col>
      <Col span="2" />
      <Col span="11" class="c-img-size__col">
        <span style="white-space: nowrap">高度：</span>
        <Input v-model="state.height" @on-blur="handleBlur('height')" />
      </Col>
    </Row>
  </div>
</template>

<script setup name="ImgSize">
import useSelect from '@/hooks/select'

const { mixinState, canvasEditor } = useSelect()

const state = reactive({
  type: '',
  width: 0,
  height: 0,
  originWidth: 0,
  originHeight: 0
})

const handleSelectOne = () => {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0]
  state.type = activeObject.type
  if (activeObject && activeObject.type === 'image') {
    state.width = Math.round(activeObject.getScaledWidth())
    state.height = Math.round(activeObject.getScaledHeight())
    state.originWidth = activeObject.width
    state.originHeight = activeObject.height
  }
}

const handleBlur = (type) => {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0]
  const scaleX = state.width / state.originWidth
  const scaleY = state.height / state.originHeight
  const record = {}
  if (type === 'width') {
    record.scaleX = scaleX
  } else {
    record.scaleY = scaleY
  }
  activeObject.set(record)
  canvasEditor.canvas.renderAll()
}

onMounted(() => {
  canvasEditor.on('selectOne', handleSelectOne);
})

onBeforeUnmount(() => {
  canvasEditor.off('selectOne', handleSelectOne);
})
</script>

<style lang="less">
.c-img-size__col {
  display: flex;
  align-items: center;
}
</style>
