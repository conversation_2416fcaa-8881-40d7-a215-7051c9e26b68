<template>
  <Tooltip :content="$t('quick.hide')" v-if="mixinState.mSelectMode === 'one'">
    <Button long v-if="isHide" @click="doHide(false)" icon="md-eye-off" type="text"></Button>
    <Button long v-else @click="doHide(true)" icon="md-eye" type="text"></Button>
  </Tooltip>
</template>

<script setup name="Hide">
import useSelect from '@/hooks/select';
import { onBeforeUnmount, onMounted } from 'vue';

const { mixinState, canvasEditor } = useSelect();
const isHide = ref(false);

const doHide = (hide) => {
  const activeObject = canvasEditor.canvas.getActiveObject();
  activeObject.set('visible', !hide);
  canvasEditor.canvas.requestRenderAll();
  isHide.value = hide;
};

const handleSelected = () => {
  const activeObject = canvasEditor.canvas.getActiveObject();
  isHide.value = !activeObject.visible;
};

onMounted(() => {
  canvasEditor.on('selectOne', handleSelected);
});

onBeforeUnmount(() => {
  canvasEditor.off('selectOne', handleSelected);
});
</script>
