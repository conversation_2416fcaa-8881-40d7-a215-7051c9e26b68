<!-- file 富文本 -->
<template>
  <Toolbar
    style="border: 1px solid #ccc;border-bottom-color: transparent;"
    mode="default"
    :editor="editorRef"
    :defaultConfig="toolbarConfig"
  />
  <Editor
    style="height: 500px; overflow-y: hidden;border: 1px solid #ccc;"
    mode="default"
    v-model="valueHtml"
    :defaultConfig="editorConfig"
    @onCreated="handleCreated"
  />
</template>

<script setup name="Wangeditor">
import dayjs from 'dayjs'
import '@wangeditor/editor/dist/css/style.css'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import Loading from '@/components/load/index.js'
import { getDocumentV2 } from '@/api/v2'
import { getSrcFromHtml } from '@/utils/token'
import { cos } from '@/utils/cos'

const editorRef = shallowRef()
const valueHtml = ref('')
const props = defineProps({
  documentId: {
    type: [String, Number],
    default: ''
  }
})

const alreadyImg = ref([])
const toolbarConfig = {
  excludeKeys: ['group-video']
}
const editorConfig = {
  placeholder: '请输入内容...',
  autoFocus: true,
  MENU_CONF: {
    uploadImage: {
      async customUpload(file, insertFn) {
        Loading.show('图片上传中')
        const { Location } = await cos.uploadFileToDocument(file, `${dayjs().format('YYYY-MM-DD_HH-mm-ss_doc')}.png`)
        Loading.hide()
        insertFn('//' + Location, '图片加载失败')
      }
    }
  }
}

const handleCreated = (editor) => {
  editorRef.value = editor // 记录 editor 实例，重要！
}

const handleOk = () => {
  const imgs = editorRef.value.getElemsByType('image').map(item => item.src)
  const delImgList = []
  alreadyImg.value.forEach(src => {
    if (!imgs.includes(src)) {
      const url =  'doc/' + src.split('/').at(-1)
      delImgList.push(url)
    }
  })
  const name = editorRef.value.getText().split('\n')[0]
  const docContent = editorRef.value.getHtml()
  if (props.documentId) {
    return {
      id: props.documentId,
      name,
      docContent,
      delImgList
    }
  }
  return {
    name,
    docContent
  }
}

onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

onMounted(async () => {
  if (!props.documentId) return
  const result = await getDocumentV2(props.documentId)
  if (result.data.docContent) {
    valueHtml.value = result.data.docContent
    alreadyImg.value = getSrcFromHtml(result.data.docContent)
  }
})

defineExpose({
  handleOk
})
</script>

<style lang="less"></style>
