<template>
  <div class="calendarCom-wrap">
    <Button type="primary" @click="calendarmodal = true">
      生成日历
    </Button>
    <Modal v-model="calendarmodal" title="日历配置"
      width="600" :closable='false' :mask-closable='false'
      ok-text='生成日历'
      @on-ok='handleSubmit' @on-cancel='calendarmodal = false'
    >
      <Form ref="formRef" :model="formData" :label-width="70">
        <Divider plain orientation="left">星期</Divider>
        <div class="wrap">
          <FormItem label="字体">
            <Select v-model="formData.weekFontFamily" filterable :remote-method="onQueryChange">
              <RecycleScroller
                :buffer="200"
                :prerender="10"
                :items="weekFontsList"
                :item-size="54"
                key-field="name"
                style="height: 180px; width: 400px"
              >
                <template v-slot="{ item, index }">
                  <Option
                    :value="item.name"
                    :key="`font-${item.name}-${index}`"
                  >
                    <div class="font-item" :style="`background-image:url('${item.img}');`">
                      {{ !item.img ? item : '' }}
                      <span style="display: none">{{ item.name }}</span>
                    </div>
                  </Option>
                </template>
              </RecycleScroller>
            </Select>
            <Spin size="small" fix :show="isFontLoading">加载字体中...</Spin>
          </FormItem>
          <FormItem label="字体大小">
            <InputNumber v-model="formData.weekSize" :min="1"></InputNumber>
          </FormItem>
          <FormItem label="字体描边">
            <InputNumber v-model="formData.weekBold" :min="0" :step='1'></InputNumber>
          </FormItem>
          <FormItem label="字体颜色">
            <ColorPicker v-model="formData.weekColor"></ColorPicker>
          </FormItem>
        </div>
        <Divider plain orientation="left">日期</Divider>
        <div class="wrap">
          <FormItem label="一号是">
            <Select v-model="formData.week">
              <Option v-for="(item, index) in weekList" :value="item.value" :key="index">{{ item.label }}</Option>
            </Select>
          </FormItem>
          <FormItem label="月天数">
            <Select v-model="formData.mouthLength">
              <Option v-for="(item, index) in mouthLength" :value="item" :key="index">{{ item }}</Option>
            </Select>
          </FormItem>
          <FormItem label="字体">
            <Select v-model="formData.fontFamily" filterable :remote-method="onChange">
              <RecycleScroller
                :buffer="200"
                :prerender="10"
                :items="fontsList"
                :item-size="54"
                key-field="name"
                style="height: 180px; width: 400px"
              >
                <template v-slot="{ item, index }">
                  <Option
                    :value="item.name"
                    :key="`font-${item.name}-${index}`"
                  >
                    <div class="font-item" :style="`background-image:url('${item.img}');`">
                      {{ !item.img ? item : '' }}
                      <span style="display: none">{{ item.name }}</span>
                    </div>
                  </Option>
                </template>
              </RecycleScroller>
            </Select>
            <Spin size="small" fix :show="isFontLoading">加载字体中...</Spin>
          </FormItem>
          <FormItem label="字体大小">
            <InputNumber v-model="formData.size" :min="1"></InputNumber>
          </FormItem>
          <FormItem label="字体描边">
            <InputNumber v-model="formData.bold" :min="0" :step='1'></InputNumber>
          </FormItem>
          <FormItem label="字体颜色">
            <ColorPicker v-model="formData.color"></ColorPicker>
          </FormItem>
        </div>
        <Divider plain orientation="left"></Divider>
        <FormItem label="缩放">
          <InputNumber v-model="formData.scale" :min="0.1" :step='0.1'></InputNumber>
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script setup>
  import { fabric } from 'fabric'
  import useSelect from '@/hooks/select'
  import noGroup from '@/assets/calendarJson/new.json'
  import { debounce } from 'lodash-es'
  import { getFontFamilyList } from '@/api/v2'
  import { COS_URL } from '@/utils/cos'

  const APP_APIHOST = import.meta.env.APP_APIHOST
  const { canvasEditor } = useSelect()

  const calendarmodal = ref(false)

  const weekFontsList = ref([])
  const fontsList = ref([])
  const allList = ref([])

  const formData = reactive({
    week: 1,
    mouthLength: 28,
    fontFamily: '',
    size: 40,
    color: '#000000ff',
    scale: 1,
    bold: 0,
    weekFontFamily: '',
    weekSize: 40,
    weekBold: 0,
    weekColor: '#000000ff'
  })

  const weekList = [
    { value: 1, label: '周一' },
    { value: 2, label: '周二' },
    { value: 3, label: '周三' },
    { value: 4, label: '周四' },
    { value: 5, label: '周五' },
    { value: 6, label: '周六' },
    { value: 0, label: '周日' },
  ]

  const mouthLength = [ 28, 29, 30, 31 ]

  const handleSubmit = () => {
    new fabric.util.enlivenObjects(noGroup.objects, objects => {
      var origRenderOnAddRemove = canvasEditor.canvas.renderOnAddRemove
      canvasEditor.canvas.renderOnAddRemove = false

      objects.forEach( o => {
        if(o && o.type === 'group') {
          o.scaleX = formData.scale || 1
          o.scaleY = formData.scale || 1
          o._objects.forEach(item => {
            const isWeek = /[A-Za-z]$/.test(item.text)

            let text = item.text
            if(!isWeek && typeof Number(item.text) === 'number') {
              text = Number(item.text)
              if(text <= formData.week) text = ''
              else text = text - formData.week
              text = text < 10 ? `${text}` : `${Math.floor(text / 10)}${text % 10}`
            }
            item.fill = isWeek ? formData.weekColor : formData.color
            item.fontSize = isWeek ? formData.weekSize : formData.size || 40
            item.stroke = isWeek ? formData.weekColor : formData.color
            item.strokeWidth = isWeek ? formData.weekBold : formData.bold || 0

            item.width = 45
            item.textAlign = 'center'
            item.pathSide = 'center'
            item.fontFamily = isWeek ? formData.weekFontFamily : formData.fontFamily
            item.visible = isWeek || Number(item.text) <= formData.mouthLength + formData.week
            item.text = text
          })
        }
        canvasEditor.canvas.add(o)
      })
      canvasEditor.canvas.renderOnAddRemove = origRenderOnAddRemove
      canvasEditor.canvas.renderAll()
    })
  }

  const isFontLoading = ref(true)
  const getFontListHandle = () => {
    getFontFamilyList({
        pageNum: 1,
        pageSize: 100000
      }).then((result) => {
      isFontLoading.value = false
      const list = result.data.map(item => ({
        id: item.id,
        name: item.name,
        file: COS_URL + item.fileUrl,
        img: APP_APIHOST + item.imgUrl
      }))
      fontsList.value = list
      weekFontsList.value = list
      allList.value = list
    })
    .catch((err) => {
      console.log(err)
    })
  }

  const onQueryChange = debounce(function (str) {
    if (str === '') {
      weekFontsList.value = allList.value
    } else {
      weekFontsList.value = allList.value.filter(item => item.name.toUpperCase().search(str.toUpperCase()) !== -1)
    }
  }, 500)

  const onChange = debounce(function (str) {
    if (str === '') {
      fontsList.value = allList.value
    } else {
      fontsList.value = allList.value.filter(item => item.name.toUpperCase().search(str.toUpperCase()) !== -1)
    }
  }, 500)

  onMounted( async () => {
    await getFontListHandle()
  })

</script>

<style scoped lang="less">
  .calendarCom-wrap, button {
    width: 100%;
  }
  .wrap {
    display: flex;
    flex-flow: row wrap;
    .ivu-form-item {
      width: 50%;
      padding-right: 10px;
    }
    .ivu-input-number {
      width: 100%;
    }
  }
  .font-item {
    height: 40px;
    width: 330px;
    background-size: auto 40px;
    background-repeat: no-repeat;
  }
</style>
