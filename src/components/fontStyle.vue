<template>
  <div>
    <fontList></fontList>
  </div>
</template>

<script setup name="ImportSvg">
import fontList from '@/components/myMaterial/fontList.vue'
import searchType from '@/components/common/searchType';
import typeList from '@/components/common/typeList.vue';
import pageList from '@/components/common/pageList.vue';
import useSelect from '@/hooks/select';
import useCalculate from '@/hooks/useCalculate';
import { getMaterialInfoUrl, getMaterialPreviewUrl } from '@/hooks/usePageList';
import { getFontStyleTypes, getFontStyleListByType, getFontStyles } from '@/api/material';
import { fabric } from 'fabric';
import { v4 as uuid } from 'uuid';
import { Spin } from 'view-ui-plus';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const { canvasEditor } = useSelect();

const { isOutsideCanvas } = useCalculate();

const dragItem = async ({ e, info: item }) => {
  if (isOutsideCanvas(e.clientX, e.clientY)) return;
  Spin.show({
    render: (h) => h('div', t('alert.loading_data')),
  });
  await canvasEditor.downFontByJSON(JSON.stringify(item.json));
  const el = JSON.parse(JSON.stringify(item.json));
  el.id = uuid();
  const elType = capitalizeFirstLetter(el.type);
  new fabric[elType].fromObject(el, (fabricEl) => {
    canvasEditor.dragAddItem(fabricEl, e);
    Spin.hide();
  });
};

const addItem = async ({ info: item }) => {
  Spin.show({
    render: (h) => h('div', t('alert.loading_data')),
  });
  await canvasEditor.downFontByJSON(JSON.stringify(item.json));
  const el = JSON.parse(JSON.stringify(item.json));
  el.id = uuid();
  const elType = capitalizeFirstLetter(el.type);
  new fabric[elType].fromObject(el, (fabricEl) => {
    canvasEditor.dragAddItem(fabricEl);
    Spin.hide();
  });
};

function capitalizeFirstLetter(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

const selectTypeRef = ref();

const filters = reactive({
  font_style_type: {
    $contains: '',
  },
  name: {
    $contains: '',
  },
});

const formatData = (data) => {
  return data.map((item) => {
    return {
      id: item.id,
      name: item.attributes.name,
      desc: item.attributes.desc,
      json: item.attributes.json,
      src: getMaterialInfoUrl(item.attributes.img),
      previewSrc: getMaterialPreviewUrl(item.attributes.img),
    };
  });
};

const searchChange = async ({ searchKeyWord, typeValue }) => {
  filters.name.$contains = '';
  filters.font_style_type.$contains = '';
  await nextTick();
  filters.name.$contains = searchKeyWord;
  filters.font_style_type.$contains = typeValue;
};

const selectType = async (type) => {
  filters.font_style_type.$contains = type;
  selectTypeRef.value.setType(type);
  await nextTick();
};
</script>

<style scoped lang="less"></style>
