<template>
  <div v-if="mixinState.mSelectMode === 'one' && state.type === 'image'" class="box">
    <Divider plain orientation="left">
      <h4>图片滤镜</h4>
    </Divider>
    <Collapse>
      <Panel name="1">
        {{ $t('filters.simple') }}
        <template #content>
          <div class="filter-box">
            <div class="filter-item" v-for="(value, key) in state.noParamsFilters" :key="key">
              <img
                :src="getImageUrl(key)"
                alt=""
                @click="changeFilters(key, !noParamsFilters[key])"
              />
              <Checkbox
                v-model="state.noParamsFilters[key]"
                @on-change="(val) => changeFilters(key, val)"
              >
                {{ $t('filters.' + key) }}
              </Checkbox>
            </div>
          </div>
        </template>
      </Panel>
      <Panel name="2">
        {{ $t('filters.complex') }}
        <template #content>
          <div>
            <div
              class="filter-item has-params"
              v-for="item in [...state.paramsFilters, ...state.combinationFilters]"
              :key="item.type"
            >
              <Checkbox v-model="item.status" @on-change="changeFiltersByParams(item.type)">
                {{ $t('filters.' + item.type) }}
              </Checkbox>
              <div v-if="item.status" class="content">
                <div class="content slider-box" v-for="info in item.params" :key="info">
                  <div v-if="info.uiType === uiType.SELECT">
                    <RadioGroup v-model="info.value" @on-change="changeFiltersByParams(item.type)">
                      <Radio :label="listItem" v-for="listItem in info.list" :key="listItem">
                        {{ $t('filters.' + item.type + 'List.' + listItem) }}
                      </Radio>
                    </RadioGroup>
                  </div>
                  <div v-if="info.uiType === uiType.NUMBER">
                    <Slider
                      v-model="info.value"
                      :max="info.max"
                      :min="info.min"
                      :step="info.step"
                      @on-input="changeFiltersByParams(item.type)"
                    ></Slider>
                  </div>
                  <div v-if="info.uiType === uiType.COLOR">
                    <ColorPicker
                      v-model="info.value"
                      alpha
                      size="small"
                      @on-change="changeFiltersByParams(item.type)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </Panel>
      <Panel name="3">
        {{ $t('图层颜色') }}
        <template #content>
          <div class="c-filter-color">
            <span>底色:</span>
            <ColorPicker v-model="pictureColor" @on-change="handleColorFilter" />
            <span class="c-filter-color__text" @click="handleClearColor">清除</span>
          </div>
        </template>
      </Panel>
    </Collapse>
  </div>
</template>

<script name="Filter" setup>
import useSelect from '@/hooks/select';
import { uiType, paramsFilters, combinationFilters } from '@/config/constants/filter';

const { fabric, mixinState, canvasEditor } = useSelect();
const update = getCurrentInstance();
const noParamsFilters = {
  BlackWhite: false,
  Brownie: false,
  Vintage: false,
  Kodachrome: false,
  technicolor: false,
  Polaroid: false,
  Invert: false,
  Sepia: false,
};

const state = reactive({
  uiType,
  noParamsFilters,
  paramsFilters: [...paramsFilters],
  combinationFilters: [...combinationFilters],
  type: '',
});

const pictureColor = ref('');

const changeFilters = (type, value) => {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  state.noParamsFilters[type] = value;
  if (value) {
    const itemFilter = _getFilter(activeObject, type);
    if (!itemFilter) {
      _createFilter(activeObject, type);
    }
  } else {
    _removeFilter(activeObject, type);
  }
};
const changeFiltersByParams = (type) => {
  console.log(type)
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  const filtersAll = [...state.paramsFilters, ...state.combinationFilters];
  const moduleInfo = filtersAll.find((item) => item.type === type);
  if (moduleInfo.status) {
    if (moduleInfo.handler) {
      _changeAttrByHandler(moduleInfo);
    } else {
      moduleInfo.params.forEach((paramsItem) => {
        _changeAttr(type, paramsItem.key, paramsItem.value);
      });
    }
  } else {
    _removeFilter(activeObject, type);
  }
};

const handleCallBackColor = (filters = []) => {
  let isBlendColor = true
  filters.forEach(item => {
    if (item.type !== 'BlendColor') {
      isBlendColor = false
    }
  })
  if (isBlendColor && !!filters.length) {
    pictureColor.value = filters[filters.length - 1].color
  }
}

const handleSelectOne = () => {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  pictureColor.value = ''
  if (activeObject) {
    state.type = activeObject.type;
    if (state.type === 'image') {
      handleCallBackColor(activeObject.filters)
      Object.keys(noParamsFilters).forEach((type) => {
        state.noParamsFilters[type] = !!_getFilter(activeObject, type);
        update?.proxy?.$forceUpdate();
      });
      paramsFilters.forEach((filterItem) => {
        const moduleInfo = state.paramsFilters.find((item) => item.type === filterItem.type);
        const filterInfo = _getFilter(activeObject, filterItem.type);
        moduleInfo.status = !!filterInfo;
        moduleInfo.params.forEach((paramsItem) => {
          paramsItem.value = filterInfo ? filterInfo[paramsItem.key] : paramsItem.value;
        });
      });

      combinationFilters.forEach((filterItem) => {
        const moduleInfo = state.combinationFilters.find((item) => item.type === filterItem.type);
        const filterInfo = _getFilter(activeObject, filterItem.type);
        moduleInfo.status = !!filterInfo;
      });
    }
    update?.proxy?.$forceUpdate();
  }
};

onMounted(() => {
  canvasEditor.on('selectOne', handleSelectOne);
});

onBeforeUnmount(() => {
  canvasEditor.off('selectOne', handleSelectOne);
});

function getImageUrl(name) {
  return new URL(`../assets/filters/${name}.png`, import.meta.url).href;
}

function _changeAttr(type, key, value) {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  const itemFilter = _getFilter(activeObject, type);
  if (itemFilter) {
    itemFilter[key] = value;
  } else {
    const imgFilter = _createFilter(activeObject, type);
    imgFilter[key] = value;
  }
  activeObject.applyFilters();
  canvasEditor.canvas.renderAll();
}

function _changeAttrByHandler(moduleInfo) {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0];
  _removeFilter(activeObject, moduleInfo.type);
  const params = moduleInfo.params.map((item) => item.value);
  _createFilter(activeObject, moduleInfo.type, moduleInfo.handler(...params));
}

function _createFilter(sourceImg, type, options = null) {
  let filterObj;
  const fabricType = _getFabricFilterType(type);
  const ImageFilter = fabric.Image.filters[fabricType];
  if (ImageFilter) {
    filterObj = new ImageFilter(options);
    filterObj.options = options;
    sourceImg.filters.push(filterObj);
  }
  sourceImg.applyFilters();
  canvasEditor.canvas.renderAll();
  return filterObj;
}

function handleColorFilter() {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0]
  let filterObj
  const ImageFilter = fabric.Image.filters.BlendColor
  if (ImageFilter) {
    filterObj = new ImageFilter({
      color: pictureColor.value,
      mode: 'tint',
      alpha: 1
    })
    const record = new ImageFilter({
      color: pictureColor.value,
      mode: 'exclusion',
      alpha: 1
    })
    activeObject.filters = activeObject.filters.filter(item => item.mode !== 'tint' || item.mode !== 'exclusion' )
    activeObject.filters.push(record)
    activeObject.filters.push(filterObj)
  }
  activeObject.applyFilters()
  canvasEditor.canvas.renderAll()
}
function handleClearColor() {
  const activeObject = canvasEditor.canvas.getActiveObjects()[0]
  pictureColor.value = ''
  _removeFilter(activeObject, 'BlendColor')
}

function _getFilter(sourceImg, type) {
  let imgFilter = null;

  if (sourceImg) {
    const fabricType = _getFabricFilterType(type);
    const { length } = sourceImg.filters;
    let item, i;

    for (i = 0; i < length; i += 1) {
      item = sourceImg.filters[i];
      if (item.type === fabricType) {
        imgFilter = item;
        break;
      }
    }
  }

  return imgFilter;
}

function _removeFilter(sourceImg, type) {
  const fabricType = _getFabricFilterType(type);
  sourceImg.filters = sourceImg.filters.filter((value) => value.type !== fabricType);
  sourceImg.applyFilters();
  canvasEditor.canvas.renderAll();
}

function _getFabricFilterType(type) {
  return type.charAt(0).toUpperCase() + type.slice(1);
}
</script>

<style scoped lang="less">
.filter-box {
  overflow: hidden;
  .filter-item {
    float: left;
    cursor: pointer;
    width: 50%;
    margin-bottom: 10px;
    img {
      width: 90%;
      height: auto;
    }
  }
}
.has-params {
  display: inline-block;
  margin-bottom: 10px;
  width: 50%;
  .content {
    width: 90%;
  }
  cursor: none;
}
.box {
  margin-bottom: 12px;
}
.c-filter-color {
  display: flex;
  align-items: center;
  span {
    margin-right: 10px;
  }
}
.c-filter-color__text {
  cursor: pointer;
  color: #1890ff;
  margin-left: 10px;
}
</style>
