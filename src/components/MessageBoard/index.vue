<!-- file 留言板 -->
<template>
  <div class="c-message-board" v-if="currentId && !route.query.clampId && !isOuter">
    <Divider plain orientation="left">
      <h4>{{ $t('留言板') }}</h4>
    </Divider>
    <div class="c-message-board__title">当前文件(夹)名：<span class="c-message-board__title__color">{{ currentFileName }}</span></div>
    <div class="c-message-board__title">
      <div class="c-message-board__label">Title:</div>
      <Input v-model="record.title" placeholder="请输入标题" />
    </div>
    <div class="c-message-board__title">
      <div class="c-message-board__label">Tags:</div>
      <Input v-model="record.tags" placeholder="请输入Tags" />
    </div>
    <div class="c-message-board__input">
      <div class="c-message-board__label">备注:</div>
      <Input v-model="record.remark" type="textarea" :autosize="{minRows: 10,maxRows: 10}" placeholder="请输入留言" />
    </div>
    <div class="c-message-board__document">
      <div class="c-message-board__label">文档:</div>
      <div class="c-message-board__document__icon" v-for="item in documents" :key="item.id" @click="handlePreview(item)">
        <Icon class="c-message-close" type="md-close" @click.stop="handleRemoveDocument(item)" />
        <img :src="Document" alt="" />
        <div class="c-message-board__document__name" :title="item.name">{{ item.name }}</div>
      </div>
      <div class="c-message-board__document__icon" @click="handleAddDocument">
        <Icon class="c-message-add" type="md-add-circle" />
      </div>
    </div>
    <div class="c-message-board__input flex">
      <div class="c-message-board__label">已上架:</div>
      <Switch true-color="#13ce66" false-color="#ff4949" v-model="record.alreadyShelves"/>
    </div>
    <div class="c-message-board__upload">
      <div class="c-message-board__image" v-for="item in uploads.fileList" @click="handleCheckImage(item)">
        <Image :src="toWebp(item, 160, 160)" fit="cover" width="100%" height="100%" />
        <div class="c-message-board__image__cover">
          <Icon type="ios-eye-outline" @click.stop="handleView(item)"></Icon>
          <Icon type="ios-trash-outline" @click.stop="handleRemove(item)"></Icon>
          <Icon type="md-arrow-down" @click.stop="handleDownImage([item])" />
        </div>
        <Icon class="c-message-board__image__checked" type="ios-checkmark-circle" color="#409eff" v-show="downLoadImages.includes(item)" />
      </div>
      <Upload
        ref="upload"
        :show-upload-list="false"
        accept="image/*"
        :max-size="2048"
        :before-upload="handleBeforeUpload"
        :on-remove="handleRemove"
        multiple
        type="drag"
        action="/"
        style="display: inline-block;width:75px;">
        <div style="width: 75px;height:75px;line-height: 75px;">
          <Icon type="ios-camera" size="20"></Icon>
        </div>
      </Upload>
    </div>
    <div class="c-message-board__button">
      <Button size="small" type="primary" @click="handleDownImage(downLoadImages)" style="margin-right: 15px">批量下载</Button>
      <Button size="small" @click="handleSave" :loading="isLoading" style="margin-right: 10px">保存</Button>
    </div>
    <ImagePreview v-model="uploads.previewVisible" :preview-list="[uploads.previewImageUrl]" />
  </div>

  <!-- 弹层 -->
  <Modal
    v-model="editorVisible"
    title="文档"
    width="1100px"
    :mask-closable="false">
    <Wangeditor v-if="editorVisible" :document-id="documentId" ref="wangeditorRef" />
    <template #footer>
      <Button @click="() => editorVisible = false">取消</Button>
      <Button type="primary" :loading="isEditorLoading" @click="handleOk">确定</Button>
    </template>
  </Modal>
</template>

<script name="MessageBoard" setup>
import dayjs from 'dayjs'
import { Message, Modal } from 'view-ui-plus'
import { $bus, EVENT_NAME } from '@/utils/mitt'
import useSystemPermission from "@/hooks/permission";
import {
  addNotesV2,
  getNotesV2,
  editNotesV2,
  setDocumentV2,
  setRichTextV2,
  removeRichTextV2,
  getDocumentV2,
  getDownImage
} from '@/api/v2'
import { downloadFile } from './config'
import { getSrcFromHtml } from '@/utils/token'
import { useRoute } from 'vue-router'
import Wangeditor from '@/components/wangeditor'
import Document from '@/assets/icon/document.png'
import Loading from '@/components/load/index.js'
import { cos, toWebp, COS_URL } from '@/utils/cos'

const route = useRoute()
const record = reactive({
  remark: '',
  title: '',
  tags: '',
  alreadyShelves: true
})

const { isOuter } = useSystemPermission()

const currentFileName = ref('')
const currentId = ref('')
const isAdd = ref(true)

const uploads = reactive({
  onlineImg: [],
  fileList: [],
  removeList: [],
  previewVisible: false,
  previewImageUrl: ''
})

const isLoading = ref(false)
const handleSave = async () => {
  isLoading.value = true
  try {
    if (isAdd.value) {
      // 新增
      const { code } = await addNotesV2({
        ...record,
        fileId: currentId.value,
        images: uploads.fileList
      })
      if (code === 50000) throw new Error('保存失败')
      Message.success('保存成功')
    } else {
      // 编辑
      const { code }  = await editNotesV2({
        ...record,
        fileId: currentId.value,
        delImageUrls: uploads.removeList,
        addImageUrls: uploads.fileList.filter(url => !uploads.onlineImg.includes(url))
      })
      if (code === 50000) throw new Error('保存失败')
      Message.success('保存成功')
    }
    await handleSearch({ id: currentId.value, name: currentFileName.value })
  } catch (e) {
    Message.error('保存失败')
  } finally {
    isLoading.value = false
  }
}

const handleView = (url) => {
  uploads.previewImageUrl = COS_URL + url
  uploads.previewVisible = true
}
const handleRemove = (url) => {
  Modal.confirm({
    title: '提示',
    content: '是否删除该图片？',
    onOk: () => {
      uploads.fileList = uploads.fileList.filter(item => item !== url)
      uploads.removeList.push(url)
    }
  })
}

const handleRemoveDocument = (record) => {
  Modal.confirm({
    title: '提示',
    content: '是否删除该文档？',
    onOk: async () => {
      const data = await getDocumentV2(record.id)
      let delImgList = []
      if (data.data.docContent) {
        delImgList = getSrcFromHtml(data.data.docContent).map(src => 'doc/' + src.split('/').at(-1))
      }
      const result = await removeRichTextV2({
        id: record.id,
        delImgList
      })
      if (result.code === 0) {
        handleSearch({ id: currentId.value, name: currentFileName.value })
        return Message.success('删除成功')
      } else {
        return Message.error('删除失败')
      }
    }
  })
}

const isAddDocument = ref(false)
const documentId = ref('')
const handlePreview = (record) => {
  documentId.value = record.id
  isAddDocument.value = false
  editorVisible.value = true
}
const handleAddDocument = () => {
  documentId.value = ''
  isAddDocument.value = true
  editorVisible.value = true
}

const handleDownImage = async (urls) => {
  if (!urls.length) {
    return Message.error('请选择需要下载的图片')
  }
  if (urls.length === 1) {
    const url = urls[0]
    downloadFile(COS_URL + url, url.split('/').at(-1))
  } else {
    const result = await getDownImage({
      folderId: currentId.value,
      imgNames: urls
    })
    downloadFile(URL.createObjectURL(result), `${dayjs().format('YYYY_MM_DD_HH:mm:ss')}.zip`)
    downLoadImages.value = []
  }
}

const handleBeforeUpload = async (file) => {
  console.log(file.name)
  if (uploads.fileList.length > 14) return Message.error('至多只能上传15张图片')
  Loading.show('图片上传中')
  const { Location } = await cos.uploadFileToNotes(file, `${dayjs().format('YYYY-MM-DD_HH-mm-ss_Note')}_${Math.floor(Math.random() * 1000)}.png`)
  const urlArr = Location.split('/')
  urlArr.shift()
  uploads.fileList.push(urlArr.join('/'))
  Loading.hide()
}

const documents = ref([])
const handleSearch = async ({ id, name }) => {
  handleClear()
  if (!id) return
  currentFileName.value = name
  currentId.value = id
  const { data } = await getNotesV2(id)
  isAdd.value = !data.id
  if (!data) return
  record.remark = data.remark
  record.title = data.title
  record.tags = data.tags
  documents.value = data.docs
  record.alreadyShelves = data.alreadyShelves
  if (data.imgUrls) {
    uploads.fileList = [...data.imgUrls]
    uploads.onlineImg = [...data.imgUrls]
  }
}

const handleClear = () => {
  currentFileName.value = ''
  currentId.value = ''
  isAdd.value = true
  record.remark = ''
  record.title = ''
  record.tags = ''
  documents.value = []
  record.alreadyShelves = true
  uploads.fileList = []
  uploads.removeList = []
}

const downLoadImages = ref([])
const handleCheckImage = (url) => {
  if (downLoadImages.value.includes(url)) {
    downLoadImages.value = downLoadImages.value.filter(item => item !== url)
  } else {
    downLoadImages.value.push(url)
  }
}

const editorVisible = ref(false)
const wangeditorRef = ref(null)
const isEditorLoading = ref(false)
const handleOk = async () => {
  isEditorLoading.value = true
  const params = wangeditorRef.value.handleOk()
  const api = params.id ? setRichTextV2 : setDocumentV2
  if (!params.id) params.fileId = currentId.value
  try {
    const result = await api(params)
    if (result.code === 0) {
      Message.success(result.data)
    } else {
      throw new Error('出错了')
    }
    handleSearch({ id: currentId.value, name: currentFileName.value })
    editorVisible.value = false
  } catch (e) {
    Message.error('新增失败')
  } finally {
    isEditorLoading.value = false
  }
}

onMounted(() => {
  $bus.on(EVENT_NAME.CURRENT_ID, handleSearch)
  $bus.on(EVENT_NAME.CLEAR_DOCUMENT, handleClear)
})
onUnmounted(() => {
  $bus.off(EVENT_NAME.CURRENT_ID, handleSearch)
  $bus.off(EVENT_NAME.CLEAR_DOCUMENT, handleClear)
})
</script>



<style lang="less">
.c-message-board__button {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
.c-message-board__input {
  margin-bottom: 20px;
}
.c-message-board__image {
  display: inline-block;
  width: 80px;
  height: 80px;
  text-align: center;
  line-height: 80px;
  border-radius: 4px;
  background: #fff;
  position: relative;
  cursor: pointer;
  box-shadow: 0 1px 1px rgba(0,0,0,.2);
  margin-right: 10px;
  margin-bottom: 25px;
  .ivu-image {
    overflow: hidden;
  }
}
.c-message-board__image__checked {
  position: absolute;
  left: 50%;
  bottom: -20px;
  transform: translateX(-50%);
}
.c-message-board__image__cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0,0,0,.6);
}
.c-message-board__image:hover .c-message-board__image__cover {
  display: block;
}
.c-message-board__image__cover i {
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  margin: 0 2px;
}
.c-message-board__title {
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 15px;
}
.c-message-board__label {
  margin-bottom: 10px;
  font-weight: 500;
  font-size: 16px;
  white-space: nowrap;
}
.flex {
  display: flex;
  align-items: center;
  .c-message-board__label {
    margin-bottom: 0;
    margin-right: 10px;
  }
}
.c-message-board__title__color {
  color: #0068ff;
}
.c-message-board__document__icon {
  display: inline-block;
  width: 80px;
  height: 80px;
  background-color: #ffffff;
  box-shadow: 0 0 10px rgba(0,0,0,.2);
  border-radius: 6px;
  position: relative;
  margin-right: 10px;
  cursor: pointer;
  .c-message-close {
    position: absolute;
    right: 2px;
    top: 2px;
    cursor: pointer;
  }
  .c-message-add {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  img {
    margin: 5px auto 0;
    display: block;
    width: 60px;
    height: 70px;
  }
}
.c-message-board__document {
  margin-bottom: 15px;
}
.c-message-board__document__name {
  position: absolute;
  text-align: center;
  left: 50%;
  bottom: -20px;
  transform: translateX(-50%);
  white-space: nowrap;
  width: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
