function getObjectURL(file: File) {
  let url = null ;
  if (window.URL !== undefined) {
    url = window.URL.createObjectURL(file) ;
  } else if (window.webkitURL !== undefined) {
    url = window.webkitURL.createObjectURL(file);
  }
  return url ;
}

function downloadFile(link: string, filename: string){
  const a = document.createElement('a')
  a.href = link
  a.download = filename || 'default.png'
  a.click()
}

export {
  getObjectURL,
  downloadFile
}
