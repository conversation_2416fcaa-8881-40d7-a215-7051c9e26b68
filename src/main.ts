import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import ViewUiPlus from 'view-ui-plus';
import 'view-ui-plus/dist/styles/viewuiplus.css';
import './styles/index.less';
import VueLazyLoad from 'vue3-lazyload';
import '@/assets/fonts/font.css';
import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css';
import ContextMenu from '@imengyu/vue3-context-menu';
import Cropper from 'vue3-cropper'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import 'vue3-cropper/lib/vue3-cropper.css'
import vSelect from 'vue-select'
import { RecycleScroller } from 'vue-virtual-scroller'

import { VueMasonryPlugin } from 'vue-masonry';

import i18n from './language/index';
import { useTheme } from './hooks/useTheme';

async function bootstrap() {
  // 初始化主题
  const { theme } = useTheme();
  const app = createApp(App);
  app.use(Cropper);
  app.use(VueMasonryPlugin);
  app.use(ContextMenu);
  app.use(router);
  app.use(i18n);
  app.use(VueLazyLoad, {});
  app.use(ViewUiPlus);
  app.component('RecycleScroller', RecycleScroller)
  app.component('v-select', vSelect)
  await router.isReady();
  app.mount('#app');
}
bootstrap();
