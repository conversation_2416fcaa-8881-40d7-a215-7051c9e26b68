import type { RouteRecordRaw } from 'vue-router';

import { setToken, autoLogin, logout } from '@/api/user';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    beforeEnter: async (to) => {
      if (to.query.username && to.query.key) {
        const res = await autoLogin({
          username: to.query.username,
          projectid: to.query.projectid,
          key: to.query.key,
        });
        if (res.data.jwt) {
          setToken(res.data.jwt);
        } else {
          logout();
          alert('签名失败');
          window.location.href = '/';
        }
      }
      return true;
    },
    name: 'home',
    component: () => import('@/views/home/<USER>'),
  },
  {
    path: '/user',
    component: () => import('@/views/user/index.vue'),
  },
  {
    path: '/system',
    component: () => import('@/views/system/index.vue'),
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/v2/index.vue')
  }
];

export default routes;
