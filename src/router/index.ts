import { createRouter, createWebHashHistory } from 'vue-router';
import routes from './routes';

const router = createRouter({
  routes,
  history: createWebHashHistory(),
  scrollBehavior() {
    return { top: 0 };
  },
});

router.beforeEach((to, form, next) => {
  const java_token = localStorage.getItem('java_token')
  if (!java_token && to.path !== '/login') {
    next('/login')
  } else {
    next()
  }
})
export default router
