import CryptoJS from 'crypto-js'

const KEY = CryptoJS.enc.Utf8.parse('xpzefsiqWevqQve9')
const IV = CryptoJS.enc.Utf8.parse('tevH723bnacefm46')

/**
 * AES加密 ：字符串 key iv  返回base64
 */
export function Encrypt(word: any, keyStr: any, ivStr: any) {
  let key = KEY;
  let iv = IV;
  if (keyStr) {
    key = CryptoJS.enc.Utf8.parse(keyStr);
    iv = CryptoJS.enc.Utf8.parse(ivStr);
  }
  const srcs = CryptoJS.enc.Utf8.parse(word);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.ZeroPadding
  });
  return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
}

// AES解密
export function getDAesString(word: any, keyStr: any, ivStr: any) {
  let key = KEY;
  let iv = IV;
  if (keyStr) {
    key = CryptoJS.enc.Utf8.parse(keyStr);
    iv = CryptoJS.enc.Utf8.parse(ivStr);
  }
  const decrypted =CryptoJS.AES.decrypt(word, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.ZeroPadding
  })
  return decrypted.toString(CryptoJS.enc.Utf8)
}
