// 主题变量定义
:root {
  // 亮色主题 - 现代化配色
  --bg-color: #ffffff;           // 纯白背景
  --bg-color-secondary: #f6f8fa; // 次要背景，GitHub 风格的浅灰
  --text-color: #24292f;         // 主要文字颜色，深灰而非纯黑
  --text-color-secondary: #656d76; // 次要文字颜色，中性灰
  --border-color: #d0d7de;       // 边框颜色，柔和的灰色
  --border-color-light: #f6f8fa; // 浅边框颜色
  --shadow-color: rgba(31, 35, 40, 0.04); // 阴影颜色，更自然
  --header-bg: #ffffff;          // 头部背景
  --sidebar-bg: #ffffff;         // 侧边栏背景
  --canvas-bg: #f1f1f1;          // 画布背景，保持原来的浅灰色
  --menu-active-bg: #f6f8fa;     // 菜单激活背景
  --button-bg: #f6f8fa;          // 按钮背景
  --button-hover-bg: #f3f4f6;    // 按钮悬停背景
  --input-bg: #ffffff;           // 输入框背景
  --modal-bg: #ffffff;           // 模态框背景
  --tooltip-bg: #24292f;         // 提示框背景，深色
  --scrollbar-bg: #f6f8fa;       // 滚动条背景
  --scrollbar-thumb: #d0d7de;    // 滚动条滑块
  --file-type-bg: rgb(243, 245, 249);        // 文件夹背景
  --img-font-bg: #f4f4f4;        // 图文混排背景
}

// 暗色主题 - 参考 Vite 官网和现代设计系统
[data-theme="dark"] {
  --bg-color: #0d1117;           // GitHub 暗色背景，深蓝灰色
  --bg-color-secondary: #161b22; // 次要背景色，稍微亮一点
  --text-color: #f0f6fc;         // 主要文字颜色，柔和的白色
  --text-color-secondary: #8b949e; // 次要文字颜色，中性灰
  --border-color: #30363d;       // 边框颜色，温和的灰色
  --border-color-light: #21262d; // 浅边框颜色
  --shadow-color: rgba(0, 0, 0, 0.4); // 阴影颜色
  --header-bg: #161b22;          // 头部背景，与次要背景一致
  --sidebar-bg: #0d1117;         // 侧边栏背景，与主背景一致
  --canvas-bg: #f1f1f1;          // 画布背景，保持原来的浅灰色，不跟随主题
  --menu-active-bg: #21262d;     // 菜单激活背景，温和的高亮
  --button-bg: #21262d;          // 按钮背景
  --button-hover-bg: #30363d;    // 按钮悬停背景，更明显的高亮
  --input-bg: #0d1117;           // 输入框背景，与主背景一致
  --modal-bg: #161b22;           // 模态框背景
  --tooltip-bg: #21262d;         // 提示框背景
  --scrollbar-bg: #161b22;       // 滚动条背景
  --scrollbar-thumb: #484f58;    // 滚动条滑块，适中的对比度
  --file-type-bg: transparent;        // 文件夹背景
  --img-font-bg: #72757b;        // 图文混排背景
}

// 主题切换过渡动画
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

.ivu-menu-vertical.ivu-menu-light:after {
  background: var(--border-color) !important;
}

.ivu-modal-header p, .ivu-modal-header-inner {
  color: var(--text-color);
}

.ivu-message-error {
  color: red;
}