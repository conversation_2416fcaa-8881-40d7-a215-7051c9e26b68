@import '~view-ui-plus/src/styles/index.less';
@import './resetViewUi.less';
@ionicons-font-path: '~view-ui-plus/src/styles/common/iconfont/fonts';

// 主题相关的全局样式覆盖
[data-theme="dark"] {
  // ViewUI Plus 组件暗色主题覆盖
  .ivu-layout {
    background: var(--bg-color) !important;
  }

  .ivu-layout-content {
    background: var(--bg-color) !important;
  }

  .ivu-menu-light {
    background: var(--sidebar-bg) !important;
    border-right: 1px solid var(--border-color) !important;

    .ivu-menu-item {
      color: var(--text-color) !important;

      &:hover {
        background: var(--menu-active-bg) !important;
      }

      &.ivu-menu-item-active {
        background: var(--menu-active-bg) !important;
        color: var(--text-color) !important;
      }
    }
  }

  .ivu-btn {
    background: var(--button-bg) !important;
    border-color: var(--border-color) !important;
    color: var(--text-color) !important;

    &:hover {
      background: var(--button-hover-bg) !important;
    }
  }

  .ivu-btn-text {
    background: transparent !important;
    color: var(--text-color) !important;

    &:hover {
      background: var(--button-hover-bg) !important;
    }
  }

  .ivu-modal {
    .ivu-modal-content {
      background: var(--modal-bg) !important;
    }

    .ivu-modal-header {
      background: var(--modal-bg) !important;
      border-bottom: 1px solid var(--border-color) !important;
      color: var(--text-color) !important;
    }

    .ivu-modal-body {
      background: var(--modal-bg) !important;
      color: var(--text-color) !important;
    }

    .ivu-modal-confirm-head-title {
      color: var(--text-color) !important;
    }

    .ivu-modal-confirm-body {
      color: var(--text-color) !important;
    }
  }

  .ivu-input {
    background: var(--input-bg) !important;
    border-color: var(--border-color) !important;
    color: var(--text-color) !important;
  }

  .ivu-input-number {
    .ivu-input-number-input {
      background: var(--input-bg) !important;
      border-color: var(--border-color) !important;
      color: var(--text-color) !important;
    }

    .ivu-input-number-handler {
      background: var(--button-bg) !important;
      border-color: var(--border-color) !important;
      color: var(--text-color) !important;

      &:hover {
        background: var(--button-hover-bg) !important;
      }
    }
  }

  .ivu-select {
    .ivu-select-selection {
      background: var(--input-bg) !important;
      border-color: var(--border-color) !important;
      color: var(--text-color) !important;
    }
  }

  .ivu-form {
    .ivu-form-item-label {
      color: var(--text-color) !important;
    }

    .ivu-form-item-content {
      color: var(--text-color) !important;
    }
  }

  .ivu-radio-group-button {
    .ivu-radio-wrapper {
      background: var(--button-bg) !important;
      border-color: var(--border-color) !important;
      color: var(--text-color) !important;

      &:hover {
        background: var(--button-hover-bg) !important;
      }

      &.ivu-radio-wrapper-checked {
        background: var(--menu-active-bg) !important;
        border-color: var(--border-color) !important;
        color: var(--text-color) !important;
      }

      &.ivu-radio-wrapper-disabled {
        background: var(--bg-color-secondary) !important;
        border-color: var(--border-color-light) !important;
        color: var(--text-color-secondary) !important;

        &.ivu-radio-wrapper-checked {
          background: var(--bg-color-secondary) !important;
          border-color: var(--border-color-light) !important;
          color: var(--text-color-secondary) !important;
        }
      }
    }
  }

  .ivu-divider-horizontal {
    &.ivu-divider-with-text-center,
    &.ivu-divider-with-text-left,
    &.ivu-divider-with-text-right {
      &:before,
      &:after {
        border-top: 1px solid var(--border-color-light) !important;
      }
    }

    // 普通分割线
    &:not(.ivu-divider-with-text-center):not(.ivu-divider-with-text-left):not(.ivu-divider-with-text-right) {
      border-top: 1px solid var(--border-color-light) !important;
    }
  }

  .ivu-divider-vertical {
    border-left: 1px solid var(--border-color-light) !important;
  }

  .ivu-divider-inner-text {
    color: var(--text-color-secondary) !important;
  }

  .ivu-steps {
    .ivu-steps-main {
      color: var(--text-color) !important;
    }

    .ivu-steps-title {
      color: var(--text-color) !important;
    }

    .ivu-steps-content {
      color: var(--text-color-secondary) !important;
    }

    .ivu-steps-tail {
      &:after {
        background: var(--border-color) !important;
      }
    }

    .ivu-steps-head {
      background: var(--bg-color) !important;

      .ivu-steps-head-inner {
        background: var(--bg-color) !important;
        border-color: var(--border-color) !important;
        color: var(--text-color-secondary) !important;
      }
    }

    .ivu-steps-title {
      background: var(--bg-color) !important;
    }

    // 完成状态
    &.ivu-steps-status-finish {
      .ivu-steps-head-inner {
        background: var(--menu-active-bg) !important;
        border-color: var(--border-color) !important;
        color: var(--text-color) !important;
      }

      .ivu-steps-title {
        color: var(--text-color) !important;
      }
    }

    // 进行中状态
    &.ivu-steps-status-process {
      .ivu-steps-head-inner {
        background: var(--menu-active-bg) !important;
        border-color: var(--border-color) !important;
        color: var(--text-color) !important;
      }

      .ivu-steps-title {
        color: var(--text-color) !important;
      }
    }

    // 等待状态
    &.ivu-steps-status-wait {
      .ivu-steps-title {
        color: var(--text-color-secondary) !important;
      }
    }

    // 错误状态
    &.ivu-steps-status-error {
      .ivu-steps-title {
        color: var(--text-color) !important;
      }
    }
  }

  .tool-box {
    span {
      color: var(--text-color) !important;
      background-color: var(--button-bg) !important;

      &:hover {
        color: var(--text-color) !important;
        background-color: var(--button-hover-bg) !important;
        svg path {
          fill: #3f89e9;
        }
      }

      &.active {
        background-color: var(--menu-active-bg) !important;
        color: var(--text-color) !important;
      }
    }
  }
}

// 全局主题样式
body {
  background-color: var(--bg-color);
  color: var(--text-color);
}

div.attr-item-box {
  h3 {
    padding-bottom: 10px;
    color: var(--text-color);
  }

  .ivu-tooltip {
    flex: 1;
  }
  .ivu-tooltip-rel {
    width: 100%;
  }

  .bg-item {
    width: 100%;
    padding: 5px;
    display: flex;
    flex: 1;
    justify-content: space-between;
    border-radius: 5px;
    background: var(--bg-color-secondary);
  }
}
