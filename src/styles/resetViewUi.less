.ivu-btn:focus {
  z-index: 2;
  -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
}
.preview-modal-wrap {
  overflow: hidden;
  .ivu-modal-body {
    display: flex;
    padding: 0;
    max-height: calc(100vh - 51px);
    overflow: overlay;
    &::-webkit-scrollbar {
      width: 4px;
      background-color: #efeae6;
    }
    &:hover ::-webkit-scrollbar-track-piece {
      background-color: #fff;
      border-radius: 6px;
    }
    &:hover::-webkit-scrollbar-thumb:hover {
      background-color: #c1c1c1;
    }
    &:hover::-webkit-scrollbar-thumb:vertical {
      background-color: #c1c1c1;
      border-radius: 6px;
      outline: 2px solid #c1c1c1;
      outline-offset: -2px;
      border: 2px solid #c1c1c1;
    }
  }
  ivu-modal-content {
    border-radius: 6px 6px 0 0;
  }
  .ivu-modal {
    top: 0;
  }
}
