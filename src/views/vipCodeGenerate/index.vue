<template>
  <div class="home">
    <Layout>
      <Header :style="{ position: 'fixed', width: '100%', zIndex: 99 }">
        <div class="left">
          <logo></logo>
          <Divider type="vertical" />
          在线设计工具
        </div>
      </Header>

      <Content :style="{ margin: '40px 20px 0', minHeight: '500px' }">
        <div class="form-box">
          <Card :bordered="false">
            <template #title>会员码生成</template>
            <Form
              ref="formRef"
              v-show="!vipCode"
              :model="formInline"
              :rules="ruleInline"
              :label-width="80"
            >
              <FormItem prop="username" label="用户名">
                <Input type="text" v-model="formInline.username" placeholder="用户名">
                  <template #prepend>
                    <Icon type="ios-person-outline"></Icon>
                  </template>
                </Input>
              </FormItem>
              <FormItem prop="password" label="密码">
                <Input type="password" v-model="formInline.password" placeholder="密码">
                  <template #prepend>
                    <Icon type="ios-lock-outline"></Icon>
                  </template>
                </Input>
              </FormItem>
              <FormItem prop="type" label="会员类型">
                <Select v-model="formInline.type">
                  <Option v-for="item in typeList" :value="item.value" :key="item.value">
                    {{ item.label }}
                  </Option>
                </Select>
              </FormItem>
              <FormItem prop="days" label="订阅类型">
                <Select v-model="formInline.days">
                  <Option v-for="item in dayList" :value="item.value" :key="item.value + '--days'">
                    {{ item.label }}
                  </Option>
                </Select>
              </FormItem>
              <FormItem>
                <Button type="primary" @click="handleSubmit">生成</Button>
              </FormItem>
            </Form>

            <Result type="success" title="生成成功" v-if="vipCode" style="padding-bottom: 40px">
              <template #desc>
                {{ tipText }}
              </template>
              <template #extra>会员码：{{ vipCode }}</template>
              <template #actions>
                <Button type="primary" @click="copyVipCode">复制会员码</Button>
                <Button @click="copyTipText">复制提示语</Button>
              </template>
            </Result>
          </Card>
        </div>
      </Content>
    </Layout>
  </div>
</template>

<script name="VipCodeGenerate" setup>
import logo from '@/components/logo.vue';
import { Message, Copy } from 'view-ui-plus';
import { generateVipCode } from '@/api/material';
const typeList = ref([
  {
    label: '批量生成',
    value: 'batchimg',
  },
  {
    label: '批量抠图',
    value: 'removebg',
  },
]);

const dayList = ref([
  {
    label: '1个月',
    value: '30',
  },
  {
    label: '1个季度',
    value: '90',
  },
  {
    label: '半年',
    value: '180',
  },
  {
    label: '1年',
    value: '365',
  },
]);

const formInline = ref({
  username: '',
  password: '',
  type: '',
  days: '',
});

const ruleInline = reactive({
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'blur' }],
  days: [{ required: true, message: '请选择天数', trigger: 'blur' }],
});

const formRef = ref(null);
const vipCode = ref('');
const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      generateVipCode({ ...formInline.value }).then((res) => {
        if (res?.data?.data) {
          console.log(res.data, 2222);
          vipCode.value = res.data?.data.code;
        } else {
          Message.error(res.data?.msg);
        }
      });
    } else {
      Message.error('请检查表单项');
    }
  });
};

const copyVipCode = () => {
  Copy({
    text: vipCode.value,
  });
};

const tipText =
  '感谢您使用本系统，请复制并保留会员码，在系统中绑定会员码即可，从绑定当日起开始生效。';
const copyTipText = () => {
  Copy({
    text: tipText,
  });
};
</script>
<style lang="less" scoped>
:deep(.ivu-layout-header) {
  --height: 45px;
  padding: 0 0px;
  border-bottom: 1px solid #eef2f8;
  background: #fff;
  height: var(--height);
  line-height: var(--height);
  display: flex;
  justify-content: space-between;

  .left {
    display: flex;
    align-items: center;
    img {
      display: block;
      margin-right: 10px;
    }
  }
}

.layout-footer-center {
  text-align: center;
}

.form-box {
  padding: 20px 0;
}
</style>
