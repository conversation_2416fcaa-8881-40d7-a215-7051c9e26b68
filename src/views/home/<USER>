<template>
  <div class="home">
    <Layout>
      <Header v-if="state.show">
        <div class="left">
          <Divider type="vertical" />
          <import-file></import-file>
          <Divider type="vertical" />
          <myTemplName></myTemplName>
          <history></history>
        </div>

        <div class="right">
          <admin />
          <previewCurrent @mode="val => isFileMode = val" />
          <save></save>
        </div>
      </Header>
      <Content style="display: flex; height: calc(100vh - 64px)">
        <div v-if="state.show" :class="`left-bar ${state.toolsBarShow && 'show-tools-bar'} ${ isFileMode && 'is-full-screen' }`">
          <Menu :active-name="menuActive" accordion @on-select="showToolsBar" width="65px">
            <MenuItem v-for="item in renderLeftBar" :key="item.key" :name="item.key" class="menu-item">
              <template v-if="item.icon">
                <Icon :type="item.icon" size="24" />
              </template>
              <template v-else>
                <svg t="1734144114572" class="c-poker-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4364" width="24" height="24"><path d="M178.176 303.232l42.24-5.888 7.36 52.8-42.24 5.888-7.36-52.8z" p-id="4365" fill="#525a6c"></path><path d="M920.96 227.733333l-152.96-21.333333V181.333333a21.333333 21.333333 0 0 0-21.333333-21.333333H277.333333a21.333333 21.333333 0 0 0-21.333333 21.333333v25.066667l-152.96 21.333333a21.333333 21.333333 0 0 0-17.706667 24.106667L176.106667 906.666667a21.333333 21.333333 0 0 0 21.333333 18.453333h2.986667L512 881.6l311.893333 43.413333h2.88a21.333333 21.333333 0 0 0 21.333334-18.453333l91.306666-654.933333a21.333333 21.333333 0 0 0-18.453333-23.893334zM298.666667 202.666667h426.666666v618.666666H298.666667v-618.666666z m-168.64 64L256 249.493333V842.666667a21.333333 21.333333 0 0 0 21.333333 21.333333h51.52l-113.493333 15.786667-85.333333-613.12z m678.613333 613.12l-113.493333-15.786667H746.666667a21.333333 21.333333 0 0 0 21.333333-21.333333V249.493333l125.973333 17.173334-85.333333 613.12z" p-id="4366" fill="#525a6c"></path><path d="M796.373333 350.805333l7.36-52.8 42.24 5.888-7.36 52.8-42.24-5.888z m-200.746666 85.461334a78.4 78.4 0 0 0-156.586667 0 78.506667 78.506667 0 0 0 31.253333 150.4c8.746667-0.042667 17.429333-1.536 25.706667-4.373334v52.266667h42.666667v-52.266667c8.277333 2.837333 16.96 4.330667 25.706666 4.373334a78.506667 78.506667 0 0 0 31.253334-150.4z m-31.253334 107.733333a35.946667 35.946667 0 0 1-29.546666-15.573333 21.333333 21.333333 0 0 0-34.986667 0 35.733333 35.733333 0 1 1-35.2-55.466667 21.333333 21.333333 0 0 0 17.6-24.96 35.733333 35.733333 0 1 1 70.186667 0 21.333333 21.333333 0 0 0 17.6 25.066667 35.733333 35.733333 0 0 1-5.653334 70.933333zM341.333333 245.333333h42.666667V298.666667h-42.666667v-53.333334zM640 725.333333h42.666667v53.333334h-42.666667V725.333333z" p-id="4367" fill="#525a6c"></path></svg>
              </template>
              <div>{{ item.name }}</div>
            </MenuItem>
          </Menu>
          <div class="content" v-show="state.toolsBarShow">
            <div class="left-panel">
              <KeepAlive>
                <component :is="leftBarComponent[menuActive]"></component>
              </KeepAlive>
            </div>
          </div>
          <div
            :class="`close-btn left-btn ${state.toolsBarShow && 'left-btn-open'}`"
            @click="hideToolsBar"
          ></div>
        </div>

        <div id="workspace" :class="{'c-file-mode': isFileMode}">
          <div class="canvas-box">
            <div class="inside-shadow"></div>
            <canvas id="canvas" :class="state.ruler ? 'design-stage-grid' : ''"></canvas>
            <dragMode v-if="state.show"></dragMode>
            <zoom></zoom>
          </div>
        </div>

        <div class="right-bar" :class="{'c-file-mode': isFileMode}" v-show="state.attrBarShow">
          <div v-if="state.show" style="padding-top: 10px">
            <div v-show="!mixinState.mSelectMode">
              <set-size></set-size>
              <bg-bar></bg-bar>
            </div>

            <div v-show="mixinState.mSelectMode === 'multiple'">
              <group v-if="!route.query.clampId"></group>
              <align></align>
              <center-align></center-align>
              <GroupImg />
            </div>

            <ClampTemplate />

            <div v-show="mixinState.mSelectMode === 'one'" class="attr-item-box">
              <group></group>
              <Divider v-if="!route.query.clampId" plain orientation="left">
                <h4>快捷操作</h4>
              </Divider>
              <div class="bg-item" v-if="!route.query.clampId" v-show="mixinState.mSelectMode">
                <lock></lock>
                <dele></dele>
                <clone></clone>
                <hide></hide>
                <edit></edit>
                <PictureText />
              </div>
              <center-align></center-align>
              <replaceImg></replaceImg>
              <clip-image @clip="handleClip"></clip-image>
              <flip></flip>
              <attributeBarcode></attributeBarcode>
              <attributeQrCode></attributeQrCode>
              <ImgSize />
<!--              <Fixture />-->
              <filters></filters>
              <imgStroke />
              <attributeColor></attributeColor>
              <attributeBorder></attributeBorder>
              <attributeFont></attributeFont>
              <attributeTextFloat></attributeTextFloat>
              <attribute-text-content></attribute-text-content>
              <attributeCurvedText></attributeCurvedText>
              <attributePostion></attributePostion>
              <attributeShadow></attributeShadow>
              <attributeRounded></attributeRounded>
            </div>

            <div v-if="isCilp">
              <Clip :record="currentClipRecord" @cancel="() => isCilp = false" />
            </div>

            <MessageBoard v-show="!mixinState.mSelectMode" />
          </div>
        </div>
        <div
          :class="`close-btn right-btn ${state.attrBarShow && 'right-btn-open'} ${isFileMode && 'c-file-mode'}`"
          @click="switchAttrBar"
        ></div>
      </Content>
    </Layout>
  </div>
</template>

<script name="Home" setup>
import { useRoute, useRouter } from 'vue-router';
import { nextTick } from 'vue';
import { checkLogin } from '@/api/v2'
import { initCos } from '@/utils/cos'
import useSystemPermission from '@/hooks/permission'
const route = useRoute();
const router = useRouter();

const { isOuter, isAdmin } = useSystemPermission()

const importFile = defineAsyncComponent(() => import('@/components/importFile.vue'));
const MessageBoard = defineAsyncComponent(() => import('@/components/MessageBoard/index.vue'));
const myTemplName = defineAsyncComponent(() => import('@/components/myTemplName.vue'));
const fontStyle = defineAsyncComponent(() => import('@/components/fontStyle.vue'));
const myMaterial = defineAsyncComponent(() => import('@/components/myMaterial/index.vue'));
const tools = defineAsyncComponent(() => import('@/components/tools.vue'));
const layer = defineAsyncComponent(() => import('@/components/layer.vue'));
const material = defineAsyncComponent(() => import('@/components/material.vue'));
const batch = defineAsyncComponent(() => import('@/components/batch/index.vue'));
const PictureText = defineAsyncComponent(() => import('@/components/pictureText'));
const admin = defineAsyncComponent(() => import('@/components/admin.vue'));
const previewCurrent = defineAsyncComponent(() => import('@/components/previewCurrent'));
const save = defineAsyncComponent(() => import('@/components/save.vue'));
const ImgSize = defineAsyncComponent(() => import('@/components/size'));
const GroupImg = defineAsyncComponent(() => import('@/components/group/GroupImg.vue'));
// const Fixture = defineAsyncComponent(() => import('@/components/fixture'));
const clamp = defineAsyncComponent(() => import('@/components/clamp/index.vue'));
const poker = defineAsyncComponent(() => import('@/components/poker/index.vue'));
const ClampTemplate = defineAsyncComponent(() => import('@/components/clamp/ClampTemplate.vue'));

import align from '@/components/align.vue';
import centerAlign from '@/components/centerAlign.vue';
import flip from '@/components/flip.vue';
import clone from '@/components/clone.vue';
import hide from '@/components/hide.vue';
import group from '@/components/group.vue';
import zoom from '@/components/zoom.vue';
import dragMode from '@/components/dragMode.vue';
import lock from '@/components/lock.vue';
import dele from '@/components/del.vue';
import history from '@/components/history.vue';
import bgBar from '@/components/bgBar.vue';
import setSize from '@/components/setSize.vue';
import replaceImg from '@/components/replaceImg.vue';
import filters from '@/components/filters.vue';
import imgStroke from '@/components/imgStroke.vue';
import attributePostion from '@/components/attributePostion.vue';
import attributeCurvedText from '@/components/attributeCurvedText.vue';
import attributeShadow from '@/components/attributeShadow.vue';
import attributeBorder from '@/components/attributeBorder.vue';
import attributeRounded from '@/components/attributeRounded.vue';
import attributeFont from '@/components/attributeFont.vue';
import attributeTextFloat from '@/components/attributeTextFloat.vue';
import attributeColor from '@/components/attributeColor.vue';
import attributeBarcode from '@/components/attributeBarcode.vue';
import attributeQrCode from '@/components/attributeQrCode.vue';
import Clip from '@/components/Cilp.vue';

import { fabric } from 'fabric';

if (fabric.isWebglSupported()) {
  fabric.textureSize = fabric.maxTextureSize
}

import useSelectListen from '@/hooks/useSelectListen';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const isCilp = ref(false)
const currentClipRecord = ref(null)
const handleClip = (record) => {
  isCilp.value = true
  currentClipRecord.value = record
}

const APIHOST = import.meta.env.APP_APIHOST;

import Editor, {
  AddBaseTypePlugin,
  DringPlugin,
  AlignGuidLinePlugin,
  ControlsPlugin,
  CenterAlignPlugin,
  LayerPlugin,
  CopyPlugin,
  MoveHotKeyPlugin,
  DeleteHotKeyPlugin,
  GroupPlugin,
  DrawLinePlugin,
  GroupTextEditorPlugin,
  GroupAlignPlugin,
  WorkspacePlugin,
  HistoryPlugin,
  FlipPlugin,
  RulerPlugin,
  MaterialPlugin,
  WaterMarkPlugin,
  FontPlugin,
  PolygonModifyPlugin,
  DrawPolygonPlugin,
  FreeDrawPlugin,
  PathTextPlugin,
  PsdPlugin,
  SimpleClipImagePlugin,
  BarCodePlugin,
  QrCodePlugin,
  ImageStroke,
  BatchPlugin,
  EmitPlugin,
  ResizePlugin,
} from '@kuaitu/core';
import Edit from '@/components/edit.vue';
import ClipImage from '@/components/clipImage.vue';
import AttributeTextContent from '@/components/attributeTextContent.vue';

const canvasEditor = new Editor();

const state = reactive({
  menuActive: 1,
  show: false,
  toolsBarShow: true,
  attrBarShow: true,
  select: null,
  ruler: true,
});

const menuActive = ref('myMaterial');
const leftBarComponent = {
  tools,
  material,
  fontStyle,
  layer,
  myMaterial,
  batch,
  clamp,
  poker
};

const leftBar = reactive([
  {
    key: 'myMaterial',
    name: computed(() => t('mine')),
    icon: 'md-book',
    isShow: true
  },
  {
    key: 'tools',
    name: computed(() => t('elements')),
    icon: 'ios-leaf-outline',
    isShow: !isOuter.value
  },
  {
    key: 'fontStyle',
    name: computed(() => t('font_style')),
    icon: 'ios-pulse',
    isShow: !isOuter.value
  },
  {
    key: 'material',
    name: computed(() => t('material.cartoon')),
    icon: 'md-images',
    isShow: !isOuter.value
  },
  {
    key: 'layer',
    name: computed(() => t('layers')),
    isShow: true,
    icon: 'md-reorder',
  },
  {
    key: 'batch',
    name: computed(() => t('batch')),
    icon: 'ios-infinite',
    isShow: !isOuter.value
  },
  {
    key: 'poker',
    name: computed(() => t('扑克牌')),
    isShow: !isOuter.value
  },
  {
    key: 'clamp',
    name: computed(() => t('夹具')),
    isShow: !isOuter.value,
    icon: 'ios-albums'
  },
  {
    key: 'system',
    name: computed(() => t('权限')),
    isShow: isAdmin,
    icon: 'md-key'
  }
]);

const renderLeftBar = computed(() => {
  return leftBar.filter(item => item.isShow)
})

onMounted(() => {
  const canvas = new fabric.Canvas('canvas', {
    fireRightClick: true, // 启用右键，button的数字为3
    stopContextMenu: true, // 禁止默认右键菜单
    controlsAboveOverlay: true, // 超出clipPath后仍然展示控制条
    preserveObjectStacking: true, // 当选择画布中的对象时，让对象不在顶层。
  });

  canvasEditor.init(canvas);
  canvasEditor.use(DringPlugin);
  canvasEditor.use(PolygonModifyPlugin);
  canvasEditor.use(AlignGuidLinePlugin);
  canvasEditor.use(ControlsPlugin);
  canvasEditor.use(AddBaseTypePlugin);
  canvasEditor.use(CenterAlignPlugin);
  canvasEditor.use(LayerPlugin);
  canvasEditor.use(CopyPlugin);
  canvasEditor.use(MoveHotKeyPlugin);
  canvasEditor.use(DeleteHotKeyPlugin);
  canvasEditor.use(GroupPlugin);
  canvasEditor.use(DrawLinePlugin);
  canvasEditor.use(GroupTextEditorPlugin);
  canvasEditor.use(GroupAlignPlugin);
  canvasEditor.use(WorkspacePlugin);
  canvasEditor.use(HistoryPlugin);
  canvasEditor.use(FlipPlugin);
  canvasEditor.use(RulerPlugin);
  canvasEditor.use(DrawPolygonPlugin);
  canvasEditor.use(FreeDrawPlugin);
  canvasEditor.use(PathTextPlugin);
  canvasEditor.use(SimpleClipImagePlugin);
  canvasEditor.use(BarCodePlugin);
  canvasEditor.use(QrCodePlugin);
  canvasEditor.use(BatchPlugin);
  canvasEditor.use(EmitPlugin);
  canvasEditor.use(ResizePlugin);
  canvasEditor.use(FontPlugin, {
    repoSrc: APIHOST,
  });
  canvasEditor.use(MaterialPlugin, {
    repoSrc: APIHOST,
  });
  canvasEditor.use(WaterMarkPlugin);
  canvasEditor.use(PsdPlugin);
  canvasEditor.use(ImageStroke);

  state.show = true;
  if (state.ruler) {
    // 延迟启用标尺，确保canvas完全初始化
    nextTick(() => {
      const canvas = canvasEditor.canvas;
      if (canvas && canvas.width > 0 && canvas.height > 0) {
        canvasEditor.rulerEnable();
      } else {
        // 如果canvas尺寸为0，等待一段时间后重试
        setTimeout(() => {
          if (canvas && canvas.width > 0 && canvas.height > 0) {
            canvasEditor.rulerEnable();
          }
        }, 100);
      }
    });
  }

  if (route?.query?.id) {
    menuActive.value = 'myMaterial';
  }
});

onUnmounted(() => canvasEditor.destory());

const hideToolsBar = () => {
  state.toolsBarShow = !state.toolsBarShow;
};
const showToolsBar = (val) => {
  if (val === 'system') {
    const { href } = router.resolve({
      path: '/system'
    })
    window.open(href, '_blank')
    return
  }
  menuActive.value = val
  state.toolsBarShow = true
};
const switchAttrBar = () => {
  state.attrBarShow = !state.attrBarShow;
};

const { mixinState } = useSelectListen(canvasEditor);

// 文件模式
const isFileMode = ref(false)

provide('fabric', fabric);
provide('canvasEditor', canvasEditor);
provide('mixinState', mixinState);

const handleActiveMenu = () => {
  if (route.query.clampId) {
    menuActive.value = 'clamp'
  }
}

onMounted(() => {
  checkLogin()
  handleActiveMenu()
  initCos()
})
</script>
<style lang="less" scoped>
.left-bar {
  width: 65px;
  height: 100%;
  background: var(--sidebar-bg);
  display: flex;
  position: relative;
  border-right: 1px solid var(--border-color);

  &.show-tools-bar {
    width: 380px;
  }
}
.right-bar {
  width: 304px;
  height: 100%;
  padding: 10px;
  overflow-y: auto;
  background: var(--sidebar-bg);
  border-left: 1px solid var(--border-color);
}

.close-btn {
  width: 20px;
  height: 64px;
  cursor: pointer;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAACACAMAAABOb9vcAAAAhFBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////8AAADHx8cODg50dHTx8fF2dnZ1dXWWlpZHR0c4ODhQpkZ5AAAAIXRSTlMA9t+/upkRAnPq5NXDfDEsKQjMeGlRThkMsquljTwzIWhBHpjgAAABJElEQVRYw+3YyW7CQBCEYbxig8ELGJyQkJRJyPb+75dj3zy/lD7kMH3+ZEuzSFO1mlZwhjOE2uwhVHJYMygNVwilhz2EUvNaMigledUFoE1anKYAtA9nVRuANpviOQBt0t2ZQSnZ9QxK6Qih9LSGUHkJobYlhGp6CPW4hlAVhckLhMop1InCjEK1FBYU1hSqo/BI4YXCjMIthTWFijDCCB3g7fuO4O1t/rkvQXPz/LUIzX0oAM0tQHOfCkBzC9DcuwLQXACao9Dv1yb9lsek2xaaxMcMH1x6Ff79dY0wwgj/DGv3p2tG4cX9wd55h4rCO/hk3uEs9w6QlXPIbXrfIJ6XrmVBOtJCA1YkXqVLkh1aUgyNk1fV1BxLxzpsuNLKzrME/AWr0ywwvyj83AAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 50%;
  position: absolute;
  right: -20px;
  z-index: 1;
  top: 50%;
  margin-top: -10px;

  &.left-btn {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAACACAYAAAB5sSvuAAAAAXNSR0IArs4c6QAAAFBlWElmTU0AKgAAAAgAAgESAAMAAAABAAEAAIdpAAQAAAABAAAAJgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAKKADAAQAAAABAAAAgAAAAAAobJzlAAABWWlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iWE1QIENvcmUgNi4wLjAiPgogICA8cmRmOlJERiB4bWxuczp0aWZmPSJodHRwOi8vbnMuYWRvYmUuY29tL3RpZmYvMS4wLyI+CiAgICAgICAgIDx0aWZmOk9yaWVudGF0aW9uPjE8L3RpZmY6T3JpZW50YXRpb24+CiAgICAgIDwvcmRmOkRlc2NyaXB0aW9uPgogICA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgoZXuEHAAADf0lEQVR4Ae2cvYsTQRjGE7FQkICFB1pZRyzEJkUKmzOpBEHwX9DCQkmChf4JahewsLpWFOQUzwMRPEgEy0PLpPADvEISDrVyfZ6cK0tIZrI7u7MPMi+8mb35uPnlmXczyeXmrURRdKyibAB8Dz8pywg42if4OUnIGd7Bww8Ut+GHpEATgPEll/y8DGRMtaB8hrryl30B2HzVW1Rcgx8vQ9UqaVac+Cf67cC34C+q1erHFcc5dUsDOD/RGBWv4M/hrwG8jzJ3cwFMwlDdd/BN+BZgd5ONLtd5Ac4zfEYFld0ALMMisxUFmAQa44dHdMB+TTasdM2bxJNxI7gDP7ISWNzJE1xymhF+uBzPbyvL2NZOA+oJIO/BrfP7iEGTSNtovIrY/L6sU9mA5PoAby6DtEq87JnlWF/H7+K+v/DmUQDkc23CNxbFpAogIa/Ab/IiaQoxmOThlnkG8TiKK5UUJNNR+MMYjqUaIJnWEYuXeEFTBCTXv1hUi0HCxXYWsbirqiAhb/BBWcE9KLimDEgB68pLTMAL6oBNdcBT6oBr6oAn1O9i2a2Od/DM1Jc4KBivVOYyLHFm6f4ODAoGBV0VcB0fYjAo6KqA6/gQg0FBVwVcx4cYDAq6KuA6/v+Mwel0Wmm325XhcOgqkH08/h6cyiaTSdRoNPhvBFGtVosGg0Gq8Wk7V9IO6Pf7MzgC+oBMDcgn1Ov1vEFmAvQJmRmQkN1ut3AlnQB9QDoDErLT6RSmZC6ARULmBlgUpPxWl5uCRcVhLoBFwTFsnAGLfi10AiwazklBX/txJgV9wWVSUP7tlvwbVspOyFarVfi7ac4Vvquzfyoy95DfiwOgeQHtrUFBu0bmHkFBsz721qCgXSNzj6CgWR97a1DQrpG5R1DQrI+9NSho18jcIyho1sfauqeuoDzgN3UFv6gD7qh/cK8rA84OGygv8VO+CCkrKH3g5Q1P41BB1SV+QDia4hJvQ72LB3h6gPIH/+5CvVGsntoSPwYQzxr/VgRkJoF1wP1KwvFa4SaRPgDNI+RLT2dTwTJfB+9j/jaWden5dgIe5oNnG2O+WwCb7bXWuflliSfLlAjCh4JULHMqjaIAc0tGkhdgnM6FyXI2EV+5pXNxAeTSMSHOSzg3+H2UuVsaQKq0A/eaUmiVb9yZlOk6vJSkTCZA2bRWsonBpFOrySan+wNoJmOM0LyBGwAAAABJRU5ErkJggg==);
    opacity: 0.1;
  }

  &.left-btn-open {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAACACAMAAABOb9vcAAAAhFBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////8AAADHx8cODg50dHTx8fF2dnZ1dXWWlpZHR0c4ODhQpkZ5AAAAIXRSTlMA9t+/upkRAnPq5NXDfDEsKQjMeGlRThkMsquljTwzIWhBHpjgAAABJElEQVRYw+3YyW7CQBCEYbxig8ELGJyQkJRJyPb+75dj3zy/lD7kMH3+ZEuzSFO1mlZwhjOE2uwhVHJYMygNVwilhz2EUvNaMigledUFoE1anKYAtA9nVRuANpviOQBt0t2ZQSnZ9QxK6Qih9LSGUHkJobYlhGp6CPW4hlAVhckLhMop1InCjEK1FBYU1hSqo/BI4YXCjMIthTWFijDCCB3g7fuO4O1t/rkvQXPz/LUIzX0oAM0tQHOfCkBzC9DcuwLQXACao9Dv1yb9lsek2xaaxMcMH1x6Ff79dY0wwgj/DGv3p2tG4cX9wd55h4rCO/hk3uEs9w6QlXPIbXrfIJ6XrmVBOtJCA1YkXqVLkh1aUgyNk1fV1BxLxzpsuNLKzrME/AWr0ywwvyj83AAAAABJRU5ErkJggg==);
    transform: rotateY(360deg);
  }

  &.right-btn {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAACACAYAAAB5sSvuAAAAAXNSR0IArs4c6QAAAFBlWElmTU0AKgAAAAgAAgESAAMAAAABAAEAAIdpAAQAAAABAAAAJgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAKKADAAQAAAABAAAAgAAAAAAobJzlAAABWWlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iWE1QIENvcmUgNi4wLjAiPgogICA8cmRmOlJERiB4bWxuczp0aWZmPSJodHRwOi8vbnMuYWRvYmUuY29tL3RpZmYvMS4wLyI+CiAgICAgICAgIDx0aWZmOk9yaWVudGF0aW9uPjE8L3RpZmY6T3JpZW50YXRpb24+CiAgICAgIDwvcmRmOkRlc2NyaXB0aW9uPgogICA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgoZXuEHAAADf0lEQVR4Ae2cvYsTQRjGE7FQkICFB1pZRyzEJkUKmzOpBEHwX9DCQkmChf4JahewsLpWFOQUzwMRPEgEy0PLpPADvEISDrVyfZ6cK0tIZrI7u7MPMi+8mb35uPnlmXczyeXmrURRdKyibAB8Dz8pywg42if4OUnIGd7Bww8Ut+GHpEATgPEll/y8DGRMtaB8hrryl30B2HzVW1Rcgx8vQ9UqaVac+Cf67cC34C+q1erHFcc5dUsDOD/RGBWv4M/hrwG8jzJ3cwFMwlDdd/BN+BZgd5ONLtd5Ac4zfEYFld0ALMMisxUFmAQa44dHdMB+TTasdM2bxJNxI7gDP7ISWNzJE1xymhF+uBzPbyvL2NZOA+oJIO/BrfP7iEGTSNtovIrY/L6sU9mA5PoAby6DtEq87JnlWF/H7+K+v/DmUQDkc23CNxbFpAogIa/Ab/IiaQoxmOThlnkG8TiKK5UUJNNR+MMYjqUaIJnWEYuXeEFTBCTXv1hUi0HCxXYWsbirqiAhb/BBWcE9KLimDEgB68pLTMAL6oBNdcBT6oBr6oAn1O9i2a2Od/DM1Jc4KBivVOYyLHFm6f4ODAoGBV0VcB0fYjAo6KqA6/gQg0FBVwVcx4cYDAq6KuA6/v+Mwel0Wmm325XhcOgqkH08/h6cyiaTSdRoNPhvBFGtVosGg0Gq8Wk7V9IO6Pf7MzgC+oBMDcgn1Ov1vEFmAvQJmRmQkN1ut3AlnQB9QDoDErLT6RSmZC6ARULmBlgUpPxWl5uCRcVhLoBFwTFsnAGLfi10AiwazklBX/txJgV9wWVSUP7tlvwbVspOyFarVfi7ac4Vvquzfyoy95DfiwOgeQHtrUFBu0bmHkFBsz721qCgXSNzj6CgWR97a1DQrpG5R1DQrI+9NSho18jcIyho1sfauqeuoDzgN3UFv6gD7qh/cK8rA84OGygv8VO+CCkrKH3g5Q1P41BB1SV+QDia4hJvQ72LB3h6gPIH/+5CvVGsntoSPwYQzxr/VgRkJoF1wP1KwvFa4SaRPgDNI+RLT2dTwTJfB+9j/jaWden5dgIe5oNnG2O+WwCb7bXWuflliSfLlAjCh4JULHMqjaIAc0tGkhdgnM6FyXI2EV+5pXNxAeTSMSHOSzg3+H2UuVsaQKq0A/eaUmiVb9yZlOk6vJSkTCZA2bRWsonBpFOrySan+wNoJmOM0LyBGwAAAABJRU5ErkJggg==);
    transform: rotateY(180deg);
    right: 0px;
  }

  &.right-btn-open {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAACACAMAAABOb9vcAAAAhFBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////8AAADHx8cODg50dHTx8fF2dnZ1dXWWlpZHR0c4ODhQpkZ5AAAAIXRSTlMA9t+/upkRAnPq5NXDfDEsKQjMeGlRThkMsquljTwzIWhBHpjgAAABJElEQVRYw+3YyW7CQBCEYbxig8ELGJyQkJRJyPb+75dj3zy/lD7kMH3+ZEuzSFO1mlZwhjOE2uwhVHJYMygNVwilhz2EUvNaMigledUFoE1anKYAtA9nVRuANpviOQBt0t2ZQSnZ9QxK6Qih9LSGUHkJobYlhGp6CPW4hlAVhckLhMop1InCjEK1FBYU1hSqo/BI4YXCjMIthTWFijDCCB3g7fuO4O1t/rkvQXPz/LUIzX0oAM0tQHOfCkBzC9DcuwLQXACao9Dv1yb9lsek2xaaxMcMH1x6Ff79dY0wwgj/DGv3p2tG4cX9wd55h4rCO/hk3uEs9w6QlXPIbXrfIJ6XrmVBOtJCA1YkXqVLkh1aUgyNk1fV1BxLxzpsuNLKzrME/AWr0ywwvyj83AAAAABJRU5ErkJggg==);
    right: 304px;
  }
}

:deep(.attr-item) {
  position: relative;
  margin-bottom: 12px;
  height: 40px;
  padding: 0 10px;
  background: var(--bg-color-secondary);
  border: none;
  border-radius: 4px;
  display: flex;
  align-items: center;
  .ivu-tooltip {
    text-align: center;
    flex: 1;
  }
}

.ivu-menu-vertical .menu-item {
  text-align: center;
  padding: 10px 2px;
  box-sizing: border-box;
  font-size: 12px;

  & > i {
    margin: 0;
  }
}

:deep(.ivu-layout-header) {
  --height: 45px;
  padding: 0 0px;
  border-bottom: 1px solid var(--border-color);
  background: var(--header-bg);
  height: var(--height);
  line-height: var(--height);
  display: flex;
  justify-content: space-between;
  color: var(--text-color);
}

.left,
.right {
  display: flex;
  align-items: center;
  img {
    display: block;
    margin-right: 10px;
  }
}
.home,
.ivu-layout {
  height: 100vh;
}

.icon {
  display: block;
}

.canvas-box {
  position: relative;
}
.inside-shadow {
  position: absolute;
  width: 100%;
  height: 100%;
  box-shadow: inset 0 0 9px 2px #0000001f;
  z-index: 2;
  pointer-events: none;
}

#canvas {
  width: 300px;
  height: 300px;
  margin: 0 auto;
}

#workspace {
  flex: 1;
  width: 100%;
  position: relative;
  background-color: var(--canvas-bg);
  overflow: hidden;
}

.content {
  flex: 1;
  width: 220px;
  padding: 0 10px;
  height: 100%;
  overflow-y: auto;
}

.ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu) {
  background: none;
}
.switch {
  margin-right: 10px;
}
.design-stage-grid {
  --offsetX: 0px;
  --offsetY: 0px;
  --size: 16px;
  --color: #dedcdc;
  background-image: linear-gradient(
      45deg,
      var(--color) 25%,
      transparent 0,
      transparent 75%,
      var(--color) 0
    ),
    linear-gradient(45deg, var(--color) 25%, transparent 0, transparent 75%, var(--color) 0);
  background-position: var(--offsetX) var(--offsetY),
    calc(var(--size) + var(--offsetX)) calc(var(--size) + var(--offsetY));
  background-size: calc(var(--size) * 2) calc(var(--size) * 2);
}
// 文件模式
.c-file-mode {
  max-width: 0;
  padding: 0;
  margin: 0;
}
.c-home-loginOut {
  position: absolute;
  bottom: 15px;
  left: 22px;
  z-index: 1000;
  cursor: pointer;
}

// 画布区域采用主题样式
#workspace {
  background-color: var(--canvas-bg);

  &.c-file-mode {
    background-color: var(--canvas-bg);
  }
}

.canvas-box {
  background-color: var(--canvas-bg);
}

// 内容区域主题样式
.content {
  background-color: var(--sidebar-bg);
  color: var(--text-color);
}
</style>
<style lang="less">
.is-full-screen {
  width: 100% !important;
  .ivu-scroll-content {
    justify-content: flex-start !important;
  }
  .file-item {
    margin-right: 20px;
  }
}
.ivu-menu-item-active .c-poker-icon path {
  fill: #3f89e9;
}
.c-poker-icon:hover {
  path {
    fill: #3f89e9;
  }
}
</style>
