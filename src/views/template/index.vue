<template>
  <div class="home">
    <Layout>
      <Header :style="{ position: 'fixed', width: '100%', zIndex: 99 }">
        <div class="left">
          <Divider type="vertical" />
          在线设计工具
        </div>

        <div class="right">
          <Button type="primary" to="/" size="smail" target="_blank">新建设计</Button>
          <Divider type="vertical" />
          <login></login>
          <lang></lang>
        </div>
      </Header>

      <Content :style="{ margin: '40px 20px 0', minHeight: '500px', minWidth: '1200px' }">
        <banner></banner>
        <div class="search-box">
          <Input
            size="large"
            class="search-input"
            clearable
            search
            v-model="filters.name.$containsi"
            enter-button
            placeholder="请输入关键词"
            @on-search="search"
          />
          <TagSelect v-model="filters.templ_type.$in" @on-change="search">
            <TagSelectOption :name="item.id" :key="item.id" v-for="item in typeList">
              {{ item.name }}
            </TagSelectOption>
          </TagSelect>
        </div>

        <div
          class="img-box grid"
          id="imgBox"
          v-masonry
          transition-duration="0.3s"
          :gutter="10"
          item-selector=".grid-item"
        >
          <div v-masonry-tile class="img-item grid-item" v-for="info in templList" :key="info.id">
            <Tooltip :content="info.name" placement="top">
              <img :src="info.src" :alt="info.name" @click="toInfo(info)" />
            </Tooltip>
          </div>
        </div>
        <Page
          class="page"
          :total="total"
          v-model="page"
          @on-change="getTmplListHandel"
          @on-page-size-change="(val) => (pageSize = val)"
          :page-size="pageSize"
          show-sizer
        />
      </Content>
    </Layout>
  </div>
</template>

<script name="Home" setup>
import { toRaw } from 'vue';
import { Spin } from 'view-ui-plus';
import qs from 'qs';

import { useRouter } from 'vue-router';
const router = useRouter();
import banner from './components/banner.vue';
import lang from '@/components/lang.vue';
import login from '@/components/login';

import { getMaterialPreviewUrl } from '@/hooks/usePageList';
import { getTmplTypes, getTmplList } from '@/api/material';

const typeList = ref([]);
const getTypeListHandel = async () => {
  const res = await getTmplTypes();
  typeList.value = res.data.data.map((item) => {
    return {
      id: item.id,
      name: item.attributes.name,
    };
  });
};
getTypeListHandel();

const templList = ref([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(20);

const filters = reactive({
  name: {
    $containsi: '',
  },
  templ_type: {
    $in: [],
  },
});

const getTmplListHandel = async () => {
  const params = {
    populate: {
      img: '*',
    },
    filters: toRaw(filters),
    fields: ['name'],
    pagination: {
      page: page.value,
      pageSize: pageSize.value,
    },
  };
  Spin.show();
  try {
    const res = await getTmplList(qs.stringify(params));

    total.value = res.data.meta.pagination.total;
    templList.value = res.data.data.map((item) => ({
      id: item.id,
      name: item.attributes.name,
      src: getMaterialPreviewUrl(item.attributes.img),
    }));
  } catch (error) {
    console.log(error);
  }
  Spin.hide();
};

const search = () => {
  page.value = 1;
  getTmplListHandel();
};
getTmplListHandel();

const toInfo = (info) => {
  const href = router.resolve({
    path: '/',
    query: {
      tempId: info.id,
    },
  });
  console.log(href, 1111);
  window.open(href.href, '_blank');
};
</script>
<style lang="less" scoped>
:deep(.ivu-layout-header) {
  --height: 45px;
  padding: 0 0px;
  border-bottom: 1px solid #eef2f8;
  background: #fff;
  height: var(--height);
  line-height: var(--height);
  display: flex;
  justify-content: space-between;

  .left,
  .right {
    display: flex;
    align-items: center;
    img {
      display: block;
      margin-right: 10px;
    }
  }
}

.layout-footer-center {
  text-align: center;
}

.search-box {
  width: 1200px;
  margin: 20px auto;
  border-radius: 10px;
  background: #ffffffed;
  padding: 20px;
  border: 2px solid #fff;

  /deep/.ivu-tag-select {
    line-height: 32px;
    max-height: none;
    margin-left: 0;
    margin-top: 20px;
    .ivu-tag {
      font-size: 20px;
      line-height: 32px;
      height: 32px;
    }
  }
}

.img-box {
  width: 1200px;
  margin: 0 auto;
  .grid-item {
    width: 232px;
    cursor: pointer;
    margin-bottom: 5px;

    img {
      width: 100%;
      border-radius: 10px;
      &:hover {
        transform: scale(1.02);
      }
    }
  }
}

.page {
  margin: 20px auto;
  text-align: center;
}
</style>
