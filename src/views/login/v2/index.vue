<script name="login" setup>
import { FormItem, Button, Input, Icon, Form, Message } from 'view-ui-plus'
import { useRouter } from 'vue-router'
import { login, getCode } from '@/api/v2'
import { Encrypt } from '@/contants'

const router = useRouter()

const ruleInline = {
  userName: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  userPassword: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ]
}

const formInline = reactive({
  userName: '',
  userPassword: '',
  code: ''
})

const formInlineRef = ref(null)
const isLoading = ref(false)
const handleSubmit = async () => {
  isLoading.value = true
  formInline.userName = formInline.userName.trim()
  formInline.time = time.value
  try {
    const { data, message } = await login({
      ...formInline,
      userPassword: Encrypt(formInline.userPassword.trim())
    })
    if (!data) {
      throw new Error(message)
    }
    // await getToken()
    localStorage.setItem('user_type', JSON.stringify(data.userType))
    localStorage.setItem('java_token', JSON.stringify(data.token))
    Message.success('登录成功')
    router.push({
      path: '/'
    })
  } catch (e) {
    Message.error(e.message)
    updateCode()
  } finally {
    isLoading.value = false
  }
}

const src = ref(false)
const time = ref('')
const updateCode = async () => {
  time.value = Date.now().toString()
  const result = await getCode({ time: time.value })
  src.value = window.URL.createObjectURL(result)
}
onMounted(() => {
  updateCode()
})
</script>

<template>
  <div class="c-login">
  <div class="container">
    <div class="heading">Sign In</div>
    <form class="form" @submit.prevent>
      <input
        id="email"
        name="email"
        class="input"
        placeholder="用户名"
        v-model="formInline.userName"
      />
      <input
        placeholder="密码"
        id="password"
        name="password"
        type="password"
        class="input"
        v-model="formInline.userPassword"
      />

      <!-- 验证码区域 -->
      <div class="captcha-container">
        <div class="captcha-input-group">
          <input
            v-model="formInline.code"
            placeholder="验证码"
            class="input captcha-input"
            maxlength="4"
          />
          <img style="border-radius: 20px" :src="src" alt="验证码失败" @click="updateCode" />
        </div>
      </div>

      <button class="bubbles" @click="handleSubmit">
        <span class="text"> L O G I N </span>
      </button>
    </form>
  </div>
  </div>
</template>

<style scoped lang="less">
.c-login {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background-color: #f7f7f7;
  justify-content: center;
}
.c-login::after, .c-login::before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 50%;
  bottom: 0;
  background-image: url(../images/bg-login-left.png);
  background-repeat: no-repeat;
  background-position: bottom;
  background-size: 100%;
}
.c-login::after {
  left: 50%;
  right: 0;
  background-image: url(../images/bg-login-right.png);
}
/* From Uiverse.io by jeel-sardhara */
.container {
  max-width: 450px;
  background: #f8f9fd;
  background: linear-gradient(
    0deg,
    rgb(255, 255, 255) 0%,
    rgb(244, 247, 251) 100%
  );
  border-radius: 40px;
  padding: 25px 35px;
  margin: 0 auto;
  border: 5px solid rgb(255, 255, 255);
  box-shadow: rgba(133, 189, 215, 0.**********) 0px 30px 30px -20px;
  z-index: 999;
}

.heading {
  text-align: center;
  font-weight: 900;
  font-size: 30px;
  color: rgb(16, 137, 211);
}

.form {
  margin-top: 20px;
}

.form .input {
  width: 100%;
  background: white;
  border: none;
  padding: 15px 20px;
  border-radius: 20px;
  margin-top: 15px;
  box-shadow: #cff0ff 0px 10px 10px -5px;
  border-inline: 2px solid transparent;
}

.form .input::-moz-placeholder {
  color: rgb(170, 170, 170);
}

.form .input::placeholder {
  color: rgb(170, 170, 170);
}

.form .input:focus {
  outline: none;
}

/* 验证码样式 */
.captcha-container {
  margin-top: 15px;
}

.captcha-input-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.captcha-input {
  flex: 1;
  margin-top: 0 !important;
}

.captcha-display {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border-radius: 15px;
  padding: 5px 10px;
  box-shadow: #cff0ff 0px 10px 10px -5px;
  border: 2px solid transparent;
  transition: all 0.2s ease-in-out;
}

.captcha-display:hover {
  border-color: #12b1d1;
}

.captcha-canvas {
  cursor: pointer;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8f9fd 0%, #ffffff 100%);
  transition: all 0.2s ease-in-out;
}

.captcha-canvas:hover {
  transform: scale(1.02);
}

.refresh-btn {
  background: linear-gradient(45deg, rgb(16, 137, 211) 0%, rgb(18, 177, 209) 100%);
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: all 0.2s ease-in-out;
  box-shadow: rgba(133, 189, 215, 0.6) 0px 8px 8px -5px;
}

.refresh-btn:hover {
  transform: scale(1.1) rotate(180deg);
  box-shadow: rgba(133, 189, 215, 0.8) 0px 12px 12px -8px;
}

.refresh-btn:active {
  transform: scale(0.95) rotate(180deg);
}

.captcha-tip {
  font-size: 11px;
  color: rgb(170, 170, 170);
  text-align: center;
  margin-top: 8px;
  opacity: 0.8;
}

.form .forgot-password {
  display: block;
  margin-top: 10px;
  margin-left: 10px;
}

.form .forgot-password a {
  font-size: 11px;
  color: #0099ff;
  text-decoration: none;
}

.form .login-button {
  display: block;
  width: 100%;
  font-weight: bold;
  background: linear-gradient(
    45deg,
    rgb(16, 137, 211) 0%,
    rgb(18, 177, 209) 100%
  );
  color: white;
  padding-block: 15px;
  margin: 20px auto;
  border-radius: 20px;
  box-shadow: rgba(133, 189, 215, 0.**********) 0px 20px 10px -15px;
  border: none;
  transition: all 0.2s ease-in-out;
}

.form .login-button:hover {
  transform: scale(1.03);
  box-shadow: rgba(133, 189, 215, 0.**********) 0px 23px 10px -20px;
}

.form .login-button:active {
  transform: scale(0.95);
  box-shadow: rgba(133, 189, 215, 0.**********) 0px 15px 10px -10px;
}

.social-account-container {
  margin-top: 25px;
}

.social-account-container .title {
  display: block;
  text-align: center;
  font-size: 10px;
  color: rgb(170, 170, 170);
}

.social-account-container .social-accounts {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 5px;
}

.social-account-container .social-accounts .social-button {
  background: linear-gradient(45deg, rgb(0, 0, 0) 0%, rgb(112, 112, 112) 100%);
  border: 5px solid white;
  padding: 5px;
  border-radius: 50%;
  width: 40px;
  aspect-ratio: 1;
  display: grid;
  place-content: center;
  box-shadow: rgba(133, 189, 215, 0.**********) 0px 12px 10px -8px;
  transition: all 0.2s ease-in-out;
}

.social-account-container .social-accounts .social-button .svg {
  fill: white;
  margin: auto;
}

.social-account-container .social-accounts .social-button:hover {
  transform: scale(1.2);
}

.social-account-container .social-accounts .social-button:active {
  transform: scale(0.9);
}

.agreement {
  display: block;
  text-align: center;
  margin-top: 15px;
}

.agreement a {
  text-decoration: none;
  color: #0099ff;
  font-size: 9px;
}
.bubbles {
  --c1: #fff; /* Recommendation: same background color */
  --c2: #0099ff;
  --size-letter: 18px;
  padding: 0.5em 1em;
  width: 100%;
  margin-top: 20px;
  font-size: var(--size-letter);

  background-color: transparent;
  border: calc(var(--size-letter) / 6) solid var(--c2);
  border-radius: 10px;
  cursor: pointer;

  overflow: hidden;
  position: relative;
  transition: 300ms cubic-bezier(0.83, 0, 0.17, 1);
}

.bubbles > .text {
  font-weight: 700;
  color: var(--c2);
  position: relative;
  z-index: 1;
  transition: color 700ms cubic-bezier(0.83, 0, 0.17, 1);
}

.bubbles::before {
  top: 0;
  left: 0;
}

.bubbles::after {
  top: 100%;
  left: 100%;
}

.bubbles::before,
.bubbles::after {
  content: "";
  width: 150%;
  aspect-ratio: 1/1;
  scale: 0;
  transition: 600ms cubic-bezier(0.76, 0, 0.24, 1);

  background-color: var(--c2);
  border-radius: 50%;

  position: absolute;
  translate: -50% -50%;
}

.bubbles:hover {
  & > span {
    color: var(--c1);
  }
  &::before,
  &::after {
    scale: 1;
  }
}

.bubbles:active {
  scale: 0.98;
  filter: brightness(0.9);
}
</style>
