<template>
  <div class="home">
    <Layout>
      <Header :style="{ position: 'fixed', width: '100%', zIndex: 99 }">
        <div class="left">
          <Divider type="vertical" />
          在线设计工具
        </div>
        <div class="right">
          <login></login>
        </div>
      </Header>

      <Content :style="{ margin: '40px 20px 0', minHeight: '500px' }">
        <div class="form-box">
          <Card :bordered="false">
            <template #title>个人中心</template>

            <div v-if="isLogin">
              <DescriptionList title="VIP会员信息" layout="vertical">
                <Description :term="codeType[item.type]" v-for="item in vipList" :key="item.id">
                  开始时间：{{ item.start }} - 结束时间：{{ item.end }}
                </Description>
              </DescriptionList>

              <Form ref="formRef" :label-width="60" inline>
                <FormItem label="会员码">
                  <Input type="text" v-model="vipCode" placeholder="请输入会员码"></Input>
                </FormItem>
                <FormItem :label-width="0">
                  <Button type="primary" @click="handleSubmit">绑定</Button>
                </FormItem>
                <FormItem :label-width="0">
                  <Button
                    type="primary"
                    to="https://docs.qq.com/doc/DVE9rdXZRVHhWa2ZR?is_no_hook_redirect=1"
                    target="_blank"
                  >
                    购买会员码
                  </Button>
                </FormItem>
              </Form>
            </div>
            <div v-else>请登录</div>
          </Card>
        </div>
      </Content>
    </Layout>
  </div>
</template>

<script name="VipCodeGenerate" setup>
import login from '@/components/login';
import { Message, Copy } from 'view-ui-plus';
import { getVipCodeList, bindVipCode } from '@/api/user';

const codeType = {
  batchimg: '批量图片生成',
  removebg: '批量抠图',
};
const vipCode = ref('');
const handleSubmit = async () => {
  if (!vipCode.value) {
    Message.error('请输入会员码');
    return;
  }
  const res = await bindVipCode({
    code: vipCode.value,
  });
  if (res.data.data) {
    Message.success('绑定成功!');
    getCodeList();
  } else {
    Message.error(res.data?.msg || '绑定失败');
  }
};

const copyVipCode = () => {
  Copy({
    text: vipCode.value,
  });
};

const vipList = ref([]);
const isLogin = ref(false);
const getCodeList = async () => {
  try {
    const res = await getVipCodeList();
    vipList.value = res?.data?.data || [];
    isLogin.value = true;
  } catch (error) {
    isLogin.value = false;
  }
};

getCodeList();
</script>
<style lang="less" scoped>
:deep(.ivu-layout-header) {
  --height: 45px;
  padding: 0 0px;
  border-bottom: 1px solid #eef2f8;
  background: #fff;
  height: var(--height);
  line-height: var(--height);
  display: flex;
  justify-content: space-between;

  .left {
    display: flex;
    align-items: center;
    img {
      display: block;
      margin-right: 10px;
    }
  }
}

.layout-footer-center {
  text-align: center;
}

.form-box {
  padding: 20px 0;
}
</style>
