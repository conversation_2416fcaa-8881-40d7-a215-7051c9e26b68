<!-- file 权限管理 -->
<template>
  <Modal
    v-model="visible"
    title="权限管控"
    :width="600"
    :mask-closable="false"
    :closable="true"
    class="user-form-modal"
    @on-cancel="handleCancel"
    @on-ok="handleOk"
  >
    <h4>模板文件夹权限 : </h4>
    <Tree :data="treeData" show-checkbox multiple></Tree>
  </Modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { getUserPermission, updateFolderPermissions } from '@/api/v2'
import { Message, LoadingBar } from 'view-ui-plus'

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': [value: null]
}>()

// Props
interface Props {
  modelValue: boolean
  id: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  id: 0
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

const treeData = ref([])
watch(() => visible.value, async (val) => {
  if (!val) return
  LoadingBar.start()
  const { data } = await getUserPermission({ userId: props.id, type: 0 })
  treeData.value = data.map((item: any) => {
    return {
      title: item.name,
      id: item.id,
      checked: item.checked === '1',
      children: []
    }
  })
  LoadingBar.finish()
})


const handleCancel = () => {
  visible.value = false
}

const handleOk = async () => {
  const permissions = treeData.value.filter((item: any) => item.checked).map((item: any) => item.id)
  await updateFolderPermissions({
    permissions,
    type: 0,
    userId: props.id
  })
  visible.value = false
  Message.success('保存成功')
  emit('close', null)
}
</script>

<style lang="less">
.ivu-modal-header p, .ivu-modal-header-inner {
  color: var(--text-color);
}
</style>
