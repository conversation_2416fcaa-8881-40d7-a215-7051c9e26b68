<!-- file 用户管理 --->
<template>
  <div class="user-management">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-card">
        <div class="search-header">
          <div class="search-title">
            <Icon type="ios-search" class="title-icon" />
            <span>筛选条件</span>
          </div>
          <Button
            type="text"
            @click="toggleSearchExpand"
            class="expand-btn"
          >
            <Icon :type="searchExpanded ? 'ios-arrow-up' : 'ios-arrow-down'" />
          </Button>
        </div>

        <div class="search-content" :class="{ expanded: searchExpanded }">
          <div class="search-grid">
            <div class="search-item">
              <div class="item-label">用户名</div>
              <Input
                v-model="searchForm.userName"
                placeholder="搜索用户名..."
                clearable
                prefix="ios-person"
                @on-enter="handleSearch"
                style="width: 150px"
              />
            </div>

            <div class="search-item">
              <div class="item-label">用户类型</div>
              <Select
                v-model="searchForm.userType"
                placeholder="选择类型"
                clearable
                style="width: 150px"
              >
                <Option :value="-1">
                  <span class="option-item">
                    <Icon type="ios-globe" color="#52c41a" />
                    外部用户
                  </span>
                </Option>
                <Option :value="1">
                  <span class="option-item">
                    <Icon type="ios-people" color="#1890ff" />
                    内部用户
                  </span>
                </Option>
                <Option :value="0">
                  <span class="option-item">
                    <Icon type="ios-star" color="#f5222d" />
                    管理员
                  </span>
                </Option>
              </Select>
            </div>

            <div class="search-actions">
              <Button
                type="primary"
                @click="handleSearch"
                icon="ios-search"
                class="search-btn"
              >
                搜索
              </Button>
              <Button
                @click="handleReset"
                icon="ios-refresh"
                class="reset-btn"
              >
                重置
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <div class="table-card">
        <!-- 表格头部 -->
        <div class="table-header">
          <div class="header-left">
            <div class="table-title">
              <Icon type="ios-people" class="title-icon" />
              <span>用户列表</span>
              <div class="count-badge">{{ pagination.total }}</div>
            </div>
          </div>
          <div class="header-right">
            <Button
              type="primary"
              @click="handleAdd"
              icon="ios-add"
              class="add-btn"
            >
              新增用户
            </Button>
          </div>
        </div>

        <!-- 表格内容 -->
        <div class="table-wrapper" ref="tableWrapperRef">
          <Table
            ref="tableRef"
            :columns="columns"
            :data="tableData"
            :loading="loading"
            :height="tableHeight"
            class="modern-table"
            stripe
            @on-selection-change="handleSelectionChange"
          >
            <template #userType="{ row }">
              <div class="type-tag" :class="`type-${row.userType}`">
                <Icon :type="getUserTypeIcon(row.userType)" />
                <span>{{ getUserTypeText(row.userType) }}</span>
              </div>
            </template>

            <template #action="{ row }">
              <div class="action-group">
                <Button
                  type="text"
                  @click="handleEdit(row)"
                  class="action-btn edit"
                  size="small"
                >
                  <Icon type="ios-create" />
                  编辑
                </Button>
                <Button
                  type="text"
                  @click="handlePermissions(row)"
                  class="action-btn warn"
                  size="small"
                >
                  <Icon type="md-git-network" />
                  分配权限
                </Button>
                <Button
                  type="text"
                  @click="handleDelete(row)"
                  class="action-btn delete"
                  size="small"
                >
                  <Icon type="ios-trash" />
                  删除
                </Button>
              </div>
            </template>
          </Table>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <div class="pagination-info">
            <span>共 {{ pagination.total }} 条数据</span>
          </div>
          <Page
            :total="pagination.total"
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :page-size-opts="[10, 20, 50, 100]"
            show-total
            show-sizer
            show-elevator
            @on-change="handlePageChange"
            @on-page-size-change="handlePageSizeChange"
          />
        </div>
      </div>
    </div>

    <!-- 用户表单弹窗 -->
    <UserFormModal
      v-model="showUserModal"
      :user-data="currentUser"
      @submit="handleUserSubmit"
    />

    <!-- 分配权限弹窗 -->
    <Permissions v-model="showPermissionsModal" :id="currentUserId" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, onUnmounted } from 'vue'
import { Message, Modal } from 'view-ui-plus'
import UserFormModal from '@/components/system/UserFormModal.vue'
import Permissions from './components/Permissions.vue'
import { getUserList, removeUser, updateUser, createUser } from '@/api/v2'

// 接口类型定义
interface User {
  id: number
  userName: string
  email: string
  nickName: string
  userType: number
}

interface SearchForm {
  userName: string
  userType: number | null
}

interface Pagination {
  current: number
  pageSize: number
  total: number
}

// 响应式数据
const tableRef = ref()
const tableWrapperRef = ref()
const loading = ref(false)
const searchExpanded = ref(true)
const selectedRows = ref<User[]>([])
const tableHeight = ref(400)
const showUserModal = ref(false)
const currentUser = ref<User | null>(null)

// 搜索表单
const searchForm = reactive<SearchForm>({
  userName: '',
  userType: null
})

// 分页信息
const pagination = reactive<Pagination>({
  current: 1,
  pageSize: 20,
  total: 0
})

// 表格数据
const tableData = ref<User[]>([])

// 表格列配置
const columns = [
  {
    title: '昵称',
    key: 'nickName',
    ellipsis: true
  },
  {
    title: '账号',
    key: 'userName',
    ellipsis: true
  },
  {
    title: '邮箱',
    key: 'email',
    ellipsis: true
  },
  {
    title: '用户类型',
    key: 'userType',
    slot: 'userType'
  },
  {
    title: '创建时间',
    key: 'createdAt',
  },
  {
    title: '操作',
    key: 'action',
    slot: 'action'
  }
]

// 工具方法
const getUserTypeText = (userType: number): string => {
  const typeMap = {
    [-1]: '外部用户',
    [0]: '管理员',
    [1]: '内部用户'
  }
  return typeMap[userType] || '未知'
}

const getUserTypeIcon = (userType: number): string => {
  const iconMap = {
    [-1]: 'ios-globe',
    [0]: 'ios-star',
    [1]: 'ios-people'
  }
  return iconMap[userType] || 'ios-help'
}

// 事件处理方法
const toggleSearchExpand = () => {
  searchExpanded.value = !searchExpanded.value
  // 搜索区域展开/收起后重新计算表格高度
  setTimeout(() => {
    calculateTableHeight()
  }, 300) // 等待动画完成
}

const handleSearch = () => {
  pagination.current = 1
  fetchUserList()
}

const handleReset = () => {
  searchForm.userName = ''
  searchForm.userType = null
  pagination.current = 1
  fetchUserList()
}

const handleAdd = () => {
  currentUser.value = null
  showUserModal.value = true
}

const handleEdit = (row: User) => {
  currentUser.value = { ...row }
  showUserModal.value = true
}

const handleDelete = (row: User) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除用户 "${row.userName}" 吗？`,
    onOk: async () => {
      try {
        await removeUser(row.id)
        Message.success('删除成功')
        fetchUserList()
      } catch (error) {
        Message.error('删除失败')
      }
    }
  })
}

const showPermissionsModal = ref(false)
const currentUserId = ref(0)
const handlePermissions = (record: {id: number}) => {
  currentUserId.value = record.id
  showPermissionsModal.value = true
}

const handleRefresh = () => {
  fetchUserList()
}

const handleExport = () => {
  Message.info('导出功能待实现')
}

const handleSelectionChange = (selection: User[]) => {
  selectedRows.value = selection
}

const handlePageChange = (page: number) => {
  pagination.current = page
  fetchUserList()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchUserList()
}

// 处理用户表单提交
const handleUserSubmit = async (data: any, isEdit: boolean) => {
  try {
    if (isEdit) {
      // 编辑用户
      await updateUser(data)
      Message.success('用户更新成功')
    } else {
      // 新增用户
      const { code, message } = await createUser(data)
      if (code === '40000') return Message.error(message)
      Message.success('用户创建成功')
    }

    // 刷新用户列表
    fetchUserList()

  } catch (error) {
    console.error('用户操作失败:', error)
    Message.error(isEdit ? '用户更新失败' : '用户创建失败')
  }
}

// 计算表格高度
const calculateTableHeight = () => {
  nextTick(() => {
    if (!tableWrapperRef.value) return

    // 获取视窗高度
    const windowHeight = window.innerHeight

    // 获取表格容器的位置信息
    const tableWrapperRect = tableWrapperRef.value.getBoundingClientRect()

    // 计算表格容器顶部到视窗顶部的距离
    const tableWrapperTop = tableWrapperRect.top

    // 预留底部空间（分页器高度 + 内边距）
    const bottomSpace = 100

    // 计算可用高度
    const availableHeight = windowHeight - tableWrapperTop - bottomSpace

    // 设置最小高度和最大高度
    const minHeight = 300
    const maxHeight = 800

    // 计算最终高度
    tableHeight.value = Math.max(minHeight, Math.min(maxHeight, availableHeight))
  })
}

// 窗口大小变化监听
const handleResize = () => {
  calculateTableHeight()
}

// 获取用户列表数据
const fetchUserList = async () => {
  loading.value = true
  try {
    // 模拟接口调用
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      userName: searchForm.userName || undefined,
      userType: searchForm.userType !== null ? searchForm.userType : undefined
    }

    const result = await getUserList(params)

    tableData.value = result.data
    pagination.total = Number(result.meta.pagination.total)

  } catch (error) {
    console.error('获取用户列表失败:', error)
    Message.error('获取用户列表失败')

    // 接口失败时使用模拟数据
    const mockData = [
      { id: 1, userName: 'admin', email: '<EMAIL>', nickName: '系统管理员', userType: 0 },
      { id: 2, userName: 'john_doe', email: '<EMAIL>', nickName: '约翰·多伊', userType: 1 },
      { id: 3, userName: 'jane_smith', email: '<EMAIL>', nickName: '简·史密斯', userType: -1 },
      { id: 4, userName: 'mike_wilson', email: '<EMAIL>', nickName: '迈克·威尔逊', userType: 1 },
      { id: 5, userName: 'sarah_connor', email: '<EMAIL>', nickName: '莎拉·康纳', userType: -1 },
      { id: 6, userName: 'david_brown', email: '<EMAIL>', nickName: '大卫·布朗', userType: 1 },
      { id: 7, userName: 'lisa_white', email: '<EMAIL>', nickName: '丽莎·怀特', userType: -1 },
      { id: 8, userName: 'tom_green', email: '<EMAIL>', nickName: '汤姆·格林', userType: 1 },
      { id: 9, userName: 'anna_black', email: '<EMAIL>', nickName: '安娜·布莱克', userType: -1 },
      { id: 10, userName: 'peter_gray', email: '<EMAIL>', nickName: '彼得·格雷', userType: 1 },
      { id: 11, userName: 'mary_blue', email: '<EMAIL>', nickName: '玛丽·布鲁', userType: -1 },
      { id: 12, userName: 'jack_red', email: '<EMAIL>', nickName: '杰克·瑞德', userType: 1 },
      { id: 13, userName: 'emma_yellow', email: '<EMAIL>', nickName: '艾玛·耶洛', userType: -1 },
      { id: 14, userName: 'alex_purple', email: '<EMAIL>', nickName: '亚历克斯·珀普', userType: 1 },
      { id: 15, userName: 'sophia_orange', email: '<EMAIL>', nickName: '索菲亚·奥兰', userType: -1 }
    ]

    tableData.value = mockData
    pagination.total = 150
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchUserList()
  calculateTableHeight()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.user-management {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 20px;
  background: var(--bg-color);
  gap: 20px;
}

// 搜索区域
.search-section {
  .search-card {
    background: var(--bg-color);
    border-radius: 16px;
    border: 1px solid var(--border-color-light);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
  }

  .search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color-light);
    background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-color-secondary) 100%);

    .search-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color);

      .title-icon {
        color: #1890ff;
        font-size: 18px;
      }
    }

    .expand-btn {
      color: var(--text-color-secondary);
      transition: all 0.3s ease;

      &:hover {
        color: #1890ff;
        transform: scale(1.1);
      }
    }
  }

  .search-content {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;

    &.expanded {
      max-height: 200px;
    }
  }

  .search-grid {
    display: flex;
    padding: 24px;
  }

  .search-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    .item-label {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-color);
      white-space: nowrap;
      margin-right: 10px;
    }
  }

  .search-actions {
    display: flex;
    gap: 12px;

    .search-btn {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      border: none;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);
      }
    }

    .reset-btn {
      background: var(--button-bg);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      color: var(--text-color);
      transition: all 0.3s ease;

      &:hover {
        background: var(--button-hover-bg);
        transform: translateY(-1px);
      }
    }
  }
}

// 表格区域
.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;

  .table-card {
    background: var(--bg-color);
    border-radius: 16px;
    border: 1px solid var(--border-color-light);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color-light);
    background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-color-secondary) 100%);

    .header-left {
      .table-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 18px;
        font-weight: 600;
        color: var(--text-color);

        .title-icon {
          color: #1890ff;
          font-size: 20px;
        }

        .count-badge {
          background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
          color: white;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          min-width: 24px;
          text-align: center;
        }
      }
    }

    .header-right {
      display: flex;
      gap: 12px;

      .add-btn {
        background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
        border: none;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(82, 196, 26, 0.4);
        }
      }

      .icon-btn {
        background: var(--button-bg);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        color: var(--text-color-secondary);
        width: 36px;
        height: 36px;
        padding: 0;
        transition: all 0.3s ease;

        &:hover {
          background: var(--button-hover-bg);
          color: #1890ff;
          transform: translateY(-1px);
        }
      }
    }
  }

  .table-wrapper {
    flex: 1;
    overflow: hidden;
    position: relative;

    .modern-table {
      :deep(.ivu-table) {
        background: var(--bg-color);
        border: none;

        tr:hover {
          box-shadow: 0 0 6px rgba(0, 0, 0, 0.1) !important;
        }

        // 自定义滚动条样式
        .ivu-table-body {
          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }

          &::-webkit-scrollbar-track {
            background: var(--bg-color-secondary);
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
            transition: all 0.3s ease;

            &:hover {
              background: var(--text-color-secondary);
            }
          }
        }

        .ivu-table-header {
          th {
            background: var(--bg-color-secondary);
            color: var(--text-color);
            border: none;
            font-weight: 600;
            font-size: 14px;
            padding: 16px;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 16px;
              right: 16px;
              height: 1px;
              background: var(--border-color-light);
            }
          }
        }

        .ivu-table-body {
          tr {
            transition: all 0.2s ease;

            &:hover {
              background: var(--button-hover-bg) !important;
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            td {
              background: var(--bg-color);
              color: var(--text-color);
              border: none;
              padding: 16px;
              font-size: 14px;
              border-bottom: 1px solid var(--border-color-light);
            }
          }
        }
      }
    }
  }

  .type-tag {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;

    &.type--1 {
      background: rgba(82, 196, 26, 0.1);
      color: #52c41a;
      border: 1px solid rgba(82, 196, 26, 0.3);
    }

    &.type-0 {
      background: rgba(245, 34, 45, 0.1);
      color: #f5222d;
      border: 1px solid rgba(245, 34, 45, 0.3);
    }

    &.type-1 {
      background: rgba(24, 144, 255, 0.1);
      color: #1890ff;
      border: 1px solid rgba(24, 144, 255, 0.3);
    }
  }

  .action-group {
    display: flex;
    gap: 8px;
    justify-content: center;

    .action-btn {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
      transition: all 0.2s ease;
      border: none;

      &.edit {
        color: #1890ff;
        background: rgba(24, 144, 255, 0.1);

        &:hover {
          background: rgba(24, 144, 255, 0.2);
          transform: translateY(-1px);
        }
      }

      &.delete {
        color: #f5222d;
        background: rgba(245, 34, 45, 0.1);

        &:hover {
          background: rgba(245, 34, 45, 0.2);
          transform: translateY(-1px);
        }
      }

      &.warn {
        color: #f59f39;
        background: rgba(#f59f39, 0.1);

        &:hover {
          background: rgba(#f59f39, 0.2);
          transform: translateY(-1px);
        }
      }
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-top: 1px solid var(--border-color-light);
    background: var(--bg-color);

    .pagination-info {
      font-size: 14px;
      color: var(--text-color-secondary);
      font-weight: 500;
    }
  }
}

// 输入框和选择器样式
:deep(.ivu-input) {
  background: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-color);
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
  }

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &::placeholder {
    color: var(--text-color-secondary);
  }
}

:deep(.ivu-select) {
  .ivu-select-selection {
    background: var(--input-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-color);
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
    }
  }

  .ivu-select-placeholder {
    color: var(--text-color-secondary);
  }

  .ivu-select-arrow {
    color: var(--text-color-secondary);
  }
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

// 下拉菜单样式
:deep(.ivu-select-dropdown) {
  background: var(--modal-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

  .ivu-option {
    color: var(--text-color);
    padding: 12px 16px;
    transition: all 0.2s ease;

    &:hover {
      background: var(--button-hover-bg);
    }

    &.ivu-option-selected {
      background: rgba(24, 144, 255, 0.1);
      color: #1890ff;
    }
  }
}

// 分页器样式
:deep(.ivu-page) {
  .ivu-page-item {
    background: var(--button-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-color);
    margin: 0 4px;
    transition: all 0.2s ease;

    &:hover {
      background: var(--button-hover-bg);
      border-color: #1890ff;
      transform: translateY(-1px);
    }

    &.ivu-page-item-active {
      background: #1890ff;
      border-color: #1890ff;
      color: white;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
      a {
        color: var(--text-color);
      }
    }
  }

  .ivu-page-prev,
  .ivu-page-next {
    background: var(--button-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-color);
    transition: all 0.2s ease;

    &:hover {
      background: var(--button-hover-bg);
      border-color: #1890ff;
      color: #1890ff;
    }
  }

  .ivu-page-options {
    .ivu-select {
      .ivu-select-selection {
        background: var(--button-bg);
        border-color: var(--border-color);
        border-radius: 6px;
      }
    }

    .ivu-page-options-elevator input {
      background: var(--button-bg);
      border: 1px solid var(--border-color);
      border-radius: 6px;
      color: var(--text-color);
    }
  }
}
</style>
