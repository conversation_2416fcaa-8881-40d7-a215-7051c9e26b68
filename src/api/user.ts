import axios from 'axios';
const baseURL = import.meta.env.APP_APIHOST;

const instance = axios.create({ baseURL, timeout: 3600000 });

instance.interceptors.request.use(function (config) {
  const token = getToken();
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`;
  }
  return config;
});

const tokenKey = 'token';
function getToken() {
  const token = localStorage.getItem(tokenKey);
  return token || ''
}

export const getUserInfo = (data: any) => instance.get('/api/users/me', data);

export const login = (data: any) => instance.post('/api/auth/local', data);

export const register = (data: any) => instance.post('/api/auth/local/register', data);

export const logout = () => localStorage.setItem(tokenKey, '');

export const autoLogin = (data: any) => instance.post('/api/custom/autoAuthUser', data);

export const setToken = (token: string) => localStorage.setItem(tokenKey, token);

export const getFileList = (data: any) => instance.get('/api/user-materials?populate=*', data);

export const uploadImg = (data: any) => instance.post('/api/upload', data);

export const createdMaterial = (data: any) => instance.post('/api/user-materials', data);

export const removeMaterial = (id: any) => instance.delete('/api/user-materials/' + id);

export const createdTempl = (data: any) => instance.post('/api/user-templs', data);

export const removeTempl = (data: any) => instance.delete(`/api/user-templs/${data}`);

export const updataTempl = (id: any, data: any) => instance.put(`/api/user-templs/${id}`, data);

export const getTmplList = (data: any) => instance.get(`/api/user-templs?${data}`);

export const getTmplInfo = (data: any) => instance.get(`/api/user-templs/${data}`);

export const getUserFileTypeTree = () => instance.get(`/api/user-templ/getUerFileTypeTree`);

export const getFileTypeTree = (data: any) =>
  instance.get(`/api/custom/getUerFileTypeTree`, {
    params: data,
  });

export const getUerFileTree = () => instance.get(`/api/user-templ/getUerFileTree`);

export const getVipCodeList = () => instance.get('/api/vipcode/queryAll');

export const bindVipCode = (data: any) => instance.post('/api/vipcode/bind', data);

export const checkVipFeature = (data: any) =>
  instance.get('/api/vipcode/check', {
    params: data,
  });

export const userVipFeature = (data: any) => instance.post('/api/vipcode/use', data);

export const createFont = (data: any) => instance.post('/api/userfonts', data);

export const removeFont = (id: any) => instance.delete('/api/userfonts/' + id);

export const getFontList = (data: any) => instance.get('/api/userfonts?' + data);


export {
  instance
}
