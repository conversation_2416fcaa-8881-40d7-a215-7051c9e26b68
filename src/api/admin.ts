import axios from 'axios';
const baseURL = import.meta.env.APP_ADMINAPIHOST;

const instance = axios.create({ baseURL });

instance.interceptors.request.use(function (config) {
  const token = getToken();
  if (token) {
    config.headers['Authorization'] = `${token}`;
  }
  return config;
});

const tokenKey = 'AdminToken';
export function getToken() {
  const token = localStorage.getItem(tokenKey);
  return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwiaWF0IjoxNzIwMjgwMjA0LCJleHAiOjE3MjI4NzIyMDR9.1MKST1QhmL7rMePWpvd9avWvHAkBDD0TzOdlQWz9thY'
}

export function setToken(token: string) {
  localStorage.setItem(tokenKey, token);
}

export const createdTempl = (data: any) =>
  instance.post('/content-manager/collection-types/api::templ.templ', data);

export const updataTempl = (id: any, data: any) =>
  instance.put(`/content-manager/collection-types/api::templ.templ/${id}`, data);

export const uploadImg = (data: any) => instance.post('/upload', data);

export const deleteImg = (id: string) => instance.delete('/upload/files/' + id);

export const getTempl = (id: string) =>
  instance.get(`/content-manager/collection-types/api::templ.templ/${id}`);
