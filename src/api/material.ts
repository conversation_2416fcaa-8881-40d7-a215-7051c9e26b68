import qs from 'qs';
import axios from 'axios';

const baseURL = import.meta.env.APP_APIHOST;

const instance = axios.create({ baseURL });

export const getWebInfo = () => instance.get('/api/web-site?populate=*');

export const getMaterialTypes = () => instance.get('/api/material-types');

export const getMaterials = (data: any) => instance.get('/api/materials?' + data);

export const getMaterialsByType = (data: any) =>
  instance.get('/api/materials?' + qs.stringify(data));

export const getFontStyleTypes = () => instance.get('/api/font-style-types');

export const getFontStyles = (data: any) => instance.get('/api/font-styles?' + data);

export const getFontStyleListByType = (data: any) =>
  instance.get('/api/font-styles?' + qs.stringify(data));

export const getTmplTypes = () => instance.get('/api/templ-types');

export const getTmplList = (data: any) => instance.get('/api/templs?' + data);

export const getBannerList = (data: any) => instance.get('/api/banners?' + data);

export const generateVipCode = (data: any) => instance.post('/api/vipcode/generate', data);
