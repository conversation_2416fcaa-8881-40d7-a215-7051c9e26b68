import axios from 'axios'
import router from '@/router'
import { Message, Modal } from 'view-ui-plus'
import { $bus, EVENT_NAME } from '@/utils/mitt'

export const baseURL = {
  'test': 'http://43.133.23.224:6120/pre',
  'development': '/v2',
  'production': 'http://43.135.69.70:6120/pre',
}

const instance = axios.create({ baseURL: baseURL[import.meta.env.MODE as keyof typeof baseURL] });

instance.interceptors.request.use((config) => {
  const token = localStorage.getItem('java_token')
  if (token) {
    config.headers['Authorization'] = `Bearer ${JSON.parse(token)}`
    config.headers['token'] = `${JSON.parse(token)}`
  }
  return config
})
instance.interceptors.response.use((response) => {
  if (response.data.code === 40100) {
    logoutDebounce()
  }
  return response.data
})

// 防抖
let timer: NodeJS.Timeout | null
function debounce(fn: Function, delay: number) {
  return function (this: any) {
    const context = this
    const args = arguments
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(context, args);
    }, delay)
  }
}

// 退出登录
function logoutDebounce() {
  return debounce(() => {
    Modal.confirm({
      title: '登录已过期',
      content: '是否退回到登录页？若您目前在操作模板，您可以先导出模板，再退出登录',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        localStorage.removeItem('java_token')
        router.push({
          path: '/login'
        })
        Message.error('请重新登陆')
      }
    })
  }, 1000)()
}

// v2 新增留言板
export function addNotesV2(data = {}) {
  return instance.post('/notes-plus/add', data)
}

// 编辑留言板
export function editNotes(data = {}) {
  return instance.post('/editNotes', data)
}

// v2 编辑留言板
export function editNotesV2(data = {}) {
  return instance.post('/notes-plus/edit', data)
}

// v2 获取留言板内容
export function getNotesV2(id = 0) {
  return instance.get(`/notes-plus/get?id=${id}`)
}

// 批量下载文件
export function getDownImage(data = any) {
  return instance.post('/notes-plus/downloadImg', data, { responseType: 'blob' })
}

function toFormData(data: any) {
  const formData = new FormData();
  for (const key in data) {
    const value = data[key]
    if (value instanceof Array) {
      value.forEach((item: any) => {
        formData.append(key, item);
      })
    } else {
      formData.append(key, value);
    }
  }
  return formData;
}

// 文件搜索
export function getSearchFile(data = {}) {
  return instance.get('/folder/search', { params: data })
}

// 修改文件、文件夹名字
export function updateFileName(data = {}) {
  return instance.post('/folder-plus/editFolder', data)
}

// 删除文件
export function removeFile(data = {}) {
  return instance.post('/folder-plus/delPlus', data)
}

// 粘贴文件
export function pasteFile(data = {}) {
  return instance.post('/folder-plus/copyPlus', data)
}

// 文件另存为
export function saveAsFile(data = {}) {
  return instance.post('/folder-plus/saveAsPlus', data)
}

// 登录
export function login(data = {}) {
  return instance.post('/user/login', data)
}

// 获取验证码
export function getCode(params = {}) {
  return instance.get('/user/code', { responseType: 'blob', params })
}

// 退出登录
export function logout() {
  return instance.post('/user/logout')
}

// 检验登录是否过期
export function checkLogin() {
  return instance.get('/check/login')
}

// v2 上传文档
export function  setDocumentV2(data = {}) {
  return instance.post('/file-doc-plus/addDoc', data)
}

// v2 获取文档内容
export function getDocumentV2(id = 0) {
  return instance.get(`/file-doc-plus/getDoc?id=${id}`)
}

// 编辑富文本
export function setRichText(data = {}) {
  return instance.post('/file-doc/editDoc', data)
}

// v2 编辑富文本
export function setRichTextV2(data = {}) {
  return instance.post('/file-doc-plus/editDoc', data)
}

// v2 删除富文本
export function removeRichTextV2(data = {}) {
  return instance.post('/file-doc-plus/delDoc', data)
}

// 模板列表搜索
export function getTemplateList(data = {}) {
  return instance.post('/folder-plus/list', data)
}

// 模板详情
export function getTemplateDetail(id = 0) {
  $bus.emit(EVENT_NAME.IS_HIDE_SHOW_BUTTON, true)
  return instance.get(`/folder-plus/get?fileId=${id}`)
}

// v2 创建模板
export function addTemplateV2(data = {}) {
  return instance.post('/folder-plus/add', data)
}

// v2 更新模板
export function updateTemplateV2(data = {}) {
  return instance.post('/folder-plus/editPlus', data)
}

// 获取字体列表
export function getFontFamilyList(data = {}) {
  return instance.post('/user-fonts/listPlus', data)
}

// 删除字体
export function removeFontFamily(data = {}) {
  return instance.post('/user-fonts/delPlus', data)
}

// 删除素材
export function removeMaterial(data = {}) {
  return instance.post('/user-materials/del', data)
}

// 获取具体字体列表
export function getFontFamilyDetail(data = {}) {
  return instance.post('/user-fonts/listByFontNamesPlus', data)
}

// 保存上传图片
export function updateFileImage(data = {}) {
  return instance.post('/folder/editFileImage', toFormData(data))
}

// 更新模板
export function updateTemplate(data = {}) {
  return instance.post('/folder/editFileJSON', toFormData(data))
}


// 获取素材列表
export function getMaterialList(data = {}) {
  return instance.post('/user-materials/list', data)
}

// 上传素材
export function setMaterial(data = {}) {
  return instance.post('/user-materials/uploadMaterial', toFormData(data))
}

// 增加素材文件夹
export function addMaterialFolder(data = {}) {
  return instance.post('/user-materials/addMaterialFolder ', data)
}

// 获取桶秘钥
export function getBucketKey() {
  return instance.get('/cos/generateCosKeyTemp')
}

// 获取图层信息
export function getLayerInfo(id = 0) {
  return instance.get(`/layer/get?fileId=${id}`)
}

// 新增图层信息
export function setLayerInfo(data = {}) {
  return instance.post('/layer/addOrUpdate', data)
}

// 查询夹具列表
export function getFixtureList(data = {}) {
  return instance.post('/clamp/list', data)
}

// 新增夹具模板
export function addFixture(data = {}) {
  return instance.post('/clamp/add', data)
}

// 编辑夹具模板
export function updateFixture(data = {}) {
  return instance.post('/clamp/edit', data)
}

// 修改夹具名称
export function editFixtureName(data = {}) {
  return instance.post('/clamp/editClampName', data)
}

// 删除夹具模板
export function removeFixture(data = {}) {
  return instance.post('/clamp/del', data)
}

// 获取夹具详情
export function getFixtureDetail(id = 0) {
  $bus.emit(EVENT_NAME.IS_HIDE_SHOW_BUTTON, true)
  return instance.get(`/clamp/get?id=${id}`)
}

// 粘贴夹具
export function pasteFixture(data = {}) {
  return instance.post('/clamp/copy', data)
}

// 获取父子文件夹信息
export function getAllParentFolder(data = {}) {
  return instance.post('/common/getAllParent', data)
}

// 文字上传
export function uploadText(data = {}) {
  return instance.post('/user-fonts/addFont', toFormData(data))
}

// 文件导出
export function exportFile(data = {}) {
  return instance.post('/folder-plus/export', data)
}

// 列表查询
export function getUserList(data = {}) {
  return instance.post('/user/list', data)
}

// 删除客户
export function removeUser(id: number) {
  return instance.get(`/user/delete?userId=${id}`)
}

// 更新客户信息
export function updateUser(data = {}) {
  return instance.post(`/user/update`, data)
}

// 新增客户信息
export function createUser(data = {}) {
  return instance.post(`/user/add`, data)
}

// 更新密码
export function updatePassword(data = {}) {
  return instance.post(`/user/password/update`, data)
}

// 获取权限
export function getPermissions(data = {}) {
  return instance.post(`/permission-config/listFolders`, data)
}

// 获取用户信息
export function getUserInfo() {
  return instance.get(`/user/get`)
}

// 获取用户权限
export function getUserPermission(data = {}) {
  return instance.post(`/permission-config/listFoldersByUserId`, data)
}

// 更新权限
export function updateFolderPermissions(data = {}) {
  return instance.post('/permission-config/updateFolderPermissions', data)
}
