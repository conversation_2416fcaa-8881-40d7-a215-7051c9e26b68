import Editor, { EventType } from '@kuaitu/core';
import { useI18n } from 'vue-i18n';

const { SelectMode } = EventType;

interface Selector {
  mSelectMode: (typeof SelectMode)[keyof typeof SelectMode];
  mSelectOneType: string | undefined;
  mSelectId: string | undefined;
  mSelectIds: (string | undefined)[];
  mSelectActive: unknown[];
}

export default function useSelect(matchType?: Array<string>) {
  const fabric = inject('fabric');
  const { t } = useI18n();
  const canvasEditor = inject('canvasEditor') as Editor;
  const mixinState = inject('mixinState') as Selector;

  let isMatchType;
  if (matchType) {
    isMatchType = computed(() => matchType.includes(mixinState.mSelectOneType));
  }
  const isOne = computed(() => mixinState.mSelectMode === 'one');

  return {
    fabric,
    canvasEditor,
    mixinState,
    isOne,
    isMatchType,
    t,
  };
}
