import qs from 'qs';
import { ref } from 'vue';
import { getTemplateList } from '@/api/v2'

const APIHOST = import.meta.env.APP_APIHOST;
export default function usePageList({
  v2,
  el,
  apiClient,
  filters = {},
  sort = [],
  formatData,
  fields = []
}) {
  const showScroll = ref(false);
  const scrollHeight = ref(0);
  const startPage = async () => {
    const myTemplBox = document.querySelector(el);
    scrollHeight.value = myTemplBox.offsetHeight;
    showScroll.value = true;

    await startGetList();
  };

  const pageData = ref([]);
  const page = ref(1);
  const pagination = reactive({
    page: 0,
    pageCount: 0,
    pageSize: 10,
    total: 0,
  });

  const isDownBottom = computed(() => {
    return pagination.page === page.value && pagination.page >= pagination.pageCount;
  });

  const pageLoading = ref(false);
  const getPageData = async () => {
    pageLoading.value = true;
    try {
      const query = {
        populate: {
          img: '*',
        },
        filters: {},
        sort: sort,
        fields,
        pagination: {
          page: page.value,
          pageSize: page.value.pageSize,
        },
      };
      const params = addFilterParams(query, filters);
      const record = {
        parentId: params.filters.parentId.$eq,
        pageSize: 100,
        pageNum: params.pagination.page
      }
      if (sortKey.value) {
        record[sortKey.value] = sortKey.value.includes('At') ? 'desc' : 'asc'
      }
      const api = v2 ? getTemplateList : apiClient
      const res = await api(v2 ? record : qs.stringify(params));
      const list = formatData ? formatData(v2 ? res.data : res.data.data) : res.data.data;
      Object.keys(v2 ? res.meta.pagination : res.data.meta.pagination).forEach((key) => {
        pagination[key] = v2 ? res.meta.pagination[key] : res.data.meta.pagination[key];
      });
      pageData.value = [...pageData.value, ...list];
    } catch (error) {
      console.log(error);
    }
    pageLoading.value = false;
  };

  const startGetList = () => {
    pageData.value = [];
    page.value = 1;
    getPageData();
  };

  const nextPage = () => {
    if (page.value >= pagination.pageCount) return;
    page.value++;
    setTimeout(() => {
      getPageData();
    }, 1000);
  };

  const addFilterParams = (query, filters) => {
    Object.keys(filters).forEach((key) => {
      const itemFilter = {};
      Object.keys(filters[key]).forEach((myKey) => {
        const skip = ['$eq', '$contains'];
        const isNone = !filters[key][myKey];
        const isSkip = skip.includes(myKey) && isNone;
        if (!isSkip) {
          itemFilter[myKey] = filters[key][myKey];
        } else {
          const isFilterEmpty = filters[key].filterEmpty;
          if (!isFilterEmpty) {
            itemFilter[myKey] = filters[key][myKey];
          }
        }
      });
      query.filters[key] = itemFilter;
    });
    return query;
  };

  const sortKey = ref('')
  const handleSort = (key) => {
    sortKey.value = key
    pageData.value = []
    getPageData()
  }

  return {
    page,
    sortKey,
    pageData, // 分页数据
    showScroll,
    scrollHeight,
    pageLoading,
    handleSort,
    isDownBottom, // 是否到达底部
    startPage, // 开始分页
    getPageData, // 获取分页数据
    startGetList, // 从第一个开始
    nextPage, // 下一页
  };
}

const getMaterialInfoUrl = (info) => {
  const imgUrl = info?.data?.attributes?.url || '';
  return APIHOST + imgUrl;
};

const getMaterialPreviewUrl = (info) => {
  const imgUrl = info?.data?.attributes?.formats?.small?.url || info?.data?.attributes?.url || '';
  return APIHOST + imgUrl;
};

export { getMaterialInfoUrl, getMaterialPreviewUrl };
