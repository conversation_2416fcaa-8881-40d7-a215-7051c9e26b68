import {
  createFileType as createFileType<PERSON><PERSON>,
  getFileTypeList as getFileTypeListApi,
} from '@/api/user';
import { Spin } from 'view-ui-plus';
export default function useFileType() {
  const createFileType = async (fileTypeName, parentId = '') => {
    Spin.show();
    const res = await createFileTypeApi({
      data: {
        name: fileTypeName,
        parentId,
      },
    });
    Spin.hide();
    return res;
  };

  const getFileTypeList = async (params) => {
    Spin.show();
    const res = await getFileTypeListApi({
      data: params,
    });
    Spin.hide();
    return res;
  };

  return {
    createFileType,
    getFileTypeList,
  };
}
