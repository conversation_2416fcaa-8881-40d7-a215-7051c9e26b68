import dayjs from 'dayjs'
import { useRouter, useRoute } from 'vue-router'
import { uploadImg, createdTempl } from '@/api/user'
import { getTemplateDetail, updateFileImage, updateTemplate, addTemplateV2, updateTemplateV2 } from '@/api/v2'
import { Message } from 'view-ui-plus'
import { cos } from '@/utils/cos'

export default function useMaterial() {
  const router = useRouter();
  const route = useRoute();
  const canvasEditor = inject('canvasEditor');

  const createTmpl = async (width, height, parentId = '') => {
    canvasEditor.clear();
    canvasEditor.setSize(width, height);
    const name = dayjs().format('YYYY[年]MM[月]DD[日]HH[小时]mm[分钟]ss[秒]') + '创建的作品';
    const data = await getCanvasCommonData();
    const templInfo = await createdTempl({
      data: {
        ...data,
        type: 'file',
        parentId: String(parentId),
        name,
      },
    });
    routerToId(templInfo.data.data.id);
    return templInfo;
  };

  const createTemplateV2 = async (width, height, parentId = '') => {
    canvasEditor.clear()
    canvasEditor.setSize(width, height)
    const name = dayjs().format('YYYY[年]MM[月]DD[日]HH[小时]mm[分钟]ss[秒]') + '创建的作品'
    const commonData = await getCanvasCommonDataV2()
    const result = await addTemplateV2({
      name,
      parentId,
      type: 'file',
      ...commonData
    })
    routerToId(result.data)
    return name
  }

  // 上传json中的base
  const uploadTemplateBase = (json) => {
    const imgCanvas = json.objects.filter(item => item.type === 'image' && item.src.includes('data:image'))
    return Promise.all(imgCanvas.map(async item => {
      const file = dataURLtoFile(item.src, `detail_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}_folder_${Math.ceil(Math.random() *1000)}.png`)
      const { Location } = await cos.uploadFileToTemplate(file, file.name)
      const template = json.objects.find(template => template.id === item.id)
      template.src = `http://${Location}`
      template.crossOrigin = 'anonymous'
      return true
    }))
  }

  const getCanvasCommonDataV2 = async () => {
    const json = canvasEditor.getJson()
    await uploadTemplateBase(json)
    const url = await uploadTemplateImg()
    return {
      json: JSON.stringify(json),
      url
    }
  }

  const uploadTemplateImg = async () => {
    const base64 = await canvasEditor.preview()
    const file = dataURLtoFile(base64, `${dayjs().format('YYYY-MM-DD_HH-mm-ss')}_template.png`)
    const { Location } = await cos.uploadFileToTemplate(file, file.name)
    const urlArr = Location.split('/')
    urlArr.shift()
    return urlArr.join('/')
  }

  const createdFileType = async (name, parentId = '') => {
    await createdTempl({
      data: {
        name,
        type: 'fileType',
        parentId: String(parentId),
      },
    });
  };

  const createTmplByCommon = async () => {
    const name = dayjs().format('YYYY[年]MM[月]DD[日]HH[小时]mm[分钟]ss[秒]') + '创建的作品';
    const data = await getCanvasCommonData();
    const templInfo = await createdTempl({
      data: {
        ...data,
        type: 'file',
        parentId: '',
        externalId: route.query?.projectid || null,
        name,
      },
    });
    return templInfo;
  };

  const getCanvasCommonData = async () => {
    const json = canvasEditor.getJson();
    const fileInfo = await uploadFileToInfo();
    return {
      json,
      img: fileInfo.id,
      desc: '',
    };
  };

  const getUpdateCommonData = async (id) => {
    const json = canvasEditor.getJson()
    await updateImage(id)
    return {
      jsonStr: JSON.stringify(json),
      fileId: Number(id)
    }
  }

  const updateImage = async (fileId) => {
    const base64 = await canvasEditor.preview()
    try {
      const result = await updateFileImage({
        fileId,
        file: dataURLtoFile(base64, '123.png')
      })
      if (!!result.code) throw new Error(result.message)
    } catch (e) {
      Message.error(e.message)
    }
  }

  const dataURLtoFile = (dataurl, filename) => {
    var arr = dataurl.split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  }

  const uploadFileToInfo = async () => {
    const upload = (base64) => {
      const file = dataURLtoFile(base64, '123.png');
      const formData = new FormData();
      const time = new Date();
      formData.append('files', file, `${time.getTime()}`);
      return uploadImg(formData)
        .then((res) => {
          const [info] = res.data;
          return info;
        })
        .catch((err) => {
          console.log(err);
        });
    };
    const base64 = await canvasEditor.preview();
    const fileInfo = await upload(base64);
    return fileInfo;
  };

  const routerToId = (id) => {
    router.replace('/?id=' + id);
  };

  const getTemplInfo = async (id) => {
    const result = await getTemplateDetail(id)
    const json = result.data.json && JSON.parse(result.data.json)
    json.objects.forEach(item => {
      if (item.type === 'i-text' && item.fontFamily === '') {
        delete item.fontFamily
      }
    })
    result.data.json = json
    return result
  };

  const updataTemplInfo = async (id, name) => {
    const data = await getUpdateCommonData(id);
    name && (data.name = name);
    try {
      const result = await updateTemplate(data)
      if (result.code ===  50001) {
        throw new Error(result.message)
      }
      Message.success(result.data)
    } catch (e) {
      Message.error(e.message)
    }
  };

  const updateTemplateInfo = async (id, name) => {
    const commonData = await getCanvasCommonDataV2()
    try {
      const result = await updateTemplateV2({
        id,
        name,
        ...commonData
      })
      if (result.code ===  50001) {
        throw new Error(result.message)
      }
      Message.success('更新成功')
    } catch (e) {
      Message.error(e.message)
    }
  }

  const createdFileTypeV2 = async (name, parentId) => {
    await addTemplateV2({
      name,
      parentId,
      type: 'fileType'
    })
  }

  return {
    createTmpl,
    createTemplateV2,
    createTmplByCommon,
    getTemplInfo,
    updataTemplInfo,
    routerToId,
    createdFileType,
    createdFileTypeV2,
    dataURLtoFile,
    updateTemplateInfo
  };
}
