export default function useCalculate() {
  const canvasEditor = inject('canvasEditor');

  const getCanvasBound = () => canvasEditor.canvas.getSelectionElement().getBoundingClientRect();

  const isOutsideCanvas = (x, y) => {
    const { left, right, top, bottom } = getCanvasBound();
    return x < left || x > right || y < top || y > bottom;
  };

  return {
    getCanvasBound,
    isOutsideCanvas,
  };
}
