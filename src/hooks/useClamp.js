import dayjs from 'dayjs'
import { Message } from 'view-ui-plus'
import { useRouter, useRoute } from 'vue-router'
import { cos } from '@/utils/cos'
import { addFixture, getFixtureList, updateFixture, getFixtureDetail, getAllParentFolder } from '@/api/v2'
import Loading from '@/components/load/index.js'
import { EVENT_NAME, $bus } from '@/utils/mitt'

const dataURLtoFile = (dataUrl, filename) => {
  const arr = dataUrl.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], filename, { type: mime })
}

export default function useClamp() {
  const CLAMP_TYPE = {
    FILE: '0',
    FOLDER: '1'
  }
  const router = useRouter()
  const route = useRoute()
  const pageConfig = reactive({
    parentId: 0,
    pageNum: 0,
    pageSize: 50
  })
  const sortKey = ref('')
  const clampList = ref([])
  const isLoading = ref(false)
  const paths = ref([
    { id: 0, name: '全部' }
  ])
  const canvasEditor = inject('canvasEditor')

  const handleClearPath = () => {
    paths.value = [{ id: 0, name: '全部' }]
  }

  const routerToClampId = async (id) => {
    await router.replace(`/?clampId=${id}`)
  }

  const createClampCanvas = async (width, height, type, name) => {
    Loading.show()
    try {
      let src = null
      if (type === CLAMP_TYPE.FILE) {
        canvasEditor.clear()
        canvasEditor.setSize(width, height)
        src = await uploadClampImg()
      }
      const params = await getCanvasCommonData(name)
      params.type = type
      if (type === CLAMP_TYPE.FOLDER) {
        delete params.jsonStr
      } else {
        params.url = src
      }

      const result = await addFixture(params)
      if (result.code === 50000) {
        throw new Error('新增失败')
      }
      Message.success('创建成功')
      $bus.emit(EVENT_NAME.FILE_NAME, params.name)
      $bus.emit(EVENT_NAME.CLEAR_DOCUMENT)
      await routerToClampId(result.data)
      await handleResetSearch(pageConfig.parentId)
    } catch (e) {
      Message.error(e.message)
    } finally {
      Loading.hide()
    }
  }

  const updateClampCanvas = async (name) => {
    Loading.show()
    try {
      const params = await getCanvasCommonData(name)
      const src = await uploadClampImg()
      params.url = `${src}`
      params.id = route.query.clampId
      const result = await updateFixture(params)
      if (result.code === 50000) {
        throw new Error('更新失败')
      }
      Message.success('更新成功')
      await handleResetSearch(pageConfig.parentId)
    } catch (e) {
      Message.error(e.message)
    } finally {
      Loading.hide()
    }
  }

  const getCanvasCommonData = async (name = '') => {
    const jsonStr = canvasEditor.getJson()
    return {
      parentId: pageConfig.parentId,
      jsonStr: JSON.stringify(jsonStr),
      name: name || `${dayjs().format('YYYY[年]MM[月]DD[日]HH[小时]mm[分钟]ss[秒]')}创建的作品`
    }
  }

  const uploadClampImg = async () => {
    const base64 = await canvasEditor.preview()
    const file = dataURLtoFile(base64, `${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.png`)
    const { Location } = await cos.uploadFileToClamp(file, file.name)
    const urlArr = Location.split('/')
    urlArr.shift()
    return urlArr.join('/')
  }

  const getClampList = async () => {
    isLoading.value = true
    pageConfig.pageNum++
    const params = {
      ...pageConfig
    }
    if (sortKey.value) {
      params[sortKey.value] = sortKey.value.includes('At') ? 'desc' : 'asc'
    }
    const result = await getFixtureList(params)
    clampList.value = [...clampList.value, ...result.data].sort((a, b) => b.type - a.type)
    isLoading.value = false
  }

  const handleResetSearch = async (parentId = 0) => {
    pageConfig.parentId = parentId
    pageConfig.pageNum = 0
    clampList.value = []
    await getClampList()
  }

  const handleClampIdDetail = async () => {
    if (!route.query.clampId) return
    Loading.show()
    const result = await getFixtureDetail(route.query.clampId)
    canvasEditor.loadJSON(result.data.jsonStr, Loading.hide)
  }

  const getClampFolderList = async () => {
    const id = route.query.clampId
    if (!id) {
      return await handleResetSearch()
    }
    const result = await getAllParentFolder({
      id,
      type: 'CLAMP'
    })
    paths.value = result.data.map(item => ({ id: item.id || '', name: item.name }))
    await handleResetSearch(paths.value.at(-1).id)
  }

  // 上传当前模板 生成夹具
  const uploadCurrentTemplate = async () => {
    const base64 = await canvasEditor.preview()
    const file = dataURLtoFile(base64, `${dayjs().format('YYYY-MM-DD_HH-mm-ss_Temporary')}.png`)
    const { Location } = await cos.uploadFileToTemplate(file, file.name)
    const urlArr = Location.split('/')
    urlArr.shift()
    return urlArr.join('/')
  }

  return {
    paths,
    sortKey,
    isLoading,
    pageConfig,
    CLAMP_TYPE,
    clampList,
    dataURLtoFile,
    uploadCurrentTemplate,
    routerToClampId,
    createClampCanvas,
    getClampList,
    handleResetSearch,
    updateClampCanvas,
    handleClampIdDetail,
    getClampFolderList,
    handleClearPath
  }
}
