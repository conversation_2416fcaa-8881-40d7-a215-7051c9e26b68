import { ref, watch } from 'vue'
import { getLocal, setLocal } from '@/utils/local'

const THEME_KEY = 'app-theme'

export type Theme = 'light' | 'dark'

// 主题状态
const theme = ref<Theme>((getLocal(THEME_KEY) as Theme) || 'light')

// 应用主题到DOM
const applyTheme = (newTheme: Theme) => {
  const html = document.documentElement
  if (newTheme === 'dark') {
    html.setAttribute('data-theme', 'dark')
  } else {
    html.removeAttribute('data-theme')
  }
}

// 切换主题
const toggleTheme = () => {
  theme.value = theme.value === 'light' ? 'dark' : 'light'
}

// 设置主题
const setTheme = (newTheme: Theme) => {
  theme.value = newTheme
}

// 监听主题变化
watch(theme, (newTheme) => {
  applyTheme(newTheme)
  setLocal(THEME_KEY, newTheme)
}, { immediate: true })

export const useTheme = () => {
  return {
    theme: readonly(theme),
    toggleTheme,
    setTheme,
    isDark: computed(() => theme.value === 'dark')
  }
}
