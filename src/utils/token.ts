import axios from 'axios';
import md5 from 'md5';

async function getToken() {
  const key = md5('yangji<PERSON>' + 'AUTO_lOGIN_SECRET')
  const result = await axios.post('/api/custom/autoAuthUser', {
    username: 'yang<PERSON><PERSON>',
    key
  });
  localStorage.setItem('token', result.data.jwt)
}

function getSrcFromHtml(html: string) {
  const imgReg = /<img.*?(?:>|\/>)/gi
  const srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i
  const arr = html.match(imgReg) || []
  const srcArr = []
  for (let i = 0; i < arr.length; i++) {
    const src = arr[i].match(srcReg)
    // 获取图片地址
    srcArr.push(src && src[1])
  }
  return srcArr
}

/**
 * 防抖
 * @param func
 * @param wait
 */
function debounce(func: any, wait = 1000) {
  let timeout: any;

  return function() {
    const context = this as any;
    const args = arguments;

    clearTimeout(timeout);
    timeout = setTimeout(function() {
      func.apply(context, args);
    }, wait);
  };
}

export {
  getToken,
  getSrcFromHtml,
  debounce
}
