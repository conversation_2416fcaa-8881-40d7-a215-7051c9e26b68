import COS from 'cos-js-sdk-v5'
import Loading from '@/components/load/index.js'
import { Message } from 'view-ui-plus'
import { getBucketKey } from '@/api/v2'
import { getDAesString } from '@/contants'

const BUCKET_NAME = import.meta.env.MODE === 'production' ? 'prod-env-1317082621' : 'test-env-1317082621'
const COS_URL = import.meta.env.MODE === 'production' ? '//prod-env-1317082621.cos.ap-hongkong.myqcloud.com/' : '//test-env-1317082621.cos.ap-hongkong.myqcloud.com/'
const REGION_REG = 'ap-hongkong'
const SLICE_SIZE = 1024 * 1024 * 5

let supportWebp = null
let cosRecord = null
// 当前浏览器是否支持webp
const isSupportWebp = () => {
  if (supportWebp === null) {
    supportWebp = typeof document !== 'undefined' && document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp') === 0
  }
  return supportWebp
}

// 替换COS链接为webp
const toWebp = (url, width = 100, height = 100) => {
  let str = width || height ? '/thumbnail' : ''
  if (width) {
    str += '/' + width
  }
  if (height) {
    str += 'x' + height
  }
  if (!isSupportWebp()) {
    return url + '?imageMogr2' + str
  }
  return COS_URL + url + '?imageMogr2/format/webp' + str
}

// 初始化COS配置
async function initCos() {
  const result = await getBucketKey()
  cosRecord = new COS({
    getAuthorization: (options, callback) => {
      const { sessionToken, tmpSecretId, tmpSecretKey } = result.data
      const times = Math.floor(new Date().getTime() / 1000)
      callback({
        TmpSecretId: getDAesString(tmpSecretId),
        TmpSecretKey: getDAesString(tmpSecretKey),
        SecurityToken: getDAesString(sessionToken),
        StartTime: times,
        ExpiredTime: times + 864000,
        ScopeLimit: true
      })
    }
  })
}

const uploadFileToCos = async (file, key, fileName) => {
  try {
    const result = await cosRecord.uploadFile({
      Bucket: BUCKET_NAME,
      Region: REGION_REG,
      Key: `${key}/${fileName}`,
      Body: file,
      SliceSize: SLICE_SIZE,
      ContentDisposition: 'inline'
    })
    return result
  } catch (e) {
    console.log(e)
    Message.error('上传失败')
    return null
  }
}

const cos = {
  // 上传夹具
  uploadFileToClamp: (file, name) => uploadFileToCos(file, 'clamp', name),
  // 上传模板
  uploadFileToTemplate: (file, name) => uploadFileToCos(file, 'folder', name),
  // 上传素材
  uploadFileToMaterial: (file, name) => uploadFileToCos(file, 'material', name),
  // 上传图层
  uploadFileToLayer: (file, name) => uploadFileToCos(file, 'layer', name),
  // 上传留言板
  uploadFileToNotes: (file, name) => uploadFileToCos(file, 'note', name),
  // 上传文档
  uploadFileToDocument: (file, name) => uploadFileToCos(file, 'doc', name),
  // 扑克牌
  uploadFileToPoker: (file, name) => uploadFileToCos(file, 'poker', name),
  // 其他
  uploadFileToOther: (file, name) => uploadFileToCos(file, 'other', name),
}

export {
  toWebp,
  initCos,
  cos,
  COS_URL
}
