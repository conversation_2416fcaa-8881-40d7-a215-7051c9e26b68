{"templates": "Modelos", "elements": "Elementos", "background": "Fundo", "size": "<PERSON><PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON>", "height": "Altura", "grid": "Grid", "common_elements": "Elementos comuns", "draw_elements": "Elementos arrastre", "color_macthing": "Seleção de cor", "background_texture": "Textura de fundo", "picture": "Imagem", "everything_is_fine": "<PERSON><PERSON><PERSON> tudo bem", "everything_goes_well": "<PERSON>do vai bem", "cartoon": "Cartoon", "default_size": "<PERSON><PERSON><PERSON>", "preview": "Antevis<PERSON>", "empty": "<PERSON><PERSON><PERSON>", "keep": "<PERSON><PERSON>", "copy_to_clipboard": "Copiar para a área de transferência", "save_as_picture": "<PERSON>var como imagem", "save_as_svg": "Salvar como SVG", "save_as_json": "Salvar como JSON", "layers": "Camadas", "title_template": "<PERSON><PERSON> tí<PERSON>lo", "insert_svg": "Inserir SVG", "select_svg": "Selecionar arquivo SVG", "please_choose": "Por favor, escolha", "string": "String", "file": "Arquivo", "import_files": "Importar arquivos", "select_json": "Selecione o arquivo JSON", "insert": "Inserir", "insert_picture": "Inserir imagem", "select_image": "Selecionar arquivo de imagem", "insertFile": {"remarks": "插入文件", "insert": "insert", "insert_picture": "Insert picture", "insert_SVG": "Insert SVG", "insert_SVGStr": "Insert SVG String", "insert_SVGStr_placeholder": "Please enter SVG String", "modal_tittle": "Please enter"}, "waterMark": {"text": "WaterMark", "modalTitle": "WaterMark Setting", "setting": {"name": "<PERSON>", "size": "MarK Size", "angle": "<PERSON>", "position": {"label": "<PERSON>", "lt": "Left Top", "rt": "Right Top", "lb": "Left Bottom", "rb": "Right Bottom", "full": "Full"}}}, "upload_background": "Carregar plano de fundo", "mouseMenu": {"layer": "Gestão de camadas", "copy": "Copiar", "delete": "Eliminar", "group": "combinación", "unGroup": "divida", "up": "up", "down": "down", "upTop": "Traer al frente", "downTop": "Enviar a volver", "center": "centro"}, "alert": {"loading_fonts": "<PERSON><PERSON><PERSON> font<PERSON>, aguarde...", "loading_fonts_failed": "<PERSON><PERSON><PERSON> ao carregar as fontes, tente novamente", "loading_fonts_success": "O tipo de letra foi carregado com sucesso！", "loading_data": "Carregando dados...", "select_image": "Por favor, selecione uma imagem de plano de fundo", "select_file": "Selecione um arquivo", "copied_sucessful": "A cópia foi bem sucedida", "fail_copy": "a cópia falhou"}, "fruits": "<PERSON><PERSON><PERSON> de desenhos animados", "sports": "Esportes", "seasons": "Outono", "eletronics": "Computador", "clothes": "<PERSON><PERSON><PERSON>", "flags": "Bandeiras", "threes": "<PERSON><PERSON><PERSON><PERSON>", "food": "<PERSON><PERSON><PERSON>", "medals": "<PERSON>has", "business": "<PERSON>eg<PERSON><PERSON><PERSON>", "activity": "Atividade", "vintage": "vintage", "animals": "animais", "hand_painted": "pintado à <PERSON>ão", "scenary_x": "<PERSON><PERSON> {number}", "color": "Cor", "red_book_vertical": "Red Book - V", "red_book_horizontal": "Red Book - H", "phone_wallpaper": "Phone Wallpaper", "attributes": {"id": "ID", "font": "Fonte", "align": "Alinhamento", "bold": "Negrito:", "italic": "Italico:", "underline": "Underline:", "stroke": "Stroke:", "swipe_up": "Swipe UP:", "line_height": "Line height", "char_spacing": "Espaço Char.", "exterior": "Exterior", "angle": "<PERSON><PERSON><PERSON>", "left": "Esq", "top": "Topo", "opacity": "Transparência", "shadow": "Sombra", "blur": "Blur", "offset_x": "X", "offset_y": "Y", "rx_ry": "Rounded", "picture_filter": "Filtro"}}