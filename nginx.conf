events {
  worker_connections  4096;  ## Default: 1024
}

http {
include /etc/nginx/mime.types;
client_max_body_size 100M;
sendfile on;
server {
  listen 80;
  server_name localhost;

  include /etc/nginx/mime.types;
  location / {
    root /usr/share/nginx/editor;
    try_files $uri $uri/ /index.html;
    index index.html index.htm;
  }
  location /api {
     proxy_pass http://************:1337/api;
     proxy_set_header Host $http_host;
     proxy_set_header Content-Length $content_length;
     proxy_set_header Content-Type $content_type;
  }
  location /uploads {
      proxy_pass http://************:1337/uploads;
      proxy_set_header Content-Length $content_length;
      proxy_set_header Content-Type $content_type;
  }
  location /admin/ {
       proxy_pass http://************:1338/;
       proxy_set_header Content-Length $content_length;
       proxy_set_header Content-Type $content_type;
  }

  error_page   500 502 503 504  /50x.html;
  location = /50x.html {
    root   /usr/share/nginx/html;
  }
}
}
