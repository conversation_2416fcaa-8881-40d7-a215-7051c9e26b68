{
  "compilerOptions": {
    "target": "esnext",
    "useDefineForClassFields": true,
    "allowSyntheticDefaultImports": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "allowJs": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "lib": [
      "esnext",
      "dom"
    ],
    "skipLibCheck": true,
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ]
    }
  },
  "files": [],
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "typings/**/*.ts",
    "src/hooks/useMaterial.js",
  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}