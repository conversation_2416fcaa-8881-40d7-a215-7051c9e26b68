import { defineConfig, loadEnv } from 'vite';
import dayjs from 'dayjs';
import vue from '@vitejs/plugin-vue';
import { createHtmlPlugin } from 'vite-plugin-html';
import vueJsx from '@vitejs/plugin-vue-jsx';
import vueSetupExtend from 'vite-plugin-vue-setup-extend-plus';
import autoImports from 'unplugin-auto-import/vite';
import { resolve } from 'path';
import autoprefixer from 'autoprefixer';

const config = ({ mode }) => {
  const isProd = mode === 'production'
  const isDev = mode === 'development'
  const baseUrl = {
    production: 'https://prod-env-1317082621.cos.ap-hongkong.myqcloud.com',
    test: 'https://test-env-1317082621.cos.ap-hongkong.myqcloud.com'
  }
  const envPrefix = 'APP_';
  const { APP_TITLE = '', APP_BASE_PATH } = loadEnv(mode, process.cwd(), envPrefix);

  // const SVR = "http://43.134.127.37:1337/";
  // 生产
  const SVR = 'http://43.135.69.70:8888/'
  const SVR2 = 'http://43.135.69.70:6120/pre/'
  // 测试
  // const SVR = 'http://43.133.23.224:1337/'
  // const SVR2 = 'http://43.133.23.224:6120/pre/'
  return {
    base: isDev ? '/' : baseUrl[mode],
    plugins: [
      vue(),
      autoImports({
        imports: ['vue'],
        dts: './typings/auto-imports.d.ts',
        eslintrc: {
          enabled: true,
        },
      }),
      vueSetupExtend(),
      vueJsx({
      }),
      createHtmlPlugin({
        minify: isProd,
        inject: {
          data: {
            title: APP_TITLE,
            BUILD_TIME: `版本构建时间：${dayjs().format('YYYY-MM-DD HH:mm:ss')}`
          },
        },
      }),
    ],
    build: {
      target: 'es2015',
      outDir: resolve(__dirname, 'dist'),
      assetsDir: 'assets',
      assetsInlineLimit: 8192,
      emptyOutDir: true,
      rollupOptions: {
        input: resolve(__dirname, 'index.html'),
        output: {
          chunkFileNames: 'js/[name].[hash].js',
          entryFileNames: 'js/[name].[hash].js',
        },
      },
    },
    envPrefix,
    resolve: {
      alias: [
        { find: /^@\//, replacement: resolve(__dirname, 'src') + '/' },
        { find: /^~/, replacement: '' },
        { find: /^vue-i18n/, replacement: 'vue-i18n/dist/vue-i18n.cjs.js' },
      ],
      extensions: ['.ts', '.tsx', '.js', '.mjs', '.vue', '.json', '.less', '.css'],
    },
    css: {
      postcss: {
        plugins: [
          autoprefixer({
            overrideBrowserslist: [
              'Android 4.1',
              'iOS 7.1',
              'Chrome > 31',
              'ff > 31',
              'ie >= 8',
              'last 2 versions',
            ],
          }),
        ],
      },
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          additionalData: `@import '${resolve(__dirname, 'src/styles/variable.less')}';`,
        },
      },
    },
    server: {
      host: '0.0.0.0',
      port: 3000,
      open: false,
      proxy: {
        '/api': {
          target: SVR,
          changeOrigin: true,
        },
        '/uploads': {
          target: SVR,
          changeOrigin: true,
        },
        '/v2': {
          target: SVR2,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/v2/, '')
        }
      },
    },
    preview: {
      port: 5000,
    },
  };
};

export default defineConfig(config);
