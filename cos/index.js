const glob = require('glob')
const COS = require('cos-nodejs-sdk-v5')
const path = require('path')

const BASE_URL = {
  test: 'test-env-1317082621',
  production: 'prod-env-1317082621'
}[process.argv[process.argv.length - 1]]

/**
 * 初始化cos
 * @returns {COS}
 */
function initCOS() {
  return new COS({
    SecretId: 'AKIDlRzKXXL6kCQRGsIWXMyPdfU4QYYLvITR',
    SecretKey: 'rVwAB0jbRAsX7cH5wfCWeeT5TipjwAE4'
  })
}

/**
 * 获取js文件列表
 */
function getJsFiles(cos) {
  return new Promise(resolve => {
    const files = glob.sync(path.resolve(__dirname, '../dist/js/*.js'))
    const uploadFiles = []
    for (const filePath of files) {
      uploadFiles.push(getGenerateRecord(filePath, 'js'))
    }
    let fileNum = 0
    cos.uploadFiles({
      files: uploadFiles,
      SliceSize: 1024 * 1024 * 10,
      onFileFinish: () => {
        fileNum++
        if (fileNum === uploadFiles.length) {
          resolve('js 上传完毕')
        }
      }
    })
  })
}

/**
 * 获取静态文件列表
 */
function getAssetsFiles(cos) {
  return new Promise(resolve => {
    const files = glob.sync(path.resolve(__dirname, '../dist/assets/*'))
    const uploadFiles = []
    for (const filePath of files) {
      uploadFiles.push(getGenerateRecord(filePath, 'assets'))
    }
    let fileNum = 0
    cos.uploadFiles({
      files: uploadFiles,
      SliceSize: 1024 * 1024 * 10,
      onFileFinish: () => {
        fileNum++
        if (fileNum === uploadFiles.length) {
          resolve('assets 上传完毕')
        }
      }
    })
  })
}

/**
 * 生成上传记录
 * @param FilePath
 * @param prefix
 * @returns {{FilePath, Bucket: string, Region: string, Key: string}}
 */
function getGenerateRecord(FilePath, prefix) {
  const Key = FilePath.split('/').pop()
  return {
    Bucket: BASE_URL,
    Region: 'ap-hongkong',
    Key: `${prefix}/${Key}`,
    FilePath,
  }
}

async function init() {
  const cos = initCOS()
  console.log('🚀🚀🚀---开始上传---🚀🚀🚀')
  await Promise.all([getJsFiles(cos), getAssetsFiles(cos)])
  console.log('🚀🚀🚀---上传完毕---🚀🚀🚀')
}

init()

