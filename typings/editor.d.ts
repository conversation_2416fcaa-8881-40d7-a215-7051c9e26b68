declare interface IPluginOption {
  [propName: string]: unknown | undefined;
}

declare type IEditorHooksType =
  | 'hookImportBefore'
  | 'hookImportAfter'
  | 'hookSaveBefore'
  | 'hookSaveAfter'
  | 'hookTransform';

declare interface IPluginClass extends IPluginTempl {
  new (canvas: fabric.Canvas, editor: IEditor, options?: IPluginOption);
}

declare interface IPluginMenu {
  text: string;
  command?: () => void;
  child?: IPluginMenu[];
}

declare class IPluginTempl {
  static pluginName: string;
  static events: string[];
  static apis: string[];
  canvas?: fabric.Canvas | null | undefined;
  hotkeyEvent?: (name: string, e: KeyboardEvent) => void;
  [propName: IEditorHooksType]: () => void;
  [propName: string]: any;
}
